# AnyTXN信用卡核心系统-参数应用服务 (AnyTXN Parameter Service)

本项目是 AnyTXN信用卡核心系统 的参数应用服务，负责处理统一参数管理、分发、缓存、权限控制等核心业务。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-参数应用服务 (AnyTXN Parameter Service)](#anytxn信用卡核心系统-参数应用服务-anytxn-parameter-service)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build \& Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring \& Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的参数应用服务是企业级微服务应用，在整个AnyTXN生态系统中负责全业务域参数的统一管理和分发，为卡片服务、账户服务、授权服务、交易服务等所有业务模块提供参数管理基础能力。该服务解决了多业务域参数统一管理、参数实时分发、多级缓存优化、参数权限控制、批量数据处理等关键业务问题。

主要功能包括：覆盖账户、授权、卡片、分期、会计、清算等业务域的87+个核心服务、支持58+个参数查询API的远程调用客户端、多级缓存机制（JetCache本地缓存+Redis分布式缓存）、基于Nacos的配置监听和自动刷新、支持分库分表的ShardingSphere集成、Excel和CSV数据导入导出功能、基于AnyScheduler的批量处理任务，支持高并发访问和企业级数据管理需求。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于API接口和批量作业分析，anytxn-parameter工程提供以下六大核心功能模块：

#### 1.1.1 账户参数管理功能 (Account Parameter Management)

**核心API接口：**
- `GET /param/interest/pageNum/{pageNum}/pageSize/{pageSize}` - 利率参数分页查询接口，支持利率参数的分页查询
- `POST /param/minimumPaymentPercent` - 最低还款比例参数管理接口，管理最低还款比例配置
- `POST /param/statementProcess` - 账单处理参数管理接口，配置账单处理规则
- `POST /param/autoPaymentTable` - 约定扣款参数管理接口，管理自动扣款配置

**主要服务类：**
- `IInterestService` - 利率参数服务，管理各类利率参数配置和查询
- `IMinimumPaymentPercentService` - 最低还款比例服务，处理最低还款比例参数管理
- `IStatementProcessService` - 账单处理服务，管理账单处理参数配置
- `IAutoPaymentTableService` - 约定扣款服务，处理自动扣款参数管理
- `IDelinquentControlDefineService` - 延滞控制定义服务，管理延滞控制参数

**批量作业：**
- `accountParameterSyncJob` - 账户参数同步作业，批量同步账户相关参数
- `interestParameterUpdateJob` - 利率参数更新作业，批量更新利率参数配置
- `statementParameterValidationJob` - 账单参数验证作业，验证账单参数配置有效性

#### 1.1.2 授权参数管理功能 (Authorization Parameter Management)

**核心API接口：**
- `POST /param/authorisationProcessing` - 授权处理参数管理接口，配置授权处理规则
- `POST /param/authCheckControl` - 授权检查控制参数接口，管理授权检查控制规则
- `POST /param/authCheckDefinition` - 授权检查定义参数接口，定义授权检查规则
- `POST /param/authLimitUse` - 授权类型与额度检查接口，管理授权额度检查

**主要服务类：**
- `IAuthorisationProcessingService` - 授权处理参数服务，处理授权处理参数管理
- `AuthorisationProcessingServiceImpl` - 授权处理参数服务实现，提供授权处理核心逻辑
- `IAuthCheckControlService` - 授权检查控制服务，管理授权检查控制参数
- `IAuthCheckDefinitionService` - 授权检查定义服务，处理授权检查定义参数
- `IParmTransFeeService` - 交易费用参数服务，管理交易费用相关参数

**批量作业：**
- `authorizationParameterSyncJob` - 授权参数同步作业，批量同步授权相关参数
- `authCheckParameterUpdateJob` - 授权检查参数更新作业，批量更新授权检查参数
- `authLimitParameterValidationJob` - 授权额度参数验证作业，验证授权额度参数有效性

#### 1.1.3 卡片参数管理功能 (Card Parameter Management)

**核心API接口：**
- `POST /param/cardBin` - 卡BIN参数管理接口，管理卡BIN定义和控制参数
- `POST /param/cardFace` - 卡版参数管理接口，管理卡片版面参数配置
- `POST /param/cardProductInfo` - 卡产品参数管理接口，管理卡产品信息参数
- `POST /param/cardIssue` - 发卡参数管理接口，配置发卡相关参数

**主要服务类：**
- `ICardBinDefinitionService` - 卡BIN定义服务，处理卡BIN参数管理
- `CardBinDefinitionServiceImpl` - 卡BIN定义服务实现，提供卡BIN管理核心逻辑
- `CardFaceServiceImpl` - 卡版服务实现，处理卡版参数管理
- `ICardProductInfoService` - 卡产品信息服务，管理卡产品参数
- `ParamTokenActivationMethodImpl` - Token激活方法参数服务，管理Token激活参数

**批量作业：**
- `cardParameterSyncJob` - 卡片参数同步作业，批量同步卡片相关参数
- `cardBinParameterUpdateJob` - 卡BIN参数更新作业，批量更新卡BIN参数
- `cardProductParameterValidationJob` - 卡产品参数验证作业，验证卡产品参数有效性

#### 1.1.4 公共参数管理功能 (Common Parameter Management)

**核心API接口：**
- `POST /param/organizationInfo` - 机构信息参数管理接口，管理机构基础信息参数
- `POST /param/transactionCode` - 交易码参数管理接口，管理交易码定义参数
- `POST /param/transactionType` - 交易类型参数管理接口，管理交易类型配置
- `POST /param/addTransactionCtrlUnit` - 交易管控单元参数接口，管理交易管控单元

**主要服务类：**
- `IOrganizationInfoService` - 机构信息服务，管理机构信息参数配置
- `ITransactionCodeService` - 交易码服务，处理交易码参数管理
- `ITxnTypeControlService` - 交易类型控制服务，管理交易类型控制参数
- `ITransactionCtrlUnitService` - 交易管控单元服务，处理交易管控单元参数
- `IParmSysClassService` - 系统分类参数服务，管理系统分类参数

**批量作业：**
- `businessDateCutOverJob` - 业务日期日切作业，执行机构日期切换处理
- `businessDateCutOverEndJob` - 业务日期日切结束作业，完成日切后续处理
- `organizationParameterSyncJob` - 机构参数同步作业，批量同步机构参数

#### 1.1.5 多级缓存管理功能 (Multi-Level Cache Management)

**核心API接口：**
- `GET /param/cache/clear` - 缓存清理接口，清理所有参数缓存数据
- `POST /param/cache/refresh` - 缓存刷新接口，刷新指定参数缓存
- `GET /param/cache/status` - 缓存状态查询接口，查询缓存运行状态

**主要服务类：**
- `ParamRefreshHandle` - 参数缓存刷新处理器，统一管理参数缓存刷新
- `IParamPublishService` - 参数发布服务，处理参数变更通知和缓存刷新
- `ParamCacheController` - 参数缓存控制器，提供缓存管理REST接口
- `CacheInvokeConfig` - 缓存调用配置，管理JetCache缓存配置
- `GlobalCacheConfig` - 全局缓存配置，统一管理多级缓存策略

**批量作业：**
- `cacheWarmupJob` - 缓存预热作业，预加载热点参数数据到缓存
- `cacheEvictionJob` - 缓存淘汰作业，清理过期和低频访问的缓存数据
- `cacheConsistencyCheckJob` - 缓存一致性检查作业，保证缓存与数据库数据一致性

#### 1.1.6 数据导入导出功能 (Data Import/Export Management)

**核心API接口：**
- `POST /param/import/excel` - Excel数据导入接口，支持Excel格式参数数据导入
- `POST /param/import/csv` - CSV数据导入接口，支持CSV格式参数数据导入
- `GET /param/export/excel` - Excel数据导出接口，导出参数数据为Excel格式
- `GET /param/export/template` - 导入模板下载接口，下载参数导入模板

**主要服务类：**
- `IParameterPoiService` - 参数POI服务，处理Excel和CSV数据导入导出
- `ParameterPoiServiceImpl` - 参数POI服务实现，提供数据导入导出核心逻辑
- `AbstractParameterService` - 抽象参数服务，提供参数审核和数据库操作基础功能
- `InstallProductInfoServiceImpl` - 分期产品信息服务，支持分期参数数据导入导出
- `ParamImportResultDTO` - 参数导入结果数据传输对象，封装导入结果信息

**批量作业：**
- `parameterDataImportJob` - 参数数据导入作业，批量处理参数数据导入
- `parameterDataExportJob` - 参数数据导出作业，批量导出参数数据
- `parameterDataValidationJob` - 参数数据验证作业，验证导入数据的有效性
- `parameterTemplateGenerationJob` - 参数模板生成作业，生成各类参数导入模板

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：MySQL 8.3.0, Redis（缓存支持）

**中间件**：Nacos（服务发现与配置中心）, ShardingSphere 5.5.0（分库分表）

**构建工具**：Maven 3.5+

**JDK版本要求**：Java 17

**其他核心组件**：
- MyBatis 3.5.16（数据持久化）
- MyBatis Generator（代码自动生成）
- Undertow（Web服务器）
- JetCache 2.5.16（多级缓存）
- Apache POI（Excel处理）
- Apache Commons CSV（CSV处理）
- Jasypt 3.0.5（配置加解密）
- Knife4j 4.5.0（API文档生成）
- AnyScheduler（批处理调度）
- Plumelog 3.1.0-SNAPSHOT（分布式日志）
- Mockito（测试框架）

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**Docker**：Docker & Docker Compose

**中间件要求**：
- Nacos Server
- MySQL 8.3.0+
- Redis 6.2+

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-parameter.git
cd anytxn-parameter
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：配置 IntelliJ IDEA 启动参数**

在 IntelliJ IDEA 中配置应用启动的程序参数：

1. **打开运行配置**
   - 在 IDEA 中找到主启动类（如 `ParameterServerApplication.java`）
   - 右键点击该文件，选择 `Run` 或 `Debug`
   - 或者点击 IDEA 顶部的运行配置下拉菜单

2. **编辑运行配置**
   - 点击运行配置旁的编辑按钮（齿轮图标）
   - 或者选择 `Run` → `Edit Configurations...`

3. **配置程序参数**
   - 在 `Program arguments` 字段中输入以下参数：
   ```
   --NACOS_USERNAME=nacos --NACOS_PASSWORD=nacos --CLUSTER_NAME=A --NACOS_SERVER_ADDR=**********:8848,**********:8848,**********:8848 --NACOS_NAMESPACE=2cff4a1a-e0f9-42e4-8472-a7b0a17a1235
   ```
   
   > **注意**：请根据实际环境情况调整以下参数值：
   > - `NACOS_USERNAME`: Nacos 用户名
   > - `NACOS_PASSWORD`: Nacos 密码  
   > - `CLUSTER_NAME`: 集群名称
   > - `NACOS_SERVER_ADDR`: Nacos 服务器地址列表
   > - `NACOS_NAMESPACE`: Nacos 命名空间ID

4. **保存配置**
   - 点击 `Apply` 和 `OK` 保存配置

**第五步：运行应用**

我们推荐在 IntelliJ IDEA 中直接运行 `ParameterServerApplication.java`。

或者通过命令行启动，指定 `dev` 环境配置：

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

服务启动后，你可以访问健康检查端点来验证服务状态。

## 4. 项目结构 (Project Structure)

**多模块结构说明**：
项目采用Maven多模块架构，按照业务功能和技术层次进行模块划分，实现清晰的依赖关系和职责分离。

**各模块职责划分**：
基础定义模块提供API接口和DTO定义，业务实现模块包含核心逻辑，客户端模块提供远程调用，批量处理模块处理大批量数据。

**包结构说明**：
各模块遵循标准包命名规范，基础包名为com.anytech.anytxn.parameter，按业务域进行二级分包。

**关键目录介绍**：

```
.
├── anytxn-parameter-base                # 基础定义模块 (可被其他服务依赖)
│   └── com.anytech.anytxn.parameter.base
│       ├── dto/                         # 数据传输对象
│       ├── enums/                       # 枚举定义
│       ├── exception/                   # 异常类
│       └── service/                     # 基础服务接口
├── anytxn-parameter-sdk                 # 业务实现模块 (核心业务逻辑)
│   └── com.anytech.anytxn.parameter
│       ├── account/                     # 账户参数管理 (29个服务)
│       ├── authorization/               # 授权参数管理 (14个服务)
│       ├── card/                        # 卡片参数管理 (12个服务)
│       ├── installment/                 # 分期参数管理 (7个服务)
│       ├── accounting/                  # 会计参数管理 (6个服务)
│       ├── common/                      # 公共参数管理 (13个服务)
│       └── config/                      # 配置类
├── anytxn-parameter-client              # 客户端模块 (远程调用)
│   └── com.anytech.anytxn.parameter.client
│       └── feign/                       # Feign客户端接口 (58+个API)
├── anytxn-parameter-batch               # 批量处理模块 (定时任务)
│   └── com.anytech.anytxn.parameter.batch
│       ├── job/                         # 批处理作业
│       └── config/                      # 批处理配置
├── doc/                                 # 项目文档
├── code-update-record/                  # 代码变更记录
└── pom.xml                              # 父 POM
```

- `anytxn-parameter-base`: 定义了对外暴露的DTO、枚举和基础服务接口，可以被其他微服务依赖，避免了代码重复。
- `anytxn-parameter-sdk`: 包含所有业务逻辑、Service等，是核心业务实现模块，支持87+个参数管理服务。
- `anytxn-parameter-client`: 提供Feign客户端接口，支持58+个参数查询API的远程调用。
- `anytxn-parameter-batch`: 批量处理模块，包含基于AnyScheduler的定时任务和批量数据处理作业。

## 5. 配置 (Configuration)

本项目使用 Nacos 作为配置中心。核心配置文件在 Nacos 中，`group` 为 `DEFAULT_GROUP`，`Data ID` 为 `parameter-batch.yaml`。

本地开发时，会优先加载 `src/main/resources/bootstrap.yml` 中的配置。

**关键环境变量/配置项说明:**
| 变量名/属性名 | 示例值 | 描述 |
|---------------|--------|------|
| `server-addr` | `172.16.70.20:8848` | Nacos 服务器地址 |
| `namespace` | `dev-namespace` | Nacos 命名空间 |
| `SPRING_CLOUD_NACOS_CONFIG_PREFIX` | `parameter-batch` | Nacos配置前缀 |
| `SPRING_CLOUD_NACOS_CONFIG_FILE_EXTENSION` | `yaml` | 配置文件扩展名 |
| `ANYTXN_DATASOURCE_COMMON_JDBC_URL` | `********************************************` | 数据库连接地址 |
| `ANYTXN_DATASOURCE_COMMON_USERNAME` | `test_common_rd` | 数据库用户名 |

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建
mvn clean install

# 跳过测试构建
mvn clean install -DskipTests

# 单模块构建
mvn clean install -pl anytxn-parameter-sdk -am
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包
4. MyBatis Generator自动生成代码
5. Docker镜像构建
6. 推送至Harbor镜像仓库
7. Kubernetes集群部署

**Docker部署方式**：
```bash
# 构建Docker镜像
mvn clean package docker:build

# 运行Docker容器
docker run -p 8080:8080 -e SPRING_PROFILES_ACTIVE=dev anytxn-parameter-batch:1.0.2-SNAPSHOT
```

**环境变量配置**：
- `SPRING_PROFILES_ACTIVE`: 指定运行环境（dev/sit/prod）
- `server-addr`: Nacos服务地址
- `namespace`: Nacos命名空间
- `ANYTXN_DATASOURCE_COMMON_JDBC_URL`: 数据库连接地址

## 7. 监控与告警 (Monitoring & Alerting)

- **健康检查**: Spring Boot Actuator 提供了健康检查端点。
  - `http://localhost:8080/actuator/health`
- **Metrics**: 指标已接入 Prometheus。端点地址：
  - `http://localhost:8080/actuator/prometheus`
- **日志**: 应用日志格式为 JSON，统一由 Plumelog 收集并发送到 Elasticsearch。你可以在 Kibana 中查看。
  - **Kibana Dashboard**: [链接到 Kibana 仪表盘](https://kibana.jrx.com)