package com.anytech.anytxn.installment.service.interest;

import com.anytech.anytxn.installment.base.domain.dto.InstallmentInterestDTO;
import com.anytech.anytxn.installment.base.service.IInterest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: sukang
 * @Date: 2022/11/10 18:02
 */
public class Rule78Interest implements IInterest {


    @Override
    public InstallmentInterestDTO getInterestResult(InstallmentInterestDTO interestDTO) {

        List<BigDecimal> arrayList = new ArrayList<>(interestDTO.getTerm());

        int sum = sum(interestDTO.getTerm());


        for (int i = 0; i < interestDTO.getTerm() ; i++) {
            int j = interestDTO.getTerm() - i;

            BigDecimal divide = interestDTO.getInterestAmount()
                    .multiply(new BigDecimal(String.valueOf(j)))
                    .divide(new BigDecimal(String.valueOf(sum)), 2, BigDecimal.ROUND_HALF_UP);

            arrayList.add(divide);
        }

        arrayList.set(interestDTO.getTerm() - 1, BigDecimal.ZERO);

        BigDecimal subtract = interestDTO.getInterestAmount().subtract(
                arrayList.stream().reduce(BigDecimal.ZERO, BigDecimal::add)
        );

        arrayList.set(interestDTO.getTerm() - 1, subtract);

        interestDTO.setMonthlyInterestAmount(arrayList);

        return interestDTO;
    }



    private int sum(int term){

        if (term == 0) {
            return 0;

        }
        return term + sum(term - 1);
    }







    @Override
    public String getInterestCode() {
        return null;
    }
}
