package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallPlanMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallPlanSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.dao.installment.model.InstallPlan;
import com.anytech.anytxn.business.dao.installment.model.InstallPlanKey;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallPlanDTO;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.enums.InstallRepDetailEnum;
import com.anytech.anytxn.installment.base.service.IInstallPlanService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.List;

/**
 * 分期计划表
 *
 * <AUTHOR>
 * @date 2019/7/8
*/
@Service
public class InstallPlanServiceImpl implements IInstallPlanService {

    @Autowired
    private InstallPlanMapper installPlanMapper;

    @Autowired
    private InstallOrderMapper installOrderMapper;
    @Autowired
    private InstallPlanSelfMapper installPlanSelfMapper;

    private Logger logger = LoggerFactory.getLogger(InstallPlanServiceImpl.class);

    /**
     * 根据 主键 查询分期计划参数的详细信息
     *
     * @param orderId
     * @param term
     * @return InstallPlanDTO
     */
    @Override
    public InstallPlanDTO findByIdAndTerm(String orderId, Integer term) {
        //参数校验
        if (orderId == null) {
            logger.error("分期订单号orderId不能为空");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.OR_NO_E);
        }
        if (term == null) {
            logger.error("分期期数term不能为空");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.TE_E);
        }
        InstallPlanKey installPlanKey = new InstallPlanKey();
        installPlanKey.setOrderId(orderId);
        installPlanKey.setTerm(term);
        InstallPlan installPlan = installPlanMapper.selectByPrimaryKey(installPlanKey);
        if (installPlan == null) {
            logger.error("根据orderId查询分期计划参数信息，未查询到任何数据");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PLAN_NOT_EXIST_FAULT);
        }
        //查询分期订单表获得所需数据
        InstallOrder installOrder = installOrderMapper.selectByPrimaryKey(orderId);
        //查询结果判断
        if (installOrder == null) {
            logger.error("根据条件查询分期订单参数，未查询到任何数据");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PRO_PARAM_NOT_EXIST_FAULT);
        }
        InstallPlanDTO installPlanDTO = BeanMapping.copy(installPlan, InstallPlanDTO.class);
        installPlanDTO.setOrderId(installOrder.getOrderId());
        installPlanDTO.setAccountManagementId(installOrder.getAccountManagementId());
        installPlanDTO.setCardNumber(installOrder.getCardNumber());
        installPlanDTO.setProductCode(installOrder.getProductCode());
        installPlanDTO.setTransactionDate(installOrder.getTransactionDate().toLocalDate());
        installPlanDTO.setAcquireReferenceNo(installOrder.getAcquireReferenceNo());
        return installPlanDTO;
    }

    /**
     * 根据条件查询分期计划列表
     *
     * @param orderId
     * @return List<InstallPlanDTO>
     */
    @Override
    public List<InstallPlanDTO> planByOrderId(String orderId) {
        //参数校验
        if (orderId == null) {
            logger.error("主键id不能为空");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.PR_E);
        }
        //先查询分期订单表获得所需数据
        InstallOrder installOrder = installOrderMapper.selectByPrimaryKey(orderId);
        //查询结果判断
        if (installOrder == null) {
            logger.error("根据条件查询分期订单参数，未查询到任何数据");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PRO_PARAM_NOT_EXIST_FAULT);
        }
        //根据分期订单号查询对应分期计划
        List<InstallPlan> installPlans = installPlanMapper.selectByOrderId(orderId);
        //查询结果判断
        if (installPlans.isEmpty()) {
            logger.error("根据条件查询分期计划参数，未查询到任何数据");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PLAN_NOT_EXIST_FAULT);
        }
        List<InstallPlanDTO> installPlanDtos = BeanMapping.copyList(installPlans, InstallPlanDTO.class);
        for(InstallPlanDTO installPlanDTO : installPlanDtos){
            installPlanDTO.setOrderId(installOrder.getOrderId());
            installPlanDTO.setAccountManagementId(installOrder.getAccountManagementId());
            installPlanDTO.setCardNumber(installOrder.getCardNumber());
            installPlanDTO.setProductCode(installOrder.getProductCode());
            installPlanDTO.setTransactionDate(installOrder.getTransactionDate().toLocalDate());
            installPlanDTO.setAcquireReferenceNo(installOrder.getAcquireReferenceNo());
        }
        return installPlanDtos;
    }
    /**
     * 批量插入计划表
     *
     * @param installPlansDtos
     * @return int
     */
    @Override
    public int insertInstallPlanBatch(List<InstallPlanDTO> installPlansDtos) {
        List<InstallPlan> installPlans = BeanMapping.copyList(installPlansDtos, InstallPlan.class);
        return installPlanSelfMapper.insertInstallPlanBatch(installPlans);
    }

}
