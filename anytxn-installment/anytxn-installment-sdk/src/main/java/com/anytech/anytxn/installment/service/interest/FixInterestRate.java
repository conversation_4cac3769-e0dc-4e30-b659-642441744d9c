package com.anytech.anytxn.installment.service.interest;

import com.anytech.anytxn.installment.base.domain.dto.InstallmentInterestDTO;
import com.anytech.anytxn.installment.base.enums.ChargeOptionEnum;
import com.anytech.anytxn.installment.base.service.IInterest;

import java.math.BigDecimal;

/**
 * @Author: sukang
 * @Date: 2022/11/10 15:41
 */
public class FixInterestRate implements IInterest {



    @Override
    public InstallmentInterestDTO getInterestResult(InstallmentInterestDTO interestDTO) {

        //分期金额*年化利率*分期期数/1200

        BigDecimal interestAmount = interestDTO.getCostAmount()
                .multiply(interestDTO.getAnnualInterestRate())
                .multiply(new BigDecimal(interestDTO.getTerm()))
                .divide(new BigDecimal("1200"), 2, BigDecimal.ROUND_HALF_UP);

        interestDTO.setInterestAmount(interestAmount);

        return interestDTO;
    }







    @Override
    public String getInterestCode() {
        return ChargeOptionEnum.FLAT_RATE.getCode();
    }


}
