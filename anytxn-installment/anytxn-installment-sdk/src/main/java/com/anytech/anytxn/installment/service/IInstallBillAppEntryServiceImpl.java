package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import com.anytech.anytxn.business.dao.limit.model.LimitCustCreditInfo;
import com.anytech.anytxn.business.dao.transaction.model.PostedTransaction;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.account.domain.dto.AccountStatementInfoDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatementInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionSelfMapper;
import com.anytech.anytxn.installment.base.domain.dto.*;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.service.IInstallBillAppEntryService;
import com.anytech.anytxn.installment.base.service.IInstallBillEntryAppService;
import com.anytech.anytxn.installment.service.manager.InstallmentSmsManager;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardProductInfoSelfMapper;
//import com.anytech.anytxn.sms.exception.AnyTxnSmsException;
//import com.anytech.anytxn.sms.exception.AnyTxnSmsRespCode;
//import com.anytech.anytxn.sms.exception.SmsRepDetail;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import com.anytech.anytxn.business.dao.limit.mapper.LimitCustCreditInfoMapper;

@Service
public class IInstallBillAppEntryServiceImpl implements IInstallBillAppEntryService {

    private Logger logger = LoggerFactory.getLogger(IInstallBillAppEntryServiceImpl.class);

    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;

    @Autowired
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;

    @Autowired
    ParmCardProductInfoSelfMapper cardProductInfoSelfMapper;

    @Autowired
    private PostedTransactionSelfMapper postedTransactionSelfMapper;

    @Autowired
    private  LimitCustCreditInfoMapper LimitCustCreditInfoMapper;

    @Autowired
    private InstallOrderMapper installOrderMapper;

    @Autowired
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;

    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;

    @Autowired
    private IInstallBillEntryAppService installBillEntryAppService;

    @Autowired
    private AccountStatementInfoSelfMapper accountStatementInfoSelfMapper;

    @Autowired
    private InstallmentSmsManager installmentSmsManager;
    @Override
    public InstallTradingAppDTO findBillInstallApp(String cardNumber) {
        logger.info("APP----分期账单查询请求参数---cardNumber:{}",cardNumber);
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoSelfMapper.selectByCardNumber(cardNumber);
        if (null == cardAuthorizationInfo){
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_CARD_AUTH_INFO_NOT_EXIST_FAULT);
        }
        AccountManagementInfo accountManagementInfo = accountManagementInfoSelfMapper.selectByCusIdProNumAndOrg(cardAuthorizationInfo.getPrimaryCustomerId(),cardAuthorizationInfo.getProductNumber(),OrgNumberUtils.getOrg());
        if (null == accountManagementInfo){
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ACCOUNT_MANAGEMENT_RECORDED_NOT_EXIST_FAULT);
        }
        InstallTradingAppDTO installTradingAppDTO = new InstallTradingAppDTO();
        String currency = accountManagementInfo.getCurrency();
        List<InstallTradingDTO> installTradingDTOS = installBillEntryAppService.findBillInstall( installTradingAppDTO,accountManagementInfo.getAccountManagementId(),cardNumber,accountManagementInfo.getStatementDueAmount());
        //app-账单分期返回参赋值
        if (!CollectionUtils.isEmpty(installTradingDTOS)){
            dataFillingInstalment(installTradingAppDTO,accountManagementInfo.getAccountManagementId());
            //app可分期金额=获取管理账户上的账单剩余还款金额为真正可分期金额-app分期类型上不可以做的分期的金额
            installTradingAppDTO.setInstallAmount(installTradingDTOS.get(0).getInstallAmount());
            installTradingAppDTO.setCurrency(StringUtils.isEmpty(currency) ?"702":currency);
            installTradingAppDTO.setInstallTradingDTO(installTradingDTOS);
        }
        logger.info("APP----账单分期返回数据集installTradingAppDTO:{}",installTradingAppDTO);
        return installTradingAppDTO;
    }

    /**
     * 1:账单分期 如果有利息FE006 拒绝分期
     * 2:账单分期 如果持卡人未完成最低还款额，那拒绝账单分期申请
     * @param installTradingAppDTO
     */
    public InstallTradingAppDTO dataFillingInstalment(InstallTradingAppDTO installTradingAppDTO,String accountManagementId) {
        AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(accountManagementId);
        List<PostedTransaction> postedTransactions = postedTransactionSelfMapper.selectByOrgAndPostingDateAndAcctMid(OrgNumberUtils.getOrg(), accountManagementInfo.getLastStatementDate(), accountManagementId);
        List<PostedTransaction> postedInterestTransactionList = postedTransactions.stream().filter(t -> org.apache.commons.lang3.StringUtils.equalsAny(t.getPostingTransactionCode(),"FE006","FE005")).collect(Collectors.toList());
        //1 账单里里有FE006,FE005交易码的交易不进行账单分期
        if (!CollectionUtils.isEmpty(postedInterestTransactionList)){
            installTradingAppDTO.setFe006Flag(true);
        }
        BigDecimal totalDueAmount = accountManagementInfo.getTotalDueAmount();//最小还款额
        //账单分期完成最低还款额才能进行账单分期
        installTradingAppDTO.setTotalDueAmount(totalDueAmount);
        List<AccountStatementInfoDTO> statementInfos = this.findLastedStatementInfo(accountManagementId);
        //获取账单金额
        if (!CollectionUtils.isEmpty(statementInfos)){
            installTradingAppDTO.setBillAmount(statementInfos.get(0).getCloseBalance());
        }
        //账户，活跃的卡是否有封锁码
        installTradingAppDTO.setAccountBlockCode(accountManagementInfo.getBlockCode());
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoSelfMapper.
                selectBasicCardInfoByProNumAndCusId(OrgNumberUtils.getOrg(), accountManagementInfo.getProductNumber(), accountManagementInfo.getCustomerId());
        if (null != cardAuthorizationInfo){
            installTradingAppDTO.setCardBlockCode(cardAuthorizationInfo == null?null:cardAuthorizationInfo.getBlockCode());
            //D-lite卡
            installTradingAppDTO.setpCardProductNumber(cardAuthorizationInfo==null?null:cardAuthorizationInfo.getProductNumber());
            // todo 方法不存在临时调整
            LimitCustCreditInfo limitCustCreditInfo = null;//LimitCustCreditInfoMapper.selectByCdOrgAndLimitTypeCodeAndProduct(cardAuthorizationInfo.getPrimaryCustomerId(),
//                    OrgNumberUtils.getOrg(), LimitTypeEnum.SC01.getCode(), accountManagementInfo.getProductNumber());
            installTradingAppDTO.setCreditLimitAmount(limitCustCreditInfo==null?null:limitCustCreditInfo.getFixLimitAmount());
        }
        // 持卡人手机号
        CustomerAuthorizationInfo customerAuthorizationInfo = customerAuthorizationInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(), cardAuthorizationInfo.getPrimaryCustomerId());
        installTradingAppDTO.setPhoneNumber(customerAuthorizationInfo ==null?null:customerAuthorizationInfo.getMobilePhone());
        return installTradingAppDTO;
    }
    @Override
    public InstallEntryAppResDTO billInInstallmentApp(InstallEntryAppDTO installEntryAppDTO) {
        logger.info("APP-账单分期录入请求参数installEntryAppDTO-----:{}", installEntryAppDTO);
        InstallEntryAppResDTO result = null;
        try {
            if (installmentSmsManager.verifyOtpToEai(installEntryAppDTO.getCardNumber(), installEntryAppDTO.getOpt())) {
                logger.info("短信验证成功---账单进行分期------------");
            } else {
                logger.info("账单分期进行分期短信校验失败!");
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.SMS_SEND_FAILED);
            }
        } catch (Exception e) {
            logger.info("账单分期进行分期短信校验失败!");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.SMS_SEND_FAILED);
        }
        try {
            InstallEntryDTO installEntryDTO = BeanMapping.copy(installEntryAppDTO, InstallEntryDTO.class);
            AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByAccountManagementIdAndOrganizationNumber(installEntryAppDTO.getAccountManagementId(), OrgNumberUtils.getOrg());
            installEntryDTO.setStatementDate(accountManagementInfo == null ? null : accountManagementInfo.getLastStatementDate());
            InstallEntryResDTO installEntryResDTO = installBillEntryAppService.billInInstallment(installEntryDTO);
            result = BeanMapping.copy(installEntryResDTO, InstallEntryAppResDTO.class);
            String orderId = installEntryResDTO.getOrderId();
            InstallOrder installOrder = installOrderMapper.selectByPrimaryKey(orderId);
            BigDecimal installmentAmount = installOrder.getInstallmentAmount();
            LocalDateTime applyTime = installOrder.getCreateTime();
            result.setApplyDateTime(applyTime);
            result.setInstallmentAmount(installmentAmount);
        } catch (Exception e) {
            logger.error("exception", e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ENTRY_FAIL_FAULT);
        }
        logger.info("APP-账单分期录入返回数据集-----:{}", result);
        return result;
    }
    private List<AccountStatementInfoDTO> findLastedStatementInfo(String accountManageInfoId) {
        try {
            List<AccountStatementInfo> infos = this.accountStatementInfoSelfMapper.selectLastedStatementInfoByAccountManagementIdAndDate(accountManageInfoId);

            if (org.apache.commons.collections4.CollectionUtils.isEmpty(infos)){
                return Collections.emptyList();
            }

            return BeanMapping.copyList(infos, AccountStatementInfoDTO.class);
        } catch (Exception e) {
            logger.error("accountManageInfoId is {} select data error ", accountManageInfoId, e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_SELECT_DATABASE_FAULT);
        }
    }
}
