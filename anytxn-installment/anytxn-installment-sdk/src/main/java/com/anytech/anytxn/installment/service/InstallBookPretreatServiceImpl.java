package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.business.base.installment.enums.InstallmentTypeEnum;
import com.anytech.anytxn.business.base.monetary.annotation.BatchSharedAnnotation;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import com.anytech.anytxn.common.core.enums.RelationshipIndicatorEnum;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.authorization.domain.dto.OutstandingTransactionDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerBasicInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerBasicInfo;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.installment.base.domain.bo.AdjustInstallTermBo;
import com.anytech.anytxn.installment.base.constants.InstallmentConstant;
import com.anytech.anytxn.installment.base.domain.dto.InstallParameterDTO;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.enums.DebitCreditIndEnum;
import com.anytech.anytxn.installment.base.enums.InstallPriceFlagEnum;
import com.anytech.anytxn.installment.base.enums.InstallmentDerateMethodEnum;
import com.anytech.anytxn.installment.base.enums.InstallmentFunctionCodeEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.enums.InstallRepDetailEnum;
import com.anytech.anytxn.installment.base.service.IInstallBookPretreatService;
import com.anytech.anytxn.installment.base.service.IInstallOrderManagementService;
import com.anytech.anytxn.installment.base.service.IInstallOrderService;
import com.anytech.anytxn.installment.base.utils.InstallmentThreadLocalHolder;
import com.anytech.anytxn.installment.base.utils.InstallmentCreateFileUtil;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallAccountingTransParmResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallTypeParmResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallAccountingTransParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallTypeParmService;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @date 2019-06-13 16:14
 * 分期入账预处理
 **/
@Service
public class InstallBookPretreatServiceImpl implements IInstallBookPretreatService {
    private static final Logger logger = LoggerFactory.getLogger(InstallBookPretreatServiceImpl.class);
    @Autowired
    private IInstallOrderService installOrderService;
    @Autowired
    private IInstallOrderManagementService installmentOrderManagement;
    @Autowired
    private InstallmentCreateFileUtil installmentCreateFileUtil;
    @Autowired
    private IInstallAccountingTransParmService installAccountingTransParmService;
    @Autowired
    private IInstallProductInfoService installProductInfoService;
    @Autowired
    private IInstallTypeParmService installTypeParmService;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private ITransactionCodeService transactionCodeService;
    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Autowired
    private InstallmentThreadLocalHolder installmentThreadLocalHolder;

    @Autowired
    private CustomerBasicInfoSelfMapper customerBasicInfoSelfMapper;
    @Autowired
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;

    /**
     * 分期入账预处理
     *
     * @param outstandingTransaction
     * @return int
     **/
    @Override
    public String installBookPretreat(OutstandingTransactionDTO outstandingTransaction) {
        return installBookPretreat(outstandingTransaction,null);
    }

    /**
     * 分期入账预处理
     *
     * @param outstandingTransaction
     * @return int
     **/
    @BatchSharedAnnotation
    @Override
    public String installBookPretreat(OutstandingTransactionDTO outstandingTransaction,CustReconciliationControlDTO controlDTO) {
        //必输项
        Map hashMap = passCheck(outstandingTransaction);
        //获取交易借贷标识
        String debitCreditIndicator = (String) hashMap.get(InstallmentConstant.DEBIT_CREDIT_INDICATOR);
        //未并账表赋值（分期订单类型、编号、额度处理标识）
        setInstallToOutstandingTransaction(outstandingTransaction,hashMap);
        //分期订单创建
        if (Objects.equals(DebitCreditIndEnum.DEBIT_INDICATOR.getCode(), debitCreditIndicator)) {
            return insertInstallOrder(outstandingTransaction, hashMap,controlDTO);
        } else if (Objects.equals(DebitCreditIndEnum.CREDIT_INDICATOR.getCode(), debitCreditIndicator)) {
            //分期订单管理
            return installManage(outstandingTransaction, hashMap);
        } else {
            logger.error("分期预处理 只处理借贷标识D-借记,C-贷记 debitCreditIndicator={}", debitCreditIndicator);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_DEBIT_CREDIT_IND_FAULT);
        }
    }


    /**
     * 分期调整费用用
     *
     * @param installOrderDto
     * @return int
     **/
    @Override
    public AdjustInstallTermBo orderAdjustTerm(InstallOrderDTO installOrderDto) {
        String organizationNumber = installOrderDto.getOrganizationNumber();
        //分期产品号获取
        InstallParameterDTO installParameter = new InstallParameterDTO();
        //分期产品
        InstallProductInfoResDTO installProInfo= installProductInfoService.findByIndex(organizationNumber
                , installOrderDto.getProductCode());
        if (installProInfo == null) {
            logger.error("查询分期产品参数表失败 installProInfo={}", installProInfo);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PRO_PARAM_NOT_EXIST_FAULT);
        }
        installParameter.setInstallProInfo(installProInfo);
        //光大POC
        //获取分期入帐参数信息
        String tableId = installProInfo.getPostingTransactionParmId();
        InstallAccountingTransParmResDTO accountTran = installAccountingTransParmService.selectByIndex(organizationNumber, tableId);
        installParameter.setInstallAccountTran(accountTran);
        if (installParameter.getInstallProInfo() != null) {
            //分期类型
            String productType = installParameter.getInstallProInfo().getProdType();
            //获取分期类型
            InstallTypeParmResDTO intallTypeByOrgNumAndType = installTypeParmService.findByOrgNumAndType(organizationNumber, productType);
            if (!Objects.equals(productType, intallTypeByOrgNumAndType.getType())) {
                logger.error("分期入账预处理 分期类型参数不存在 productType={}", productType);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_TYPE_NOT_NULL_FAULT);
            }
        }
        installParameter.setInstallOrder(installOrderDto);
        installParameter.setOriginTransactionId(installOrderDto.getOriginTransactionId());
        return installOrderService.adjustInstallOrderTerm(installParameter);
    }


    /**
     * 相关字段赋值
     * @param outstandingTransaction
     * @param hashMap
     */
    public void setInstallToOutstandingTransaction(OutstandingTransactionDTO outstandingTransaction,Map hashMap){
        InstallParameterDTO installParameter = (InstallParameterDTO) hashMap.get(InstallmentConstant.INSTALLPARAMETER);
        if(null != installParameter){
            InstallProductInfoResDTO installProductInfoResDTO = installParameter.getInstallProInfo();
            outstandingTransaction.setInstalmentProduct(installProductInfoResDTO.getProductCode());
            outstandingTransaction.setInstallmentType(installProductInfoResDTO.getProdType());
            outstandingTransaction.setInstallLimitProcessIndicator(installProductInfoResDTO.getLimitProcessMode());
        }

    }

    /**
     * 分期订单管理
     *
     * @param outstandingTransaction
     * @param hashMap
     * @return int
     */
    private String installManage(OutstandingTransactionDTO outstandingTransaction, Map hashMap) {
        InstallParameterDTO installParameter = (InstallParameterDTO) hashMap.get(InstallmentConstant.INSTALLPARAMETER);
        String originalGlobalFlowNumber = outstandingTransaction.getOriginalGlobalFlowNumber();
        InstallOrderDTO installOrder = installOrderService.selectByGlobalFlowNumber(originalGlobalFlowNumber);
        if (installOrder == null) {
            logger.error("分期订单不存在 installOrder={}", installOrder);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ORDER_NOT_EXIST_FAULT);
        }
        if(null != installParameter){
            outstandingTransaction.setInstalmentProduct(installParameter.getInstallProInfo().getProductCode());
        }
        try {
            int result = installmentOrderManagement.installmentOrderManagement(InstallmentFunctionCodeEnum.RETURNGOODS.getCode(), outstandingTransaction, installOrder.getOrderId());
            if (result <= 0) {
                logger.error("分期订单管理失败 result={}", result);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_MANAGE_FAIL_FAULT);
            }
            return installOrder.getOrderId();
        } catch (Exception e) {
            logger.error("分期订单管理异常 result={}", e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_MANAGE_EXCEPTION_FAULT);
        }
    }

    /**
     * 创建订单
     *
     * @param outstandingTransaction
     * @param hashMap
     * @return int
     */
    private String insertInstallOrder(OutstandingTransactionDTO outstandingTransaction, Map hashMap,CustReconciliationControlDTO controlDTO) {
        InstallParameterDTO installParameter = (InstallParameterDTO) hashMap.get(InstallmentConstant.INSTALLPARAMETER);

        if (logger.isDebugEnabled()) {
            logger.debug("创建订单的输入参数InstallParameterDTO=[{}]",installParameter.toString());
        }
        InstallOrderDTO installOrderDTO = packageInstallOrder(outstandingTransaction
                , installParameter.getInstallProInfo().getProductCode()
                , installParameter.getInstallProInfo().getProdType());
        installParameter.setInstallOrder(installOrderDTO);
        installParameter.setOriginTransactionId(outstandingTransaction.getOriginTransactionId());
        //对账表信息
        installParameter.setCustReconciliationDTO(controlDTO);
        try {
            return installOrderService.add(installParameter);
        } catch (Exception e) {
            logger.error("分期订单创建异常,", e);
            throw e;
        }
    }

    /**
     * 写拒付文件
     *
     * @param outstandingTransaction
     * @param sb
     * @return InstallOrder
     */
    private void refusalFile(OutstandingTransactionDTO outstandingTransaction, StringBuilder sb) throws IOException {
        FileOutputStream fos = null;
        //拒付文件
        String name = "installmentCreateFail";
        File file = installmentCreateFileUtil.newFile(name);
        //创建输出流
        fos = new FileOutputStream(file);
        try {
            //从流中获取通道
            FileChannel channel = fos.getChannel();
            //提供一个缓存区
            ByteBuffer buffer = ByteBuffer.allocate(1024 * 2048);
            //拒付文件
            sb.append("TRANCURR").append(outstandingTransaction.getTransactionCurrencyCode()).append("|");
            sb.append("POSTCURR").append(outstandingTransaction.getBillingCurrencyCode()).append("|");
            sb.append("TRANAMTSIGN").append("SPACE").append("|");
            sb.append("POSTAMTSIGN").append("SPACE").append("|");
            sb.append("DECLCODE").append("2").append("|");
            //往缓存去写数据
            buffer.put(sb.toString().getBytes(StandardCharsets.UTF_8));
            //翻转缓存区
            buffer.flip();
            //把缓冲区写到同道中
            channel.write(buffer);
        } catch (IOException exex) {
            logger.error("分期预处理拒付文件异常 Exception={}", exex);
        } finally {
            fos.close();
        }
    }

    /**
     * 封装订单
     *
     * @param outstandingTransaction
     * @param productCode
     * @param prodType
     * @return InstallOrder
     */
    private InstallOrderDTO packageInstallOrder(OutstandingTransactionDTO outstandingTransaction, String productCode, String prodType) {
        InstallOrderDTO installOrderDTO = new InstallOrderDTO();
        installOrderDTO.setOrganizationNumber(outstandingTransaction.getOrganizationNumber());
        logger.info("分期类型:{},管理账户ID:{}",prodType,outstandingTransaction.getInstallAccountManagerId());
        if (Objects.equals(InstallmentTypeEnum.SINGLE_INSTALL.getCode(), prodType) || Objects.equals(
            InstallmentTypeEnum.STATEMENT_INSTALL.getCode(), prodType) || Objects.equals(InstallmentTypeEnum.AUTO_INSTALL.getCode(), prodType)
                || Objects.equals(InstallmentTypeEnum.APP_STATEMENT_INSTALL.getCode(), prodType)|| Objects.equals(InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode(), prodType)
                || Objects.equals("RD", prodType)
                || Objects.equals("LD", prodType)){
            installOrderDTO.setAccountManagementId(outstandingTransaction.getInstallAccountManagerId());
        }
        //最小授权金额
        InstallTypeParmResDTO installTypeParmResDTO = installTypeParmService.findByOrgNumAndType(outstandingTransaction.getOrganizationNumber(), prodType);
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(outstandingTransaction.getCardNumber(),outstandingTransaction.getOrganizationNumber());
        if(!ObjectUtils.isEmpty(cardAuthorizationInfo)){

            //客户id 根据卡片授权信息的主附卡标识获取
            String customerId = Objects.equals(RelationshipIndicatorEnum.MIAN_CARD.getCode(),
                    cardAuthorizationInfo.getRelationshipIndicator()) ? cardAuthorizationInfo.getPrimaryCustomerId()
                    : cardAuthorizationInfo.getSupplementaryCustomerId() ;

            installOrderDTO.setCustomerId(customerId);
            CustomerBasicInfo customerBasicInfo = customerBasicInfoSelfMapper.selectByOrgAndCustId(cardAuthorizationInfo.getOrganizationNumber(), customerId);
            installOrderDTO.setBranchNumber(customerBasicInfo.getBranchNumber());

            CustomerAuthorizationInfo customerAuthorizationInfo = customerAuthorizationInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(),cardAuthorizationInfo.getPrimaryCustomerId());
            installOrderDTO.setGroupType(customerAuthorizationInfo.getGroupType());
        }

        installOrderDTO.setCustomerRegion(outstandingTransaction.getCustomerRegion());
        installOrderDTO.setCardNumber(outstandingTransaction.getCardNumber());
        installOrderDTO.setProductCode(productCode);
        installOrderDTO.setTransactionDate(outstandingTransaction.getTransactionTime());
        installOrderDTO.setAcquireReferenceNo(outstandingTransaction.getAcquireReferenceNo());
        installOrderDTO.setAuthorizationCode(outstandingTransaction.getAuthorizationCode());
        installOrderDTO.setType(prodType);
        installOrderDTO.setInstallmentAmount(outstandingTransaction.getTransactionAmount());
        installOrderDTO.setTerm(outstandingTransaction.getTerm());
        installOrderDTO.setFeeDerateFlag(outstandingTransaction.getInstallmentDerateMethod());
        installOrderDTO.setDerateFeeAmount(outstandingTransaction.getInstallmentDerateValue());
        installOrderDTO.setTotalFeeAmount(outstandingTransaction.getInstallmentTotalFee());
        installOrderDTO.setFeeRate(outstandingTransaction.getInstallmentFeeRate());
        installOrderDTO.setInstallPriceFlag(outstandingTransaction.getInstallmentPriceFlag());
        String creditLimitNodeId = outstandingTransaction.getCreditLimitNodeId();
        installOrderDTO.setLimitCode(creditLimitNodeId);
        installOrderDTO.setInstallmentCcy(outstandingTransaction.getTransactionCurrencyCode());
        installOrderDTO.setMerchantId(outstandingTransaction.getMerchantId());
        if (outstandingTransaction.getInstallmentDerateValue() != null) {
            installOrderDTO.setDerateTerm(outstandingTransaction.getInstallmentDerateValue().intValue());
        }
        installOrderDTO.setOriginTransactionId(outstandingTransaction.getOriginTransactionId());
        installOrderDTO.setTransactionDesc(outstandingTransaction.getTransactionDesc());
        installOrderDTO.setAuthTransactionTypeTop(outstandingTransaction.getAuthTransactionTypeTop());
        installOrderDTO.setAuthTransactionTypeDetail(outstandingTransaction.getAuthTransactionTypeDetail());
        installOrderDTO.setMcc(outstandingTransaction.getMcc());
        installOrderDTO.setGlobalFlowNumber(outstandingTransaction.getGlobalFlowNumber());
        installOrderDTO.setOriginalGlobalFlowNumber(outstandingTransaction.getOriginalGlobalFlowNumber());
        //最小授权金额
        installOrderDTO.setMinAuthAmount(installTypeParmResDTO.getMinAuthAmount());
        //最大授权金额
        installOrderDTO.setMaxAuthAmount(installTypeParmResDTO.getMaxAuthAmount());
        installOrderDTO.setMerchantName(outstandingTransaction.getMerchantName());
        installOrderDTO.setRetrievalReferenceNumber(outstandingTransaction.getRetrievalReferenceNumber());

        installOrderDTO.setInstallmentTotalInterest(outstandingTransaction.getInstallmentTotalInterest());
        return installOrderDTO;
    }

    /**
     * 必输项校验
     *
     * @param outstandingTransaction
     * @return HashMap
     */
    private Map passCheck(OutstandingTransactionDTO outstandingTransaction) {
        StringBuilder sb = new StringBuilder();
        sb.append("BATCHSEQUENCE:").append("00001").append("|");
        HashMap<String, Object> hashMap = new HashMap(10);
        try {
            if (outstandingTransaction == null) {
                logger.error("分期入账预处理 未并账实体为空 outstandingTransaction ={}", outstandingTransaction);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_OUTSTANDING_TRANS_NULL_FAULT, InstallRepDetailEnum.NULL, outstandingTransaction.toString());
            }
            //机构号
            String organizationNumber = outstandingTransaction.getOrganizationNumber();
            if (StringUtils.isBlank(organizationNumber)) {
                sb.append("ORG:").append(organizationNumber).append("|");
                refusalFile(outstandingTransaction, sb);
                logger.error("分期入账预处理 所输入的机构号不存在 organizationNumber ={}", organizationNumber);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_ORG_NUM_NULL_FAULT, InstallRepDetailEnum.NULL, organizationNumber);
            }
            //获取卡号
            String cardNumber = outstandingTransaction.getCardNumber();
            if (!StringUtils.isBlank(cardNumber)) {
                //判断卡号是否存在
                CardAuthorizationInfo cardAuthorizationInfo = installmentThreadLocalHolder.setCardAuthorization(cardNumber);
                if (cardAuthorizationInfo == null || StringUtils.isBlank(cardAuthorizationInfo.getCardNumber())) {
                    sb.append("CARDNUMBER:").append(cardNumber).append("|");
                    refusalFile(outstandingTransaction, sb);
                    logger.error("分期入账预处理 所输入的卡号不存在 cardNumber={}", cardNumber);
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_CARD_NUM_NULL_FAULT, InstallRepDetailEnum.NULL, cardNumber);
                }
            }
            //校验交易码(入账交易码)
            String tranCode = outstandingTransaction.getPostingTransactionCode();
            if (StringUtils.isBlank(tranCode)) {
                sb.append(InstallmentConstant.TRANCODE).append(tranCode).append("|");
                refusalFile(outstandingTransaction, sb);
                logger.error("分期预处理 交易码为空  tranCode={}", tranCode);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_TRANS_CODE_NULL_FAULT, InstallRepDetailEnum.NULL, tranCode);
            }
            //必须是数字
            /*if (!isNumber(tranCode)) {
                sb.append(InstallmentConstant.TRANCODE).append(tranCode).append("|");
                refusalFile(outstandingTransaction, sb);
                logger.error("交易码只能由数字组成 tranCode={}", tranCode);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_TRANS_CODE_MADE_UP_OF_NUMS_FAULT);
            }*/
            //检查交易码是否存在交易码参数表
            TransactionCodeResDTO transactionCode = transactionCodeService.findTransactionCode(organizationNumber,tranCode);
            if (transactionCode == null) {
                logger.error("查交易码参数表失败  transactionCode={}", transactionCode);
                refusalFile(outstandingTransaction, sb);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_TRANS_CODE_PARAM_NOT_EXIST_FAULT);
            }
            if (StringUtils.isBlank(transactionCode.getTransactionCode())) {
                sb.append(InstallmentConstant.TRANCODE).append(tranCode).append("|");
                refusalFile(outstandingTransaction, sb);
                logger.error("分期入账预处理 交易码不存在 tranCode={}", tranCode);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_TRANS_CODE_NULL_FAULT);
            }
            //获取交易借贷标识
            String debitCreditIndicator = transactionCode.getDebitCreditIndicator();
            //交易金额
            BigDecimal transactionAmount = outstandingTransaction.getTransactionAmount();
            if (transactionAmount == null || transactionAmount.signum() == -1) {
                sb.append("TRANAMTDECIMAL").append("0").append("|");
                refusalFile(outstandingTransaction, sb);
                logger.error("分期入账预处理 交易金额为null或者为负数 transactionAmount={}", transactionAmount);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_TRANS_AMOUNT_NULL_OR_MINUS_FAULT, InstallRepDetailEnum.NULL, transactionAmount.toString());
            }
            // 入账金额
            BigDecimal billingAmount = outstandingTransaction.getBillingAmount();
            if (billingAmount == null || billingAmount.signum() == -1) {
                sb.append("POSTAMTDECIMAL").append("0").append("|");
                refusalFile(outstandingTransaction, sb);
                logger.error("分期入账预处理 入账金额为null或者为负数 billingAmount={}", billingAmount);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_BILL_AMOUNT_NULL_OR_MINUS_FAULT);
            }
            // 清算金额
            BigDecimal settlementAmount = outstandingTransaction.getSettlementAmount();
            if (settlementAmount == null || settlementAmount.signum() == -1) {
                sb.append("SETTLEMENTAMOUNT").append("0").append("|");
                refusalFile(outstandingTransaction, sb);
                logger.error("分期入账预处理 清算金额为null或者为负数 settlementAmount={}", settlementAmount);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_SETTLE_AMOUNT_NULL_OR_MINUS_FAULT);
            }

            //检查交易日期必须是合法日期
            LocalDate transactionDate = outstandingTransaction.getTransactionDate();
            //检查交易日期必须是合法日期且不能大于当前业务日期
            OrganizationInfoResDTO paramOrganizationInfo = organizationInfoService.findOrganizationInfo(outstandingTransaction.getOrganizationNumber());
            if (paramOrganizationInfo == null) {
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORG_INFO_PARAM_NOT_EXIST_FAULT);
            }
            //todo 暂时解决跨年问题
//            outstandingTransaction.setTransactionDate(paramOrganizationInfo.getNextProcessingDay());
//            transactionDate = paramOrganizationInfo.getNextProcessingDay();
            //当前业务日期
            LocalDate sysDate = paramOrganizationInfo.getNextProcessingDay();
            if (transactionDate.isAfter(sysDate)) {
                sb.append("SYSDATE").append(sysDate).append("|");
                refusalFile(outstandingTransaction, sb);
                logger.error("交易日期大于机构表当前业务日期 transactionDate={}", transactionDate);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_TRANS_DATE_NOT_GREATER_THAN_TODAY_FAULT);
            }
            //检查MCC代码（AMTX-D-MCC）必须是数字
            String mcc = outstandingTransaction.getMcc();
            if (StringUtils.isNotEmpty(mcc)) {
                //必须是数字
                if (!isNumber(mcc)) {
                    sb.append("MCC").append(mcc).append("|");
                    refusalFile(outstandingTransaction, sb);
                    logger.error("分期入账预处理 MCC代码只能由数字组成 mcc={}", mcc);
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_MCC_MADE_UP_OF_NUMS_FAULT);
                }
            }
            if(Objects.equals(DebitCreditIndEnum.CREDIT_INDICATOR.getCode(), debitCreditIndicator)){
                hashMap.put(InstallmentConstant.DEBIT_CREDIT_INDICATOR, debitCreditIndicator);
                hashMap.put(InstallmentConstant.INSTALLPARAMETER, null);
                return hashMap;
            }
            //检查分期定价方式只能是0-取基础定价、1-外围上送总的费用、2-外围上送费率。
            String installPriceFlag = outstandingTransaction.getInstallmentPriceFlag();
            if (!(Objects.equals(InstallPriceFlagEnum.BASICPRICING_FLAG.getCode(), installPriceFlag) || Objects.equals(InstallPriceFlagEnum.TOTALCOST_FLAG.getCode(), installPriceFlag)
                    || Objects.equals(InstallPriceFlagEnum.DELIVERYRATE_FLAG.getCode(), installPriceFlag))) {
                logger.error("分期入账预处理 分期定价方式错误 installPriceFlag:{}", installPriceFlag);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_PRICE_FLAG_FAULT);
            }
            //如果分期定价方式是1，则分期总的费用必须输入
            if (Objects.equals(InstallPriceFlagEnum.TOTALCOST_FLAG.getCode(), installPriceFlag)) {
                BigDecimal installTotalFee = outstandingTransaction.getInstallmentTotalFee();
                if (installTotalFee == null) {
                    logger.error("定价方式是 1 分期总的费用必须输入 installTotalFee:{}", installTotalFee);
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_TOTAL_FEE_NOT_NULL_FAULT);
                }
            }
            //如果过分期定价方式是2，则分期费率必须输入
            if (Objects.equals(InstallPriceFlagEnum.DELIVERYRATE_FLAG.getCode(), installPriceFlag)) {
                BigDecimal feeRate = outstandingTransaction.getInstallmentFeeRate();
                if (feeRate == null) {
                    logger.error("定价方式是 2 分期费率必须输入 feeRate:{}", feeRate);
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_FEE_RATE_NOT_NULL_FAULT);
                }
            }
            //检查分期费用减免标志只能是0-不减免、1-减免期数、2-减免金额；3-每期折扣。
            String derateMethod = outstandingTransaction.getInstallmentDerateMethod();
            if (!Objects.equals(InstallPriceFlagEnum.BASICPRICING_FLAG.getCode(), installPriceFlag)) {
                if (!(Objects.equals(InstallmentDerateMethodEnum.REDUCTION_NO.getCode(), derateMethod) || Objects.equals(InstallmentDerateMethodEnum.REDUCTION_TERM.getCode(), derateMethod)
                        || Objects.equals(InstallmentDerateMethodEnum.REDUCTION_AMOUT.getCode(), derateMethod) || Objects.equals(InstallmentDerateMethodEnum.DISCOUNT.getCode(), derateMethod))) {
                    logger.error("分期费用减免标志 错误 derateMethod:{}", derateMethod);
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_DERATE_METHOD_FAULT);
                }
            }
            //如果减免方式是1/2/3，则减免值必须输入
            if (Objects.equals(InstallmentDerateMethodEnum.REDUCTION_TERM.getCode(), derateMethod) || Objects.equals(InstallmentDerateMethodEnum.REDUCTION_AMOUT.getCode(), derateMethod)
                    || Objects.equals(InstallmentDerateMethodEnum.DISCOUNT.getCode(), derateMethod)) {
                //减免值
                BigDecimal derateValue = outstandingTransaction.getInstallmentDerateValue();
                if (derateValue == null || derateValue.signum() == -1) {
                    logger.error("分期入账预处理 减免值必须输入 derateValue:{}", derateValue);
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_DERATE_VALUE_NOT_NULL_FAULT);
                }
            }
            //分期产品号获取
            InstallParameterDTO installParameter = verificaProduct(outstandingTransaction);

            if (installParameter.getInstallProInfo() != null) {
                //分期类型
                String productType = installParameter.getInstallProInfo().getProdType();
                //获取分期类型
                InstallTypeParmResDTO intallTypeByOrgNumAndType = installTypeParmService.findByOrgNumAndType(organizationNumber, productType);
                if (!Objects.equals(productType, intallTypeByOrgNumAndType.getType())) {
                    sb.append("PRODUCTTYPE").append(productType).append("|");
                    logger.error("分期入账预处理 分期类型参数不存在 productType={}", productType);
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_TYPE_NOT_NULL_FAULT);
                }
                hashMap.put(InstallmentConstant.DEBIT_CREDIT_INDICATOR, debitCreditIndicator);
                hashMap.put(InstallmentConstant.INSTALLPARAMETER, installParameter);

            }
        } catch (IOException e) {
            logger.error("分期入账预处理 拒付文件异常 IOException=", e);
        }
        return hashMap;
    }

    /**
     * 获取分期产品
     *
     * @return InstallParameterDTO
     * @Param outstandingTransaction
     **/
    private InstallParameterDTO verificaProduct(OutstandingTransactionDTO outstandingTransaction) {
        InstallParameterDTO installParameterDTO = new InstallParameterDTO();
        //分期产品
        InstallProductInfoResDTO installProInfo;
        if (!StringUtils.isBlank(outstandingTransaction.getInstalmentProduct())) {
            //获取分期产品
            installProInfo = installProductInfoService.findByIndex(outstandingTransaction.getOrganizationNumber()
                    , outstandingTransaction.getInstalmentProduct());
            if (installProInfo == null) {
                logger.error("分期入账预处理 调用参数服务查询分期产品参数表失败 installProInfo={}", installProInfo);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PRO_PARAM_NOT_EXIST_FAULT);
            }
        } else {
            //通过交易类型大类、交易类型细类去读分期类型参数
            String authTransactionTypeTop = outstandingTransaction.getAuthTransactionTypeTop();
            String authTransactionTypeDetail = outstandingTransaction.getAuthTransactionTypeDetail();
            //获取分期类型
            InstallTypeParmResDTO intallType = installTypeParmService.findByOrgNumAndTypeAndDetail(outstandingTransaction.getOrganizationNumber(), authTransactionTypeTop, authTransactionTypeDetail);
            installParameterDTO.setInstallProdType(intallType);
            if (intallType == null) {
                logger.error("分期入账预处理 调用参数服务查询分期类型参数表失败 installProInfo={}", intallType);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_TYPE_PARAM_NOT_EXIST_FAULT);
            }
            //获取分期类型
            String productType = intallType.getType();
            if (StringUtils.isBlank(productType)) {
                logger.error("分期入账预处理 分期类型为空数据问题  productType={}", intallType);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_TYPE_NOT_NULL_FAULT);
            }
            Integer term = outstandingTransaction.getIsPostInstallment() == 1
                    ? InstallmentTypeEnum.POS_INSTALL.getDefaultTerms()
                    : outstandingTransaction.getTerm();
            installProInfo = installProductInfoService.findProInfoByTermAndType(outstandingTransaction.getOrganizationNumber(),
                    productType, term);
        }

        if (installProInfo == null){
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_PRO_CODE_NOT_NULL_FAULT);
        }

        //pos分期期数从入参中获取,不再用
        if (Objects.equals(InstallmentTypeEnum.POS_INSTALL.getDefaultTerms(),
                installProInfo.getTerm())
                && Objects.equals(InstallmentTypeEnum.POS_INSTALL.getCode(),
                installProInfo.getProdType())){
            installProInfo.setTerm(outstandingTransaction.getTerm());
        }

        installParameterDTO.setInstallProInfo(installProInfo);

        //光大POC
        //获取分期入帐参数信息
        String tableId = installProInfo.getPostingTransactionParmId();
        InstallAccountingTransParmResDTO accountTran = installAccountingTransParmService.selectByIndex(outstandingTransaction.getOrganizationNumber(), tableId);
        installParameterDTO.setInstallAccountTran(accountTran);
        return installParameterDTO;
    }


    /**
     * 判断字符串是否由数字组成
     * @param validateString 需要判断的字符串
     * @return boolean
     */
    private boolean isNumber(String validateString){
        String regex = "^[0-9]*$";
        Pattern pattern = Pattern.compile(regex);
        Matcher m = pattern.matcher(validateString);
        return m.matches();
    }
}
