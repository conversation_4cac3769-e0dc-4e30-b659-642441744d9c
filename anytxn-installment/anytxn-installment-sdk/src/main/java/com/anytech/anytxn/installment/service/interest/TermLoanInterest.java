package com.anytech.anytxn.installment.service.interest;

import com.anytech.anytxn.common.core.utils.JacksonUtils;
import com.anytech.anytxn.installment.base.domain.dto.InstallmentInterestDTO;
import com.anytech.anytxn.installment.base.enums.ChargeOptionEnum;
import com.anytech.anytxn.installment.base.service.IInterest;
import com.anytech.anytxn.installment.service.manager.InstallManager;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: sukang
 * @Date: 2022/11/10 15:55
 */
@Slf4j
public class TermLoanInterest implements IInterest {


    @Override
    public InstallmentInterestDTO getInterestResult(InstallmentInterestDTO interestDTO) {

        //每期金额 向下取整  分期金额/分期期数
        BigDecimal termAmount = interestDTO.getCostAmount()
                .divide(new BigDecimal(String.valueOf(interestDTO.getTerm())), 0, BigDecimal.ROUND_DOWN);

        //尾款     分期金额 - 每期金额*(分期期数 - 1)
        BigDecimal balanceAmount = interestDTO.getCostAmount()
                .subtract(termAmount.multiply(new BigDecimal(interestDTO.getTerm()).subtract(BigDecimal.ONE)));


        Map<Integer, BigDecimal> installPlanMap = new HashMap<>(12);

        for (int i = 1; i <= interestDTO.getTerm(); i++) {
            BigDecimal totalAmount = termAmount;
            //第一期 & 尾款放第一期
            if (i == 1 && InstallManager.isFirstTerm(interestDTO.getBalanceMethod())){
                totalAmount = balanceAmount;
            }
            //最后一期 & 尾款放最后一期
            if (i == interestDTO.getTerm() && InstallManager.isLastTerm(interestDTO.getBalanceMethod())){
                totalAmount = balanceAmount;
            }

            //年化利率/1200*每期金额*(分期期数 - i +1)/1

            BigDecimal termFee = interestDTO.getAnnualInterestRate()
                    .divide(new BigDecimal("1200"),6,BigDecimal.ROUND_DOWN)
                    .multiply(totalAmount)
                    .multiply(new BigDecimal(interestDTO.getTerm() - i + 1))
                    .divide(BigDecimal.ONE,4,BigDecimal.ROUND_DOWN);

            installPlanMap.put(i,termFee);
        }

        log.info("Term Loan calculation result is {}", JacksonUtils.toJsonStr(installPlanMap));

        Optional<BigDecimal> reduce = installPlanMap.values().stream().reduce(BigDecimal::add);
        if (reduce.isPresent()){
            //总利率/分期期数(保留4位 进1)（保留2 直接舍）*分期期数
            BigDecimal interestAmount = reduce.get().divide(new BigDecimal(interestDTO.getTerm()), 4, BigDecimal.ROUND_UP)
                    .multiply(new BigDecimal(String.valueOf(interestDTO.getTerm())))
                    .setScale(2, BigDecimal.ROUND_DOWN);

            interestDTO.setInterestAmount(interestAmount);

        }
        return interestDTO;
    }

    @Override
    public String getInterestCode() {
        return ChargeOptionEnum.T_TERM.getCode();
    }


}
