package com.anytech.anytxn.installment.controller.order;

import com.anytech.anytxn.installment.base.domain.dto.AdjustInstallTermDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallOrderSearchKeyDTO;
import com.anytech.anytxn.installment.base.enums.InstallRepDetailEnum;
import com.anytech.anytxn.installment.base.service.IInstallAdjustTermService;
import com.anytech.anytxn.installment.base.service.IInstallOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallmentLimitUnitCrossDTO;
import com.anytech.anytxn.business.base.installment.service.InstallmentLimitUnitCrossService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2019-06-06 14:58
 **/
@RestController
@Api(tags = "分期订单")
public class InstallOrderController extends BizBaseController {
    @Autowired
    private IInstallOrderService installOrderService;

    @Autowired
    private InstallmentLimitUnitCrossService installmentLimitUnitCrossService;

    @Autowired
    private IInstallAdjustTermService installAdjustTermService;

    @ApiOperation(value = "根据订单id 查询订单表")
    @GetMapping(value = "/install/istallorder/orderId/{orderId}")
    public AnyTxnHttpResponse<InstallOrderDTO> getOrderById(@PathVariable(value = "orderId") String orderId) {
        InstallOrderDTO installOrderRes = installOrderService.findOrderById(orderId);
        return AnyTxnHttpResponse.success(installOrderRes);
    }

    @ApiOperation(value = "根据订单accountManagementId productCode transactionDate acquireReferenceNo查询订单表")
    @GetMapping(value = "/install/istallorder/accountManagementId/{accountManagementId}/productCode/{productCode}/transactionDate/{transactionDate}/authorizationCode/{authorizationCode}/installmentAmount/{installmentAmount}")
    public AnyTxnHttpResponse<InstallOrderDTO> getOrderByManageAndCodeAndDateAndAcquire(@PathVariable(value = "accountManagementId") String accountManagementId,
                                                                                        @PathVariable(value = "productCode") String productCode,
                                                                                        @PathVariable(value = "transactionDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate transactionDate,
                                                                                        @PathVariable(value = "authorizationCode") String authorizationCode,
                                                                                        @PathVariable(value = "installmentAmount") BigDecimal installmentAmount) {
        InstallOrderDTO installOrderRes = installOrderService.orderByManageAndCodeAndDateAndAcquire(
                accountManagementId,
                productCode,
                transactionDate,
                authorizationCode,
                installmentAmount);
        return AnyTxnHttpResponse.success(installOrderRes);
    }

    @ApiOperation(value = "分页查询分期订单表", notes = "分页查询分期订单表")
    @PostMapping(value = "/install/istallorder")
    public AnyTxnHttpResponse<PageResultDTO<InstallOrderDTO>> getOrderPageByOptions(@RequestBody(required = false) InstallOrderSearchKeyDTO installOrderSearchKeyDTO) {
        PageResultDTO<InstallOrderDTO> txnPage = installOrderService.orderByOrgAndManageAndProductAndDateAndAuthorAndAmount(installOrderSearchKeyDTO);
        return AnyTxnHttpResponse.success(txnPage);
    }

    @ApiOperation(value = "分页查询分期订单管控单元关联表", notes = "分页查询分期订单管控单元关联表")
    @GetMapping(value = "/install/istallorder/installLimitUnitCross")
    public AnyTxnHttpResponse<PageResultDTO<InstallmentLimitUnitCrossDTO>> getOrderPageByOptions(@ApiParam("机构号") @RequestParam String organizationNumber,
                                                                                                 @ApiParam("分期订单号") @RequestParam String installmentOrderId,
                                                                                                 @ApiParam("页码，默认1") @RequestParam(required = false) int page,
                                                                                                 @ApiParam("每页显示条数,默认8") @RequestParam(required = false) int pageSize) {
        PageResultDTO<InstallmentLimitUnitCrossDTO> txnPage = installmentLimitUnitCrossService.selectByOrgAndInstallOrderId(organizationNumber,installmentOrderId,page,pageSize);
        return AnyTxnHttpResponse.success(txnPage);
    }

    @ApiOperation(value = "支持分期调整还款期数（缩短或延长）", notes = "支持分期调整还款期数（缩短或延长）")
    @PostMapping(value = "/install/adjustTerm")
    public AnyTxnHttpResponse<Void> getOrderPageByOptions(@RequestBody(required = false) AdjustInstallTermDTO adjustTermDto) {
        installAdjustTermService.adjustInstallTerm(adjustTermDto);
        return AnyTxnHttpResponse.successDetail(InstallRepDetailEnum.AD_RE.message());
    }
}
