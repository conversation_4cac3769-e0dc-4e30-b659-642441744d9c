package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import com.anytech.anytxn.installment.base.utils.InstallmentThreadLocalHolder;
import com.anytech.anytxn.parameter.account.mapper.ParmStatementProcessSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.dto.StatementProcessResDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmStatementProcess;
import com.anytech.anytxn.parameter.base.account.service.IStatementProcessService;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.AcctProductInfoDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.ProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.product.IAcctProductMainInfoService;
import com.anytech.anytxn.parameter.base.common.service.product.IProductInfoService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.common.rule.dto.DataInputDTO;
import com.anytech.anytxn.common.rule.matcher.RuleMatcherManager;
import com.anytech.anytxn.common.rule.matcher.TxnRuleMatcher;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class StatementManager {
    private Logger logger = LoggerFactory.getLogger(StatementManager.class);

    @Autowired
    private ParmStatementProcessSelfMapper parmStatementProcessSelfMapper;

    @Autowired
    private IAcctProductMainInfoService acctProductMainInfoService;

    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;

    @Autowired
    private IProductInfoService productInfoService;

    @Autowired
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;

    @Autowired
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;

    @Autowired
    private IOrganizationInfoService organizationInfoService;

    @Autowired
    private InstallmentThreadLocalHolder installmentThreadLocalHolder;

    @Autowired
    private IStatementProcessService statementProcessService;

    private StatementProcessResDTO findByOrgAndTableId(String orgNumber, String tableId) {
        ParmStatementProcess statementProcess = this.parmStatementProcessSelfMapper.isExists(orgNumber, tableId);
        if (null == statementProcess) {
            throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_PRODUCT_STATEMENT_PROCESS_FAULT);
        } else {
            return (StatementProcessResDTO) BeanMapping.copy(statementProcess, StatementProcessResDTO.class);
        }
    }
    /**
     * 获取账单处理参数
     *
     * @param accountManagementId
     * @return
     */
    public LocalDate getStatementPaymentDueDate(String accountManagementId,String cardNumber) {
        AccountManagementInfo managementAccount = accountManagementInfoMapper.selectByAccountManagementIdAndOrganizationNumber(accountManagementId, OrgNumberUtils.getOrg());
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoSelfMapper.selectByCardNumber(cardNumber);
        ProductInfoResDTO productInfoResDTO = productInfoService.findProductInfo(managementAccount.getOrganizationNumber(), managementAccount.getProductNumber(), managementAccount.getCurrency()).get(0);

        //账单处理参数规则调用
        String tableId;
        // 公司卡
        if ("C".equals(managementAccount.getLiability())) {
            tableId = null;
        } else {
            //查询客户授权信息
            CustomerAuthorizationInfo customerAuthorizationInfo = customerAuthorizationInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(),cardAuthorizationInfo.getPrimaryCustomerId());
            if (customerAuthorizationInfo == null) {
                logger.error("客户授权信息不存在客户号:{}", managementAccount.getCustomerId());
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST);
            }
            tableId = tableIdGet(managementAccount, customerAuthorizationInfo);
        }

        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        if (StringUtils.isNotEmpty(tableId)) {
            StatementProcessResDTO statementParam = this.findByOrgAndTableId(managementAccount.getOrganizationNumber(), tableId);
            return organizationInfo.getAccruedThruDay().plusDays(Long.valueOf(statementParam.getDueDays()));

        } else {
            StatementProcessResDTO statementProcessResDTO = this.findByOrgAndTableId(managementAccount.getOrganizationNumber(), productInfoResDTO.getStatementProcessingTableId());
           return organizationInfo.getAccruedThruDay().plusDays(Long.valueOf(statementProcessResDTO.getDueDays()));

        }
    }
    /**
     * 账单处理参数规则调用
     *
     * @return String
     */
    /**
     * 匹配账单处理参数表规则
     *
     * @param managementAccount
     * @param customerAuthInfo
     * @return
     * @throws IOException
     */
    private String tableIdGet(AccountManagementInfo managementAccount, CustomerAuthorizationInfo customerAuthInfo) {
        AcctProductInfoDTO byOrgAndProductNum = acctProductMainInfoService.findByOrgAndProductNum(managementAccount.getOrganizationNumber(), managementAccount.getProductNumber());
        if (byOrgAndProductNum == null) {
            logger.warn("根据机构号:{},账产品号:{}查询账产品参数 数据不存在", managementAccount.getOrganizationNumber(), managementAccount.getProductNumber());
            return null;
        }
        Map<String, Object> map = new HashMap<>(1);
        map.put("accountManagementId", managementAccount.getAccountManagementId());
        map.put("groupType", customerAuthInfo.getGroupType());
        map.put("accountProductNum", managementAccount.getProductNumber());
        map.put("productAttribute", byOrgAndProductNum.getAcctProductMainInfoResDTO().getAttribute());

        String ruleType = "billProcessingParameterRules";
        //基于规则类型找到规则匹配器
        TxnRuleMatcher ruleMatcher = RuleMatcherManager.getMatcher(ruleType, OrgNumberUtils.getOrg());
        if (ruleMatcher == null) {
            return null;
        }
        //规则输入
        DataInputDTO dataInput = new DataInputDTO(map, ruleType);
        //执行优先规则匹配
        Map<String, Object> ruleMap = ruleMatcher.execute(dataInput);
        if (ruleMap != null && !ruleMap.isEmpty()) {
            return (String) ruleMap.entrySet().iterator().next().getValue();

        }
        if (logger.isWarnEnabled()) {
            logger.warn("账单处理匹配规则失败，ruleType:{}, data:{}", "billProcessingParameterRules", ruleMap);
        }
        return null;
    }
    public LocalDate calculateFirstPostDate( InstallProductInfoResDTO installProInfo,String accountManagementId){
        LocalDate firstPostDate;
        //延迟下账数/月
        Integer amountOfDelayed = installProInfo.getAmountOfDelayed();
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        //扣账日 0-交易日、1-账单日、2-还款日
        String billDateMethod = installProInfo.getBillDateMethod();
        firstPostDate  = organizationInfo.getNextProcessingDay();
        if (null != accountManagementId && !"".equals(accountManagementId)) {
            AccountManagementInfo accountManagementInfo = installmentThreadLocalHolder.setAccountManagementInfo(accountManagementId);
              if (accountManagementInfo == null) {
                  accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(accountManagementId);
              }
            List<ProductInfoResDTO> productInfoList = productInfoService.findProductInfo(accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getProductNumber(), accountManagementInfo.getCurrency());
            Short cycleDay = accountManagementInfo.getCycleDay();
            if ("0".equals(billDateMethod)) {
                firstPostDate = organizationInfo.getNextProcessingDay();
            } else if ("1".equals(billDateMethod)) {
                firstPostDate = calculateStatementDate(organizationInfo.getNextProcessingDay(), cycleDay);
            } else if ("2".equals(billDateMethod)) {
                LocalDate statementDate = calculateStatementDate(organizationInfo.getNextProcessingDay(), cycleDay);
                StatementProcessResDTO statementParam = statementProcessService.findByOrgAndTableId(accountManagementInfo.getOrganizationNumber(), productInfoList.get(0).getStatementProcessingTableId());
                firstPostDate = statementDate.plusDays(statementParam.getDueDays());
            }
        }
        if (amountOfDelayed != null){
            firstPostDate = firstPostDate.plusMonths(amountOfDelayed.longValue());
        }
        return firstPostDate;
    }
    /**
     * 下一账单日
     *
     * @param nextProcessingDay 下一处理日
     * @param cycleDate         账单日
     * @return 下一账单日
     */
    private LocalDate calculateStatementDate(LocalDate nextProcessingDay, short cycleDate) {
        int month;
        int year;
        if (nextProcessingDay.getDayOfMonth() > cycleDate) {
            month = nextProcessingDay.getMonthValue() + 1;
        } else {
            month = nextProcessingDay.getMonthValue();
        }
        year = nextProcessingDay.getYear();
        if (month >= 13) {
            month = month - 12;
            year = year + 1;
        }
        return LocalDate.of(year, month, cycleDate);
    }

}
