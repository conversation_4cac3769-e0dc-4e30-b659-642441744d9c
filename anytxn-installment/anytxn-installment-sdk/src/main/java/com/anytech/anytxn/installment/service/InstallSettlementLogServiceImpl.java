package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.base.authorization.domain.dto.OutstandingTransactionDTO;
import com.anytech.anytxn.business.base.installment.enums.InstallmentTypeEnum;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardBasicInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.card.model.CardBasicInfo;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallSettlementLogSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.base.transaction.domain.bo.RecordedBO;
import com.anytech.anytxn.installment.base.domain.bo.InstallSettlementLogBO;
import com.anytech.anytxn.installment.base.constants.InstallmentConstant;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.enums.DebitCreditIndEnum;
import com.anytech.anytxn.installment.base.enums.InstallPriceFlagEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.service.IInstallBookPretreatService;
import com.anytech.anytxn.installment.base.service.IInstallSettlementLogService;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallAccountingTransParmResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallTypeParmResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallAccountingTransParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallTypeParmService;
import com.anytech.anytxn.parameter.installment.mapper.ParmInstallMerchantMappingMapper;
import com.anytech.anytxn.parameter.base.installment.domain.model.ParmInstallMerchantMapping;
import com.anytech.anytxn.transaction.base.enums.MessageIndicatorEnum;
import com.anytech.anytxn.transaction.base.service.ITxnRecordedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-07-18
 **/
@Service
@Slf4j
public class InstallSettlementLogServiceImpl implements IInstallSettlementLogService {

    private Logger logger= LoggerFactory.getLogger(InstallSettlementLogServiceImpl.class);

    @Autowired
    private IInstallBookPretreatService installBookPretreatService;

    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;

    @Autowired
    private InstallSettlementLogSelfMapper installSettlementLogSelfMapper;

    @Autowired
    private ITxnRecordedService txnRecordedService;

    @Autowired
    private IInstallProductInfoService installProductInfoService;
    @Autowired
    private IInstallAccountingTransParmService installAccountingTransParmService;
    @Autowired
    private IInstallTypeParmService installTypeParmService;
    @Autowired
    private InstallOrderMapper installOrderMapper;
    @Autowired
    private CardBasicInfoMapper cardBasicInfoMapper;

    @Resource
    private ParmInstallMerchantMappingMapper parmInstallMerchantMappingMapper;

    @Resource
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;


    /**
     * 批量订单创建writer
     * @param list
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = AnyTxnInstallException.class)
    public void batchCreateInstallOrderWriter(List<? extends InstallSettlementLogBO> list) {
        for (InstallSettlementLogBO installSettlementLogDTO : list) {

            //非onus的卡不创建未并账交易
            CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(installSettlementLogDTO.getTxnCardNumber(), OrgNumberUtils.getOrg());
            if (ObjectUtils.isNotEmpty(cardAuthorizationInfo)){
                logger.info("卡号{} 分期期数{} 清算创建分期订单",installSettlementLogDTO.getTxnCardNumber(),
                        installSettlementLogDTO.getInstalmentPeriod());

                OutstandingTransactionDTO outstandingTransactionDTO = buildOutstandingTransactionDTO(installSettlementLogDTO);
                String orderId = installBookPretreatService.installBookPretreat(outstandingTransactionDTO);

                if(orderId == null){
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_BOOK_PREPROCESS_FAULT);
                }



                InstallProductInfoResDTO installProConf = getInstallProByOrgAndCode(outstandingTransactionDTO.getOrganizationNumber(), outstandingTransactionDTO.getInstalmentProduct());
                String postingTransactionParmId = installProConf.getPostingTransactionParmId();
                InstallAccountingTransParmResDTO installAccountTranConf = getInstallAccountTranByOrgNumAndTableId(outstandingTransactionDTO.getOrganizationNumber(), postingTransactionParmId);
                String createInstallmentFlag = installAccountTranConf.getCreateInstallmentFlag();
                if(InstallmentConstant.CREATE_INSTALLMENT_YES.equals(createInstallmentFlag)){
                    //调用核心入账
                    RecordedBO recorded = buildRecord(installSettlementLogDTO,outstandingTransactionDTO);
                    //分期类型
                    recorded.setInstallmentType(installSettlementLogDTO.getTxnInstallmetType());
                    //分期额度处理模式
                    recorded.setInstallmentLimitProcessIndicator(installProConf.getLimitProcessMode());
                    //分期订单id
                    recorded.setTxnInstallmentOrderId(orderId);
                    recorded.setInstallmentPostTransRecord(false);
                    txnRecordedService.txnRecorded(recorded);


                    InstallOrder installOrder = new InstallOrder();
                    installOrder.setOrderId(orderId);
                    installOrder.setAccountManagementId(recorded.getTxnAccountManageId());
                    installOrder.setTransactionDate(recorded.getTxnTransactionDate());
                    installOrderMapper.updateByPrimaryKeySelective(installOrder);
                }
                installSettlementLogSelfMapper.updateOrderSuccess(installSettlementLogDTO.getId(), "1");
            }else {
                log.info("在创建分期订单过程种，该分期交易的卡是非ONUS的，不予处理：{}",installSettlementLogDTO.getTxnCardNumber());
            }
        }

    }




    private RecordedBO buildRecord(InstallSettlementLogBO installSettlementLogDTO,
                                 OutstandingTransactionDTO outstandingTransactionDTO){
        RecordedBO recorded = new RecordedBO();
        recorded.setTxnAccountManageId(installSettlementLogDTO.getTxnAccountManageId());
        recorded.setTxnParentTransactionAccountId(installSettlementLogDTO.getTxnParentTxnAccountId());
        recorded.setTxnOriginalTransactionBalanceId(installSettlementLogDTO.getTxnOriginalTxnBalanceId());
        recorded.setTxnCardNumber(installSettlementLogDTO.getTxnCardNumber());
        recorded.setTxnOriginalTransactionDate(installSettlementLogDTO.getTxnOriginalTxnDate());
        recorded.setTxnGlobalFlowNumber(installSettlementLogDTO.getTxnGlobalFlowNumber());
        recorded.setTxnOriginalGlobalFlowNumber(installSettlementLogDTO.getTxnOriginalGlobalFlowNum());
        recorded.setTxnPostMethod(installSettlementLogDTO.getTxnPostMethod());
        recorded.setTxnRepostFromSuspend(installSettlementLogDTO.getTxnRepostFromSuspend());
        recorded.setTxnReverseFeeIndicator(installSettlementLogDTO.getTxnReverseFeeIndicator());
        recorded.setTxnTransactionCode(outstandingTransactionDTO.getPostingTransactionCode());
        recorded.setTxnTransactionDescription(installSettlementLogDTO.getTxnTransactionDescription());

        //双信息匹配条件
        recorded.setTxnTransactionDate(installSettlementLogDTO.getTxnOriginalTxnDate());
        recorded.setTxnTransactionSource(installSettlementLogDTO.getTxnTransactionSource());
        recorded.setTxnAuthorizationCode(installSettlementLogDTO.getTxnAuthorizationCode());
        recorded.setTxnBillingAmount(installSettlementLogDTO.getTxnBillingAmount());


        recorded.setTxnTransactionAmount(installSettlementLogDTO.getTxnTransactionAmount());
        recorded.setTxnTransactionCurrency(installSettlementLogDTO.getTxnTransactionCurrency());
        recorded.setTxnBillingDate(installSettlementLogDTO.getTxnBillingDate());
        recorded.setTxnBillingCurrency(installSettlementLogDTO.getTxnBillingCurrency());
        recorded.setTxnSettlementAmount(installSettlementLogDTO.getTxnSettlementAmount());
        recorded.setTxnSettlementCurrency(installSettlementLogDTO.getTxnSettlementCurrency());
        recorded.setTxnExchangeRate(installSettlementLogDTO.getTxnExchangeRate());
        recorded.setTxnZipCode(installSettlementLogDTO.getTxnZipCode());
        recorded.setTxnMerchantId(installSettlementLogDTO.getTxnMerchantId());
        recorded.setTxnMerchantName(installSettlementLogDTO.getTxnMerchantName());
        recorded.setTxnMerchantCategoryCode(installSettlementLogDTO.getTxnMerchantCategoryCode());
        recorded.setTxnCountryCode(installSettlementLogDTO.getTxnCountryCode());
        recorded.setTxnStateCode(installSettlementLogDTO.getTxnStateCode());
        recorded.setTxnCityCode(installSettlementLogDTO.getTxnCityCode());
        recorded.setTxnReferenceNumber(installSettlementLogDTO.getTxnReferenceNumber());
        recorded.setTxnAuthorizationMatchIndicator(installSettlementLogDTO.getTxnAuthMatchIndicator());
        recorded.setTxnReleaseAuthorizationAmount(installSettlementLogDTO.getTxnReleaseAuthAmount());
        recorded.setTxnLimitNodeId(installSettlementLogDTO.getTxnLimitNodeId());
        recorded.setTxnOutstandingAmount(installSettlementLogDTO.getTxnOutstandingAmount());
        recorded.setTxnOpponentBankNumber(installSettlementLogDTO.getTxnOpponentBankNum());
        recorded.setTxnOpponentAccountNumber(installSettlementLogDTO.getTxnOpponentAccountNum());
        recorded.setTxnOpponentAccountName(installSettlementLogDTO.getTxnOpponentAccountName());
        recorded.setTxnSecondMerchantId(installSettlementLogDTO.getTxnSecondMerchantId());
        recorded.setTxnSecondMerchantName(installSettlementLogDTO.getTxnSecondMerchantName());
        recorded.setTxnPosEntryMode(installSettlementLogDTO.getTxnPosEntryMode());
        recorded.setTxnVisaChargeFlag(installSettlementLogDTO.getTxnVisaChargeFlag());
        recorded.setTxnReimbursementAttribute(installSettlementLogDTO.getTxnReimbursementAttribute());
        recorded.setTxnIfiIndicator(installSettlementLogDTO.getTxnIfiIndicator());
        recorded.setTxnPsvIndicator(installSettlementLogDTO.getTxnPsvIndicator());
        recorded.setTxnDccIndicator(installSettlementLogDTO.getTxnDccIndicator());
        recorded.setTxnForcePostIndicator(installSettlementLogDTO.getTxnForcePostIndicator());
        recorded.setTxnFallBackIndicator(installSettlementLogDTO.getTxnFallBackIndicator());
        recorded.setTxnInstallmentIndicator("1");
        recorded.setTxnInstallmentOrderId(String.valueOf(installSettlementLogDTO.getTxnInstallmentOrderId()));
        recorded.setTxnInstallmentTerm(String.valueOf(installSettlementLogDTO.getTxnInstallmentTerm()));
        recorded.setTxnInterestTableId(installSettlementLogDTO.getTxnInterestTableId());
        recorded.setTxnFeeTableId(installSettlementLogDTO.getTxnFeeTableId());
        //双信息
        recorded.setMessageIndicator(MessageIndicatorEnum.DOUBLE_INDICATOR.getCode());
        return recorded;
    }

    private RecordedBO buildTransferFeeRecord(InstallSettlementLogBO installSettlementLogDTO,InstallProductInfoResDTO installProConf,InstallAccountingTransParmResDTO installAccountTranConf){
        RecordedBO recorded = new RecordedBO();
        recorded.setTxnAccountManageId(installSettlementLogDTO.getTxnAccountManageId());
        recorded.setTxnParentTransactionAccountId(installSettlementLogDTO.getTxnParentTxnAccountId());
        recorded.setTxnOriginalTransactionBalanceId(installSettlementLogDTO.getTxnOriginalTxnBalanceId());
        recorded.setTxnCardNumber(installSettlementLogDTO.getTxnCardNumber());
        recorded.setTxnOriginalTransactionDate(installSettlementLogDTO.getTxnOriginalTxnDate());
        recorded.setTxnGlobalFlowNumber(installSettlementLogDTO.getTxnGlobalFlowNumber());
        recorded.setTxnOriginalGlobalFlowNumber(installSettlementLogDTO.getTxnOriginalGlobalFlowNum());
        recorded.setTxnPostMethod(installSettlementLogDTO.getTxnPostMethod());
        recorded.setTxnRepostFromSuspend(installSettlementLogDTO.getTxnRepostFromSuspend());
        recorded.setTxnReverseFeeIndicator(installSettlementLogDTO.getTxnReverseFeeIndicator());
        recorded.setTxnTransactionCode(installAccountTranConf.getTransferFeeCode());
        recorded.setTxnTransactionSource(installSettlementLogDTO.getTxnTransactionSource());
        recorded.setTxnTransactionDescription("划账手续费");
        recorded.setTxnTransactionDate(installSettlementLogDTO.getTxnTransactionDate());
        recorded.setTxnTransactionAmount(installProConf.getTransferFee());
        recorded.setTxnTransactionCurrency(installSettlementLogDTO.getTxnTransactionCurrency());
        recorded.setTxnBillingDate(installSettlementLogDTO.getTxnBillingDate());
        recorded.setTxnBillingAmount(installProConf.getTransferFee());
        recorded.setTxnBillingCurrency(installSettlementLogDTO.getTxnBillingCurrency());
        recorded.setTxnSettlementAmount(installSettlementLogDTO.getTxnSettlementAmount());
        recorded.setTxnSettlementCurrency(installSettlementLogDTO.getTxnSettlementCurrency());
        recorded.setTxnExchangeRate(installSettlementLogDTO.getTxnExchangeRate());
        recorded.setTxnAuthorizationCode(installSettlementLogDTO.getTxnAuthorizationCode());
        recorded.setTxnZipCode(installSettlementLogDTO.getTxnZipCode());
        recorded.setTxnMerchantId(installSettlementLogDTO.getTxnMerchantId());
        recorded.setTxnMerchantName(installSettlementLogDTO.getTxnMerchantName());
        recorded.setTxnMerchantCategoryCode(installSettlementLogDTO.getTxnMerchantCategoryCode());
        recorded.setTxnCountryCode(installSettlementLogDTO.getTxnCountryCode());
        recorded.setTxnStateCode(installSettlementLogDTO.getTxnStateCode());
        recorded.setTxnCityCode(installSettlementLogDTO.getTxnCityCode());
        recorded.setTxnReferenceNumber(installSettlementLogDTO.getTxnReferenceNumber());
        recorded.setTxnAuthorizationMatchIndicator(installSettlementLogDTO.getTxnAuthMatchIndicator());
        recorded.setTxnReleaseAuthorizationAmount(installSettlementLogDTO.getTxnReleaseAuthAmount());
        recorded.setTxnLimitNodeId(installSettlementLogDTO.getTxnLimitNodeId());
        recorded.setTxnOutstandingAmount(installSettlementLogDTO.getTxnOutstandingAmount());
        recorded.setTxnOpponentBankNumber(installSettlementLogDTO.getTxnOpponentBankNum());
        recorded.setTxnOpponentAccountNumber(installSettlementLogDTO.getTxnOpponentAccountNum());
        recorded.setTxnOpponentAccountName(installSettlementLogDTO.getTxnOpponentAccountName());
        recorded.setTxnSecondMerchantId(installSettlementLogDTO.getTxnSecondMerchantId());
        recorded.setTxnSecondMerchantName(installSettlementLogDTO.getTxnSecondMerchantName());
        recorded.setTxnPosEntryMode(installSettlementLogDTO.getTxnPosEntryMode());
        recorded.setTxnVisaChargeFlag(installSettlementLogDTO.getTxnVisaChargeFlag());
        recorded.setTxnReimbursementAttribute(installSettlementLogDTO.getTxnReimbursementAttribute());
        recorded.setTxnIfiIndicator(installSettlementLogDTO.getTxnIfiIndicator());
        recorded.setTxnPsvIndicator(installSettlementLogDTO.getTxnPsvIndicator());
        recorded.setTxnDccIndicator(installSettlementLogDTO.getTxnDccIndicator());
        recorded.setTxnForcePostIndicator(installSettlementLogDTO.getTxnForcePostIndicator());
        recorded.setTxnFallBackIndicator(installSettlementLogDTO.getTxnFallBackIndicator());
        recorded.setTxnInstallmentIndicator("1");
        recorded.setTxnInstallmentOrderId(String.valueOf(installSettlementLogDTO.getTxnInstallmentOrderId()));
        recorded.setTxnInstallmentTerm(String.valueOf(installSettlementLogDTO.getTxnInstallmentTerm()));
        recorded.setTxnInterestTableId(installSettlementLogDTO.getTxnInterestTableId());
        recorded.setTxnFeeTableId(installSettlementLogDTO.getTxnFeeTableId());
        //双信息
        recorded.setMessageIndicator(MessageIndicatorEnum.DOUBLE_INDICATOR.getCode());
        return recorded;
    }
    /**
     * 获取清算流水表总数
     *
     * @param partitionKey 分区key
     * @return int
     */
    @Override
    public int getCount(String partitionKey, String organizationNumber) {
        String partitionKey0 = null;
        String partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = partitionKey.split("-")[0];
            partitionKey1 = partitionKey.split("-")[1];
        }
        return installSettlementLogSelfMapper.getCount(partitionKey0,partitionKey1, organizationNumber);
    }
    /**
     * 获取清算流水表ids
     * @param partitionKey 分区key
     * @param rowNumbers rows
     * @return List<String>
     */
    @Override
    public List<String> queryIds(String partitionKey, List<Integer> rowNumbers, String organizationNumber) {
        String partitionKey0 = null;
        String partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = partitionKey.split("-")[0];
            partitionKey1 = partitionKey.split("-")[1];
        }
        return installSettlementLogSelfMapper.queryIds(partitionKey0,partitionKey1,rowNumbers, organizationNumber);
    }

    /**
     * 构建未并账数据
     * @param installSettlementLogDTO 分期清算流水数据
     * @return OutstandingTransactionDTO  未并账数据
     */
    private OutstandingTransactionDTO buildOutstandingTransactionDTO(InstallSettlementLogBO installSettlementLogDTO) {
        OutstandingTransactionDTO outstandingTransactionDTO=new OutstandingTransactionDTO();
//        outstandingTransactionDTO.setAccountManagementId(installSettlementLogDTO.getTxnAccountManageId());
        if (StringUtils.isNotEmpty(installSettlementLogDTO.getTxnAccountManageId())) {
            AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(installSettlementLogDTO.getTxnAccountManageId());
            if(accountManagementInfo != null){
                outstandingTransactionDTO.setOrganizationNumber(accountManagementInfo.getOrganizationNumber());
                outstandingTransactionDTO.setProductNumber(accountManagementInfo.getProductNumber());
                outstandingTransactionDTO.setCustomerId(accountManagementInfo.getCustomerId());
            }
        } else if (StringUtils.isNotEmpty(installSettlementLogDTO.getTxnCardNumber())) {
            String txnCardNumber = installSettlementLogDTO.getTxnCardNumber();
            CardBasicInfo cardBasicInfo = cardBasicInfoMapper.selectByPrimaryKey(txnCardNumber,installSettlementLogDTO.getOrganizationNumber());
            outstandingTransactionDTO.setOrganizationNumber(cardBasicInfo.getOrganizationNumber());
        }

        outstandingTransactionDTO.setCardNumber(installSettlementLogDTO.getTxnCardNumber());
        outstandingTransactionDTO.setGlobalFlowNumber(installSettlementLogDTO.getTxnGlobalFlowNumber());
        outstandingTransactionDTO.setDebitCreditIndcator(DebitCreditIndEnum.DEBIT_INDICATOR.getCode());
        outstandingTransactionDTO.setTransactionSource(installSettlementLogDTO.getTxnTransactionSource());
        outstandingTransactionDTO.setTransactionDescription(installSettlementLogDTO.getTxnTransactionDescription());
        outstandingTransactionDTO.setTransactionRemainDays(0L);
        outstandingTransactionDTO.setTransactionReleaseDate(LocalDate.now());
        outstandingTransactionDTO.setBusinessDate(installSettlementLogDTO.getTxnBillingDate());
        outstandingTransactionDTO.setTransactionDate(installSettlementLogDTO.getTxnTransactionDate().toLocalDate());
        outstandingTransactionDTO.setTransactionTime(installSettlementLogDTO.getTxnTransactionDate());
        outstandingTransactionDTO.setTransactionAmount(installSettlementLogDTO.getTxnTransactionAmount());
        outstandingTransactionDTO.setTransactionCurrencyCode(installSettlementLogDTO.getTxnTransactionCurrency());
        outstandingTransactionDTO.setBillingAmount(installSettlementLogDTO.getTxnBillingAmount());
        outstandingTransactionDTO.setBillingCurrencyCode(installSettlementLogDTO.getTxnBillingCurrency());
        outstandingTransactionDTO.setSettlementAmount(installSettlementLogDTO.getTxnSettlementAmount());
        outstandingTransactionDTO.setSettlementCurrencyCode(installSettlementLogDTO.getTxnSettlementCurrency());
        outstandingTransactionDTO.setAuthorizationCode(installSettlementLogDTO.getTxnAuthorizationCode());
        outstandingTransactionDTO.setMcc(installSettlementLogDTO.getTxnMerchantCategoryCode());
        outstandingTransactionDTO.setCountryCode(installSettlementLogDTO.getTxnCountryCode());
        outstandingTransactionDTO.setMerchantId(installSettlementLogDTO.getTxnMerchantId());
        outstandingTransactionDTO.setMerchantName(installSettlementLogDTO.getTxnMerchantName());
        outstandingTransactionDTO.setPosEntryMode(installSettlementLogDTO.getTxnPosEntryMode());
        outstandingTransactionDTO.setCardSequence(0L);
        outstandingTransactionDTO.setFallbackIndicator(installSettlementLogDTO.getTxnFallBackIndicator());
        outstandingTransactionDTO.setReimbursementAttribute(installSettlementLogDTO.getTxnReimbursementAttribute());
        outstandingTransactionDTO.setOpponentBankId(installSettlementLogDTO.getTxnOpponentBankNum());
        outstandingTransactionDTO.setOpponentAccountNumber(installSettlementLogDTO.getTxnOpponentAccountNum());
        outstandingTransactionDTO.setOpponentAccountName(installSettlementLogDTO.getTxnOpponentAccountName());


        //整笔
        outstandingTransactionDTO.setInstallmentIndicator("1");
        InstallProductInfoResDTO  installProductInfoResDTO = getInstallProductCode(installSettlementLogDTO);
        outstandingTransactionDTO.setInstalmentProduct(installProductInfoResDTO.getProductCode());

        outstandingTransactionDTO.setInstallmentType(installSettlementLogDTO.getTxnInstallmetType());
        outstandingTransactionDTO.setOriginTransactionId(installSettlementLogDTO.getTxnOriginTransId());
        outstandingTransactionDTO.setAcquireReferenceNo(installSettlementLogDTO.getTxnAcquireReferencNo());
        outstandingTransactionDTO.setInstallmentPriceFlag(installSettlementLogDTO.getTxnInstallmentPriceFlag());
        outstandingTransactionDTO.setInstallmentTotalFee(installSettlementLogDTO.getTxnInstallmentTotalFee());
        outstandingTransactionDTO.setInstallmentFeeRate(installSettlementLogDTO.getTxnInstallmentFeeRate());
        outstandingTransactionDTO.setInstallmentDerateMethod(installSettlementLogDTO.getTxnInstallmentDerateMethod());
        outstandingTransactionDTO.setInstallmentDerateValue(installSettlementLogDTO.getTxnInstallmentDerateValue());

        if (StringUtils.isBlank(installSettlementLogDTO.getTxnTransactionDescription())){
            outstandingTransactionDTO.setTransactionDesc(installProductInfoResDTO.getProductDesc());
        }else {
            outstandingTransactionDTO.setTransactionDesc(installSettlementLogDTO.getTxnTransactionDescription());
        }

        outstandingTransactionDTO.setCreditLimitAmount(BigDecimal.ZERO);
        outstandingTransactionDTO.setOnlinePaymentAmount(BigDecimal.ZERO);
        outstandingTransactionDTO.setOverpaymentAmount(BigDecimal.ZERO);
        outstandingTransactionDTO.setPostIndicator(InstallmentConstant.POST_INDICATOR_BATCH);
        outstandingTransactionDTO.setTrasactionStatus(InstallmentConstant.TRASACTION_STATUS_NO);
        outstandingTransactionDTO.setPostFlag(InstallmentConstant.POSTFLAG_NO);
        outstandingTransactionDTO.setCreateTime(LocalDateTime.now());
        outstandingTransactionDTO.setUpdateBy("admin");
        outstandingTransactionDTO.setVersionNumber(1L);
        outstandingTransactionDTO.setTerm(Integer.parseInt(installSettlementLogDTO.getInstalmentPeriod()));
        outstandingTransactionDTO.setInstallmentType(installSettlementLogDTO.getTxnInstallmetType());
        //查分期类型表 获取交易大类 细类
        InstallTypeParmResDTO intallType = installTypeParmService.findByOrgNumAndType(outstandingTransactionDTO.getOrganizationNumber(), installSettlementLogDTO.getTxnInstallmetType());
        outstandingTransactionDTO.setAuthTransactionTypeTop(intallType.getAuthTransactionType());
        outstandingTransactionDTO.setAuthTransactionTypeDetail(intallType.getAuthTransactionTypeDetail());


        //分期入账交易码值
        String transactionParmId = installProductInfoResDTO.getPostingTransactionParmId();
        InstallAccountingTransParmResDTO transParmResDTO = installAccountingTransParmService.selectByIndex(installSettlementLogDTO.getOrganizationNumber(), transactionParmId);
        outstandingTransactionDTO.setPostingTransactionCode(transParmResDTO.getInstallTransactionCode());


        outstandingTransactionDTO.setCustomerRegion(installSettlementLogDTO.getCustomerRegion());
        outstandingTransactionDTO.setOrganizationNumber(installSettlementLogDTO.getOrganizationNumber());

        outstandingTransactionDTO.setIsPostInstallment(Objects.equals(InstallmentTypeEnum.POS_INSTALL.getCode(),
                installProductInfoResDTO.getProdType()) ? 1 : 0);

        outstandingTransactionDTO.setInstallmentPriceFlag(InstallPriceFlagEnum.BASICPRICING_FLAG.getCode());
        outstandingTransactionDTO.setRetrievalReferenceNumber(installSettlementLogDTO.getTxnReferenceNumber());
        return outstandingTransactionDTO;
    }

    private InstallProductInfoResDTO getInstallProductCode(InstallSettlementLogBO installSettlementLogDTO) {

        ParmInstallMerchantMapping installMerchantMapping = parmInstallMerchantMappingMapper.selectByMerchantId(
                installSettlementLogDTO.getTxnMerchantId());

        if (installMerchantMapping != null){

            return installProductInfoService.findByIndex(
                    installSettlementLogDTO.getOrganizationNumber(), installMerchantMapping.getProductCode());

        }

        return installProductInfoService.findProInfoByTermAndType(
                installSettlementLogDTO.getOrganizationNumber(), InstallmentTypeEnum.POS_INSTALL.getCode(),
                InstallmentTypeEnum.POS_INSTALL.getDefaultTerms());
    }



    @Override
    public int deleteAll(String organizationNumber) {
        return installSettlementLogSelfMapper.deleteAll(organizationNumber);
    }

    private InstallProductInfoResDTO getInstallProByOrgAndCode(String organizationNumber, String productCode) {
        InstallProductInfoResDTO result = installProductInfoService.findByIndex(organizationNumber, productCode);
        if (result == null){
            logger.error("根据机构号、产品代码查询分期产品失败");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PRO_PARAM_NOT_EXIST_FAULT);
        }
        return result;
    }
    private InstallAccountingTransParmResDTO getInstallAccountTranByOrgNumAndTableId(String organizationNumber, String tableId) {
        InstallAccountingTransParmResDTO result = installAccountingTransParmService.selectByIndex(organizationNumber, tableId);
        if (result == null){
            logger.error("根据机构号、参数表id查询分期入账交易参数失败");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ACCT_TRANS_PARAM_NOT_EXIST_FAULT);
        }
        return result;
    }
}
