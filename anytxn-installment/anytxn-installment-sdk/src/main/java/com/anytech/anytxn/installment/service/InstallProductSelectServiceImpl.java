package com.anytech.anytxn.installment.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.installment.base.constants.InstallProductSelectRuleConstant;
import com.anytech.anytxn.installment.base.constants.InstallmentConstant;
import com.anytech.anytxn.installment.base.domain.dto.InstallProductSelectRuleDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.installment.base.service.IInstallProductSelectService;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.common.rule.dto.DataInputDTO;
import com.anytech.anytxn.common.rule.matcher.RuleMatcherManager;
import com.anytech.anytxn.common.rule.matcher.TxnRuleMatcher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-03-15
 */
@Service
@Slf4j
public class InstallProductSelectServiceImpl implements IInstallProductSelectService {

    @Autowired
    private IInstallProductInfoService installProductInfoService;

    @Override
    public List<InstallProductInfoResDTO> findByInstallProductSelectRule(InstallProductSelectRuleDTO ruleDTO) {
        if (ruleDTO != null) {
            List<InstallProductInfoResDTO> installProductInfoResDTOS = installProductSelectRuleCheck(ruleDTO);
            if (CollectionUtils.isNotEmpty(installProductInfoResDTOS)){
                return installProductInfoResDTOS;
            }
        }
        return null;
    }

    private List<InstallProductInfoResDTO> installProductSelectRuleCheck(InstallProductSelectRuleDTO ruleDTO) {
        List<InstallProductInfoResDTO> installProductInfoResDTOS = new ArrayList<>(8);
        String ruleType = InstallmentConstant.INSTALL_PRODUCT_SELECT_RULE;
        TxnRuleMatcher ruleMatcher = RuleMatcherManager.getMatcher(ruleType, ruleDTO.getOrganizationNumber());
        if (ruleMatcher == null) {
            log.info("该类型规则为空,ruleType:{}", ruleType);
            return null;
        }
        log.info("规则因子数据:{}", ruleDTO.toString());
        Map<String, Object> inputMap = new HashMap<>(16);
        inputMap.put(InstallProductSelectRuleConstant.CHANNEL, ruleDTO.getChannel());
        inputMap.put(InstallProductSelectRuleConstant.GROUP_TYPE, ruleDTO.getGroupType());
        inputMap.put(InstallProductSelectRuleConstant.PAYMENT_WAY, ruleDTO.getPaymentWay());
        inputMap.put(InstallProductSelectRuleConstant.INSTALLMENT_TYPE, ruleDTO.getInstallmentType());
        inputMap.put(InstallProductSelectRuleConstant.ACTION_INDICATOR, ruleDTO.getActionIndicator());
        inputMap.put(InstallProductSelectRuleConstant.CARD_NUMBER, ruleDTO.getCardNumber());
        inputMap.put(InstallProductSelectRuleConstant.CUSTOMER_ID, ruleDTO.getCustomerId());
        inputMap.put(InstallProductSelectRuleConstant.ID_NUMBER, ruleDTO.getIdNumber());
        inputMap.put(InstallProductSelectRuleConstant.ID_TYPE, ruleDTO.getIdType());
        DataInputDTO dataInput = new DataInputDTO(inputMap, ruleType);
        log.info("分期产品查询规则入参为{}", JSON.toJSONString(inputMap));
        List<Map<String, Object>> maps = ruleMatcher.executeList(dataInput);
        Map<String, Object> execute = ruleMatcher.execute(dataInput);
        InstallProductInfoResDTO byIndex = null;
        if (CollectionUtils.isNotEmpty(maps)) {
            log.info("分期产品查询规则因子匹配结果:{}", JSON.toJSONString(maps));
            for (Map<String, Object> ruleMap : maps){
                Map.Entry<String, Object> entry = ruleMap.entrySet().iterator().next();
                String tableId = String.valueOf(entry.getValue());
                byIndex = installProductInfoService.findByIndex(OrgNumberUtils.getOrg(), tableId);
                installProductInfoResDTOS.add(byIndex);
            }
        } else if (execute != null && !execute.isEmpty()){
            log.info("分期产品查询规则因子匹配结果:{}", JSON.toJSONString(execute));
            Map.Entry<String, Object> entry = execute.entrySet().iterator().next();
            String tableId = String.valueOf(entry.getValue());
            byIndex = installProductInfoService.findByIndex(OrgNumberUtils.getOrg(), tableId);
            installProductInfoResDTOS.add(byIndex);
        }
        return installProductInfoResDTOS;
    }
}
