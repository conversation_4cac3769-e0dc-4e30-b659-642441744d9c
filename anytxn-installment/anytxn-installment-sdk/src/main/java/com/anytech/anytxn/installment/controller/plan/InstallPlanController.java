package com.anytech.anytxn.installment.controller.plan;

import com.anytech.anytxn.installment.base.service.IInstallPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallPlanDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 分期计划查询
 *
 * <AUTHOR>
 * @date 2019/9/2
 */
@RestController
@Api(tags = "分期计划")
public class InstallPlanController extends BizBaseController {
    @Autowired
    private IInstallPlanService installPlanService;

    @ApiOperation(value = "根据订单编号和分期期数查询分期计划表")
    @GetMapping(value = "/install/istallplan/orderId/{orderId}/term/{term}")
    public AnyTxnHttpResponse<InstallPlanDTO> getPlanById(@PathVariable String orderId, @PathVariable Integer term) {
        InstallPlanDTO installPlanDTO = installPlanService.findByIdAndTerm(orderId, term);
        return AnyTxnHttpResponse.success(installPlanDTO);
    }

    @ApiOperation(value = "根据订单编号查询分期计划表")
    @GetMapping(value = "/install/istallplan/orderId/{orderId}")
    public AnyTxnHttpResponse<List<InstallPlanDTO>> getPlanByOptions(@PathVariable(value = "orderId") String orderId) {
        List<InstallPlanDTO> installPlanDtos = installPlanService.planByOrderId(orderId);
        return AnyTxnHttpResponse.success(installPlanDtos);
    }
}
