package com.anytech.anytxn.installment.controller.order;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.common.core.enums.AnyTxnCommonRespCodeEnum;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.installment.base.domain.dto.*;
import com.anytech.anytxn.installment.base.enums.InstallRepDetailEnum;
import com.anytech.anytxn.installment.base.service.IInstallEntryService;
import com.anytech.anytxn.installment.base.service.IInstallNoFinTranServcie;
import com.anytech.anytxn.installment.base.service.IInstallOrderService;
import com.anytech.anytxn.installment.base.service.IInstallRecordService;
import com.anytech.anytxn.installment.service.manager.InstallManager;
import com.anytech.anytxn.installment.base.utils.InstallmentThreadLocalHolder;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.CmBizAuditRecordDTO;
import com.anytech.anytxn.parameter.base.common.enums.AuthStatusEnum;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.CmBizCommitAuditDTO;
import com.anytech.anytxn.parameter.base.authorization.service.ICmBizCommitAuditService;
import com.anytech.anytxn.parameter.base.common.service.audit.IParameterAuditService;
import com.anytech.anytxn.parameter.base.common.enums.ParmStatusEnum;
import com.anytech.anytxn.parameter.base.common.enums.TransactionTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallRecordDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallTrialResDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-05-27 16:06
 **/
@Api(tags = "分期产品录入")
@RestController
public class InstallTransEntryController extends BizBaseController {

    @Autowired
    private IInstallOrderService installOrderService;
    @Autowired
    private IInstallNoFinTranServcie installNoFinTranServcie;
    @Autowired
    private IInstallEntryService installEntryService;
    @Autowired
    private IInstallRecordService installRecordService;

    @Resource
    private InstallManager installManager;

    @Resource
    private ICmBizCommitAuditService iCmBizCommitAuditService;

    @Resource
    private IParameterAuditService iParameterAuditService;



    @PostMapping(value = "/install/order/change")
    public AnyTxnHttpResponse<InstallmentOrderChangeDTO> installmentOrderChange(@Valid @RequestBody InstallmentOrderChangeDTO installmentOrderChangeDTO) {
        Long autoComitVersionNumber = installmentOrderChangeDTO.getVersionNumber();
        Boolean isOpenAuditCommit = iParameterAuditService.openAuditCommit("transaction_audit_commit", OrgNumberUtils.getOrg());
        String commitAuditId = installmentOrderChangeDTO.getCommitAuditId();
        String approveInfo = installmentOrderChangeDTO.getApproveInfo();

        installEntryService.checkInstallOrderChange(installmentOrderChangeDTO);

        InstallmentOrderChangeDTO resp;

        if (isOpenAuditCommit) {
            CmBizAuditRecordDTO build = CmBizAuditRecordDTO.CmBizAuditRecordDTOBuilder.aCmBizAuditRecordDTO()
                    .withCardNumber(installmentOrderChangeDTO.getOriginalCardNumber())
                    .withObject(installmentOrderChangeDTO)
                    .withTransDesc(installManager.getInstallmentProductDesc(installmentOrderChangeDTO.getProductCode()))
                    .withTransactionTypeEnum(TransactionTypeEnum.INSTALLMENT_ORDER_CHANGE)
                    .build();
            String dataString = iCmBizCommitAuditService.handleFirstTransaction(build);
            if (StringUtils.isEmpty(dataString)) {
                return AnyTxnHttpResponse.success(null);
            }

            installmentOrderChangeDTO = JSON.parseObject(dataString).toJavaObject(InstallmentOrderChangeDTO.class);
            installmentOrderChangeDTO.setUpdateBy(LoginUserUtils.getLoginUserName());
        }
        if (isOpenAuditCommit || ParmStatusEnum.isEffective(installmentOrderChangeDTO.getIfAudit())) {
            //多线程CAS
            iCmBizCommitAuditService.auditHandle(commitAuditId, null,null,autoComitVersionNumber);
        }
        resp = installEntryService.orderChange(installmentOrderChangeDTO);
        //执行完成后 更新审批状态
        if (isOpenAuditCommit || ParmStatusEnum.isEffective(installmentOrderChangeDTO.getIfAudit())) {
            CmBizCommitAuditDTO cmBizCommitAuditDTO = iCmBizCommitAuditService.auditDetail(commitAuditId);
            if (cmBizCommitAuditDTO !=null){
                autoComitVersionNumber= cmBizCommitAuditDTO.getVersionNumber();
            }
            //执行完成后 更新审批状态
            iCmBizCommitAuditService.auditHandle(commitAuditId, AuthStatusEnum.HAS_AUTH.getCode(), approveInfo,autoComitVersionNumber);
        }
        return AnyTxnHttpResponse.success(resp);
    }


    @ApiOperation(value = "分期产品录入", notes = "分期产品录入")
    @PostMapping(value = "/install/entry")
    public AnyTxnHttpResponse<InstallEntryResDTO> create(@Valid @RequestBody InstallEntryDTO installEntryDTO) {
        Long oldVersionNum = installEntryDTO.getVersionNumber();

        try {
            installManager.getInstallmentProductDesc(installEntryDTO);
            //是否开启交易审批开关
            Boolean isOpenAuditCommit = iParameterAuditService.openAuditCommit("transaction_audit_commit", OrgNumberUtils.getOrg());
            String commitAuditId = installEntryDTO.getCommitAuditId();
            String approveInfo = installEntryDTO.getApproveInfo();


            InstallEntryResDTO result;
            if ("0".equals(installEntryDTO.getIfAudit())){
                result = installEntryService.entry(installEntryDTO);
            }else {
                if (isOpenAuditCommit || ParmStatusEnum.isEffective(installEntryDTO.getIfAudit())) {
                    //首次提交分期交易，进入审核列表交易记录暂存，审核通过后交易记录落库操作
                    CmBizAuditRecordDTO build = CmBizAuditRecordDTO.CmBizAuditRecordDTOBuilder.aCmBizAuditRecordDTO()
                            .withCardNumber(installEntryDTO.getCardNumber())
                            .withObject(installEntryDTO)
                            .withTransDesc(installEntryDTO.getTransactionDesc())
                            .withTransactionTypeEnum(TransactionTypeEnum.INSTALLMENT_TRANSACTION_ENTRY)
                            .build();
                    String dataString = iCmBizCommitAuditService.handleFirstTransaction(build);
                    if (StringUtils.isEmpty(dataString)) {
                        return AnyTxnHttpResponse.success(null);
                    }
                    installEntryDTO = JSON.parseObject(dataString).toJavaObject(InstallEntryDTO.class);
                }
                if (isOpenAuditCommit || ParmStatusEnum.isEffective(installEntryDTO.getIfAudit())) {
                    //多线程CAS
                    iCmBizCommitAuditService.auditHandle(commitAuditId, null,null,oldVersionNum);
                }

                result = installEntryService.entry(installEntryDTO);

                if (isOpenAuditCommit || ParmStatusEnum.isEffective(installEntryDTO.getIfAudit())) {
                    CmBizCommitAuditDTO cmBizCommitAuditDTO = iCmBizCommitAuditService.auditDetail(commitAuditId);
                    if (cmBizCommitAuditDTO !=null){
                        oldVersionNum= cmBizCommitAuditDTO.getVersionNumber();
                    }
                    //执行完成后 更新审批状态
                    iCmBizCommitAuditService.auditHandle(commitAuditId, AuthStatusEnum.HAS_AUTH.getCode(), approveInfo,oldVersionNum);
                }
            }
            return AnyTxnHttpResponse.success(result, InstallRepDetailEnum.ST_PR.message());
        } finally {
            InstallmentThreadLocalHolder.remove();
        }
    }

    @ApiOperation(value = "分期试算", notes = "分期试算")
    @PostMapping(value = "/install/trial")
    public AnyTxnHttpResponse<InstallTrialResDTO> trialInstallFee(@Valid @RequestBody InstallEntryDTO installEntryDTO) {
        try {
            InstallTrialResDTO installTrialResDTO = installOrderService.trialInstallFee(installEntryDTO);
            return AnyTxnHttpResponse.success(installTrialResDTO);
        } finally {
            InstallmentThreadLocalHolder.remove();
        }
    }

    @ApiOperation(value = "分期交易录入返回订单id", notes = "分期交易录入返回订单id")
    @PostMapping(value = "/install/record")
    public AnyTxnHttpResponse<String> entryInstall(@Valid @RequestBody InstallEntryDTO installEntryDTO) {
        try {
            InstallEntryResDTO installEntryResDTO = installEntryService.entry(installEntryDTO);

            if (StringUtils.isBlank(installEntryResDTO.getOrderId())){
                return AnyTxnHttpResponse.fail(AnyTxnCommonRespCodeEnum.D_ERR.getCode(),AnyTxnCommonRespCodeEnum.D_ERR.getMsg());
            }else {
                return AnyTxnHttpResponse.success(installEntryDTO.getOrderId(), InstallRepDetailEnum.ST_PR.message());
            }

        } finally {
            InstallmentThreadLocalHolder.remove();
        }
    }

    @ApiOperation(value = "账户记录信息查询", notes = "账户记录信息查询")
    @PostMapping(value = "/install/findAccountManagement")
    public AnyTxnHttpResponse<InstallEntryDTO> getAccountManagementRecorded(@RequestBody InstallEntryDTO installEntryDTO) {
        InstallEntryDTO accountManagement = installEntryService.getAccountManagement(installEntryDTO);
        return AnyTxnHttpResponse.success(accountManagement);
    }

    @ApiOperation(value = "单笔可分期交易查询", notes = "单笔可分期交易查询")
    @PostMapping(value = "/install/singleTransInstall")
    public AnyTxnHttpResponse<List<SingleInstallDTO>> getSingleInstallByOrgAndAccountOrCard(@RequestBody(required = false) SingleInstallSearchKeyDTO singleInstallSearchKeyDTO) {
        List<SingleInstallDTO> singleInstalls = installNoFinTranServcie.singleInstallTranQuery(singleInstallSearchKeyDTO);
        return AnyTxnHttpResponse.success(singleInstalls);
    }

    @ApiOperation(value = "分期提前还款及退货、拒付", notes = "分期提前还款及退货、拒付")
    @PutMapping(value = "/install/reimburseOrReturnOrNoPat")
    public AnyTxnHttpResponse<Boolean> reimburseOrReturnOrNoPat(@RequestBody InstallEntryDTO installEntryDTO) {
        Boolean aBoolean = installNoFinTranServcie.reimburseOrRuturnOrNoPat(installEntryDTO);
        return AnyTxnHttpResponse.success(aBoolean);
    }

    @ApiOperation(value = "搜索分期流水", notes = "分页查询")
    @PostMapping(value = "/install/searchRecord")
    public AnyTxnHttpResponse<PageResultDTO<InstallRecordDTO>> getAllByPage(@RequestBody(required = false) InstallRecordSearchKeyDTO installRecordSearchKeyDTO) {
        PageResultDTO<InstallRecordDTO> response = installRecordService.searchPageByKey(installRecordSearchKeyDTO);
        return AnyTxnHttpResponse.success(response);
    }

    @ApiOperation(value = "根据id查询分期流水", notes = "根据id查询分期流水")
    @GetMapping(value = "/install/installRecord/{id}")
    public AnyTxnHttpResponse<InstallRecordDTO> getInstallRecordById(@PathVariable(value = "id") String id) {
        InstallRecordDTO response = installRecordService.getInstallRecordById(id);
        return AnyTxnHttpResponse.success(response);
    }
}
