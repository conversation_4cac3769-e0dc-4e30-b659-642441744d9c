package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.installment.base.constants.InstallmentConstant;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.enums.InstallmentFunctionCodeEnum;
import com.anytech.anytxn.installment.base.enums.RepaymentIndicatorEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.base.authorization.domain.dto.OutstandingTransactionDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.installment.base.service.IInstallOrderEarlierSettleService;
import com.anytech.anytxn.installment.base.service.IInstallOrderManagementService;
import com.anytech.anytxn.parameter.base.account.domain.dto.BlockCodeAccountResDTO;
import com.anytech.anytxn.parameter.base.account.service.IBlockCodeAccountService;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.ProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.product.IProductInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;


/**
 * 分期订单提前结清
 *
 * <AUTHOR>
 * @date 2019-06-06
 **/
@Service
public class InstallOrderEarlierSettleServiceImpl implements IInstallOrderEarlierSettleService {

    private Logger logger = LoggerFactory.getLogger(InstallOrderEarlierSettleServiceImpl.class);

    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;
    @Autowired
    private IInstallOrderManagementService installOrderManagementService;
    @Autowired
    private IProductInfoService productInfoService;
    @Autowired
    private IBlockCodeAccountService blockCodeAccountService;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = AnyTxnInstallException.class)
    public String passiveEarlierSettlement(InstallOrderDTO installOrderDTO) {
        logger.info("分期订单提前结清开始,订单号：[{}]", installOrderDTO.getOrderId());
        AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(installOrderDTO.getAccountManagementId());

        if (accountManagementInfo != null) {
            ProductInfoResDTO accountProductConf = findParamProductInfo(installOrderDTO.getOrganizationNumber(), accountManagementInfo.getProductNumber(), accountManagementInfo.getCurrency());
            BlockCodeAccountResDTO accountBlockCodeConfig = findParamBlockCodeAccount(installOrderDTO.getOrganizationNumber(), accountProductConf.getAccountBlockCodeTableId(), accountManagementInfo.getBlockCode());
            String repaymentIndicator = accountBlockCodeConfig.getRepaymentIndicator();
            if (Objects.equals(RepaymentIndicatorEnum.YES.getCode(), repaymentIndicator)) {
                OutstandingTransactionDTO outstandingTransaction = new OutstandingTransactionDTO();
                int i = installOrderManagementService.installmentOrderManagement(InstallmentFunctionCodeEnum.FORCESETTLEMENT.getCode(), outstandingTransaction, installOrderDTO.getOrderId());
                if (i > 0) {
                    logger.info("分期订单提前结清正常结束,订单号：{}", installOrderDTO.getOrderId());
                    return InstallmentConstant.PASSIVE_EARY_SETTLE_YES;
                } else {
                    logger.error("分期订单提前结清调用分期管理失败，订单号:[{}]", installOrderDTO.getOrderId());
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ORDER_EARLY_SETTLEMENT_FAIL_FAULT);
                }

            } else {
                logger.info("分期订单提前结清正常结束,订单号：{}", installOrderDTO.getOrderId());
                return InstallmentConstant.PASSIVE_EARY_SETTLE_NO;
            }
        }
        return null;
    }

    private BlockCodeAccountResDTO findParamBlockCodeAccount(String organizationNumber, String accountBlockCodeTableId, String blockCode) {
        BlockCodeAccountResDTO result;
        if (StringUtils.isNotBlank(blockCode)) {
            result = blockCodeAccountService.findBlockCodeAccount(organizationNumber, accountBlockCodeTableId, blockCode);
        } else {
            result = blockCodeAccountService.findBlockCodeAccount(organizationNumber, accountBlockCodeTableId, null);
        }
        return result;
    }

    private ProductInfoResDTO findParamProductInfo(String organizationNumber, String productNumber, String currency) {
        ProductInfoResDTO result = productInfoService.findProductInfo(organizationNumber, productNumber, currency).get(0);
        if (result == null) {
            logger.error("根据机构号、产品代码、币种查询产品参数失败");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PRO_PARAM_NOT_EXIST_FAULT);
        }
        return result;
    }
}
