package com.anytech.anytxn.installment.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.installment.base.domain.dto.InstallProductSelectRuleDTO;
import com.anytech.anytxn.installment.base.service.IInstallProductSelectService;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-03-15
 */
@RestController
@Api(tags = "查询分期产品")
public class InstallProductSelectController extends BizBaseController {

    @Autowired
    private IInstallProductSelectService installProductSelectService;

    @ApiOperation(value = "根据分期产品查询规则查询分期产品列表", notes = "根据分期产品查询规则查询分期产品列表")
    @PostMapping(value = "/install/installProductSelectRule")
    public AnyTxnHttpResponse<List<InstallProductInfoResDTO>> getInstallProductByOptions(@RequestBody(required = false) InstallProductSelectRuleDTO installProductSelectRuleDTO) {
        List<InstallProductInfoResDTO> byInstallProductSelectRule = installProductSelectService.findByInstallProductSelectRule(installProductSelectRuleDTO);
        return AnyTxnHttpResponse.success(byInstallProductSelectRule);
    }
}
