package com.anytech.anytxn.installment.controller.order;

import com.anytech.anytxn.installment.base.domain.dto.InstallAutoSignDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallAutoSignSearchKeyDTO;
import com.anytech.anytxn.installment.base.enums.InstallRepDetailEnum;
import com.anytech.anytxn.installment.base.service.IInstallAutoSignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * anytxn-installment
 * 分期自动分期协议
 *
 * <AUTHOR>
 * @date 2019-07-08
 */
@RestController
@Api(tags = "分期自动分期协议")
public class InstallmentAutoSignController extends BizBaseController {

    @Autowired
    private IInstallAutoSignService installAutoSignService;

    @ApiOperation(value = "分页查询分期自动分期协议表", notes = "分页查询分期自动分期协议表")
    @PostMapping(value = "/install/istallautosignbypage")
    public AnyTxnHttpResponse<PageResultDTO<InstallAutoSignDTO>> getInstallmentAutoSignByPage(@RequestBody(required = false) InstallAutoSignSearchKeyDTO installAutoSignSearchKeyDTO) {
        PageResultDTO<InstallAutoSignDTO> pageResultDTO = installAutoSignService.findAll(installAutoSignSearchKeyDTO);
        return AnyTxnHttpResponse.success(pageResultDTO);
    }

    @ApiOperation(value = "添加分期自动分期协议参数", notes = "添加分期自动分期协议参数")
    @PostMapping(value = "/install/istallautosign")
    public AnyTxnHttpResponse<InstallAutoSignDTO> addInstallAutoSign(@RequestBody InstallAutoSignDTO installAutoSignReq) {
        InstallAutoSignDTO installAutoSignDTO = installAutoSignService.addInstallAutoSign(installAutoSignReq);
        return AnyTxnHttpResponse.success(installAutoSignDTO, InstallRepDetailEnum.AS.message());
    }

    @ApiOperation(value = "修改分期自动分期协议参数", notes = "修改分期自动分期协议参数")
    @PutMapping(value = "/install/istallautosign")
    public AnyTxnHttpResponse<InstallAutoSignDTO> modifyInstallAutoSign(@RequestBody InstallAutoSignDTO installAutoSignReq) {
        InstallAutoSignDTO installAutoSignDTO = installAutoSignService.modifyInstallAutoSign(installAutoSignReq);
        return AnyTxnHttpResponse.success(installAutoSignDTO, InstallRepDetailEnum.MS.message());
    }

    @ApiOperation(value = "根据卡号和分期类型删除分期自动分期协议参数", notes = "根据卡号和分期类型删除分期自动分期协议参数")
    @PutMapping("/install/istallautosign/cardNumber/{cardNumber}/autoSignType/{autoSignType}/currencyCode/{currencyCode}")
    public AnyTxnHttpResponse<Boolean> remove(@PathVariable String cardNumber, @PathVariable String autoSignType, @PathVariable String currencyCode) {
        Boolean deleted = installAutoSignService.removeInstallAutoSign(cardNumber, autoSignType,currencyCode);
        return AnyTxnHttpResponse.success(deleted, InstallRepDetailEnum.DS.message());
    }

    @ApiOperation(value = "根据id查询分期自动分期协议参数", notes = "根据id查询分期自动分期协议参数")
    @GetMapping("/install/istallautosign/id/{id}")
    public AnyTxnHttpResponse<InstallAutoSignDTO> getById(@PathVariable("id") String id) {
        InstallAutoSignDTO autoSignServiceById = installAutoSignService.findById(id);
        return AnyTxnHttpResponse.success(autoSignServiceById);
    }
}
