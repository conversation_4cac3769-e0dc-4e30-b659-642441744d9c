package com.anytech.anytxn.installment.controller.auth;

import com.anytech.anytxn.authorization.base.domain.dto.AuthCheckCodeDTO;
import com.anytech.anytxn.authorization.base.domain.dto.ISO8583ReqDTO;
import com.anytech.anytxn.authorization.base.domain.dto.ISO8583ResDTO;
import com.anytech.anytxn.authorization.base.domain.dto.JCBReqDTO;
import com.anytech.anytxn.authorization.base.domain.dto.JCBResDTO;
import com.anytech.anytxn.authorization.controller.AuthController;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.installment.service.manager.InstallmentSmsManager;

import com.anytech.anytxn.notification.domain.dto.upi.OTPGeneResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.apache.ibatis.annotations.Param;
import org.jpos.iso.ISOException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 授权分期请求处理
 * 扩展授权功能
 *
 * <AUTHOR>
 * @version 3.0
 * @date 2020/5/7
 */
@RestController
@Api(tags = "授权分期请求接口，8583请求交易类型为分期类型的请求")
public class AuthInstallmentController extends AuthController {
    @Resource
    private InstallmentSmsManager installmentSmsManager;

    @Override
    @ApiOperation("模拟处理iso8583报文接口")
    @PostMapping({"auth-installment/iso8583"})
    public AnyTxnHttpResponse<ISO8583ResDTO> sendIso8583Analog(@RequestBody ISO8583ReqDTO iso8583Req) throws IOException, ISOException {
        return super.sendIso8583Analog(iso8583Req);
    }

//    @Override
//    @ApiOperation("模拟处理VISA报文接口")
//    @PostMapping({"auth-installment/visa"})
//    public AnyTxnHttpResponse<VISAResDTO> sendVisaAnalog(VISAReqDTO visaReqDTO) throws IOException {
//        return super.sendVisaAnalog(visaReqDTO);
//    }

    /*@Override
    @ApiOperation("万事达报文接口")
    @PostMapping({"auth-installment/masterC"})
    public AnyTxnHttpResponse<MasterCResDTO> sendMastercAnalog(MasterCReqDTO masterCreqDto) throws IOException {
        return super.sendMastercAnalog(masterCreqDto);
    }*/

    @Override
    @ApiOperation("JCB报文接口")
    @PostMapping({"auth-installment/jcb"})
    public AnyTxnHttpResponse<JCBResDTO> sendJcbAnalog(JCBReqDTO jcbReqDTO) throws IOException {
        return super.sendJcbAnalog(jcbReqDTO);
    }

    @Override
    @ApiOperation("授权辅助功能（CVV/CVV2/PIN计算）")
    @PostMapping({"auth-installment/authAssist"})
    public AnyTxnHttpResponse<String> authAssist(AuthCheckCodeDTO authCheckCodeDTO) {
        return super.authAssist(authCheckCodeDTO);
    }
    @ApiOperation(value = "生成,发送opt")
    @GetMapping(value = "auth-installment/sendOtpToEai")
    public AnyTxnHttpResponse<OTPGeneResponseDTO> sendOtpToEai(@RequestParam("cardNumber") String cardNumber, @RequestParam("mobile") String mobile) {
        OTPGeneResponseDTO otpGeneRespDTO = installmentSmsManager.sendOtpToEai(cardNumber, null, mobile);
        return  AnyTxnHttpResponse.success(otpGeneRespDTO);
    }
}
