package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.installment.base.domain.dto.InstallParameterDTO;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.service.IInstallProductEnquiryService;
import com.anytech.anytxn.parameter.base.installment.domain.dto.*;
import com.anytech.anytxn.parameter.base.installment.service.IInstallAccountingTransParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallEarlyTerminationParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallFeeCodeInfoService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallTypeParmService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019-06-13 16:39
 * 分期产品查询
 **/
@Service
public class InstallProductEnquiryServiceImpl implements IInstallProductEnquiryService {
    private Logger logger = LoggerFactory.getLogger(InstallProductEnquiryServiceImpl.class);

    @Autowired
    private IInstallTypeParmService installTypeParmService;
    @Autowired
    private IInstallFeeCodeInfoService installFeeCodeInfoService;
    @Autowired
    private IInstallAccountingTransParmService installAccountingTransParmService;
    @Autowired
    private IInstallProductInfoService installProductInfoService;
    @Autowired
    private IInstallEarlyTerminationParmService installEarlyTerminationParmService ;

    @Override
    public InstallParameterDTO installmentProductEnquiry(String orgNum, String productCode) {

        //获取分期产品信息
        InstallProductInfoResDTO installProInfo = getInstallProByOrgAndCode(orgNum,productCode);
        if (installProInfo == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PRO_PARAM_NOT_EXIST_FAULT);
        }
        //获取分期类型信息
        String prodType = installProInfo.getProdType();
        InstallTypeParmResDTO intallType = getIntallTypeByOrgNumAndType(orgNum, prodType);
        if (intallType == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PLAN_NOT_EXIST_FAULT);
        }
        //获取分期费用代码信息
        String feeCodeId = installProInfo.getFeeCodeId();
        InstallFeeCodeInfoResDTO feeCode = getInstallFeeCodeByOrgNumAndFeeCode(orgNum, feeCodeId);
        if (feeCode == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_FEE_PRAM_NOT_EXIST_FAULT);
        }
        //获取分期入帐参数信息
        String tableId = installProInfo.getPostingTransactionParmId();
        InstallAccountingTransParmResDTO accountTran = getInstallAccountTranByOrgNumAndTableId(orgNum, tableId);
        if (accountTran == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ACCT_TRANS_PARAM_NOT_EXIST_FAULT);
        }
        //获取提前终止入账
        String advancedEndParmId = installProInfo.getAdvancedEndParmId();
        InstallEarlyTerminationParmResDTO installEarlyTermina = getInstallEarlyTerminaByOrgNumAndTableId(orgNum, advancedEndParmId);
        if (installEarlyTermina == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_EARLY_TERMINATE_NOT_EXIST_FAULT);
        }
        InstallParameterDTO installParameterDTO = new InstallParameterDTO();
        installParameterDTO.setInstallProInfo(installProInfo);
        installParameterDTO.setInstallProdType(intallType);
        installParameterDTO.setInstallFeeCode(feeCode);
        installParameterDTO.setInstallAccountTran(accountTran);
        installParameterDTO.setInstallEarlyTermina(installEarlyTermina);
        return installParameterDTO;
    }

    private InstallProductInfoResDTO getInstallProByOrgAndCode(String organizationNumber, String productCode) {
        InstallProductInfoResDTO result = installProductInfoService.findByIndex(organizationNumber, productCode);
        if (result == null){
            logger.error("根据机构号、分期产品代码查询分期产品失败");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PRO_PARAM_NOT_EXIST_FAULT);
        }
        return result;
    }
    private InstallTypeParmResDTO getIntallTypeByOrgNumAndType(String organizationNumber, String type) {
        InstallTypeParmResDTO result = installTypeParmService.findByOrgNumAndType(organizationNumber, type);
        if (result == null){
            logger.error("根据机构号、类型查询分期类型参数失败");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_TYPE_PARAM_NOT_EXIST_FAULT);
        }
        return result;
    }
    private InstallFeeCodeInfoResDTO getInstallFeeCodeByOrgNumAndFeeCode(String organizationNumber, String feeCode) {
        InstallFeeCodeInfoResDTO result = installFeeCodeInfoService.getByIndex(organizationNumber, feeCode);
        if (result == null){
            logger.error("根据机构号、费用代码查询分期费用参数失败");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_FEE_PRAM_NOT_EXIST_FAULT);
        }
        return result;
    }
    private InstallAccountingTransParmResDTO getInstallAccountTranByOrgNumAndTableId(String organizationNumber, String tableId) {
        InstallAccountingTransParmResDTO result = installAccountingTransParmService.selectByIndex(organizationNumber, tableId);
        if (result == null){
            logger.error("根据机构号、参数表id查询分期入账交易参数失败");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ACCT_TRANS_PARAM_NOT_EXIST_FAULT);
        }
        return result;
    }
    private InstallEarlyTerminationParmResDTO getInstallEarlyTerminaByOrgNumAndTableId(String organizationNumber, String tableId) {
        InstallEarlyTerminationParmResDTO result = installEarlyTerminationParmService.findByIndex(organizationNumber, tableId);
        if (result == null){
            logger.error("根据机构号、参数表id查询分期提前终止参数失败");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_EARLY_TERMINATE_NOT_EXIST_FAULT);
        }
        return result;
    }
}
