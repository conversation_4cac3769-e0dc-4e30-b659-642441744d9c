package com.anytech.anytxn.installment.controller.order;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallTrialResDTO;
import com.anytech.anytxn.installment.base.domain.dto.*;
import com.anytech.anytxn.installment.base.service.IInstallBillAppEntryService;
import com.anytech.anytxn.installment.base.service.IInstallOrderAppService;
import com.anytech.anytxn.installment.base.service.IInstallSingleAppEntryService;
import com.anytech.anytxn.installment.base.utils.InstallmentThreadLocalHolder;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.List;
@RestController
public class InstallTransAppManageController extends BizBaseController {
    @Autowired
    private IInstallBillAppEntryService installBillAppEntryService;

    @Autowired
    private IInstallSingleAppEntryService installSingleAppEntryService;

    @Autowired
    private IInstallOrderAppService installOrderAppService;

    @ApiOperation(value = "APP-根据账号查询单笔分期", notes = "APP-根据账号查询单笔分期 R:单笔分期 S:账单分期")
    @GetMapping(value = "/install/searchSingleInstallApp/cardNumber/{cardNumber}/installFlag/{installFlag}")
    public AnyTxnHttpResponse<List<SingleInstallAppDTO>> getSingleInstallApp(@PathVariable String cardNumber, @PathVariable String installFlag) {
        List<SingleInstallAppDTO> singleInstallDtos = installSingleAppEntryService.singleInstallmentApp(cardNumber, installFlag);
        return AnyTxnHttpResponse.success(singleInstallDtos);
    }


    @ApiOperation(value = "APP-单笔分期列表详细查询", notes = "APP-单笔分期列表详细查询")
    @PostMapping(value = "/install/searchSingleInstallListApp")
    public AnyTxnHttpResponse<List<InstallTradingDTO>> getSingleInstallList(@RequestBody(required = false) InstallTradingSearchKeyDTO installTradingSearchKeyDTO) {
        List<InstallTradingDTO> installSingleList = installSingleAppEntryService.findInstallSingleListApp(installTradingSearchKeyDTO);
        return AnyTxnHttpResponse.success(installSingleList);
    }

    @ApiOperation(value = "APP-单笔分期交易录入", notes = "APP-单笔分期交易录入")
    @PostMapping(value = "/install/singleEntryApp")
    public AnyTxnHttpResponse<InstallEntryAppResDTO> singleInstallTransEntry(@Valid @RequestBody InstallEntryAppDTO installEntryAppDTO) {
        InstallEntryAppResDTO installEntryResDTO = installSingleAppEntryService.singleInstallmentApp(installEntryAppDTO);
        return AnyTxnHttpResponse.success(installEntryResDTO);
    }
    @ApiOperation(value = "APP-根据账号查询账单分期", notes = "APP-根据账号查询账单分期")
    @GetMapping(value = "/install/searchBillInstallApp/cardNumber/{cardNumber}")
    public AnyTxnHttpResponse<InstallTradingAppDTO> getBillInstall(@PathVariable String cardNumber) {
        InstallTradingAppDTO installTradingDtos = installBillAppEntryService.findBillInstallApp(cardNumber);
        return AnyTxnHttpResponse.success(installTradingDtos);
    }

    @ApiOperation(value = "APP-账单分期交易录入", notes = "APP-账单分期交易录入")
    @PostMapping(value = "/install/billEntryApp")
    public AnyTxnHttpResponse<InstallEntryAppResDTO> billInstallTransEntry(@Valid @RequestBody InstallEntryAppDTO installEntryAppDTO) {
        InstallEntryAppResDTO billEntry = installBillAppEntryService.billInInstallmentApp(installEntryAppDTO);
        return AnyTxnHttpResponse.success(billEntry);
    }

    @ApiOperation(value = "APP-分期试算", notes = "分期试算")
    @PostMapping(value = "/install/trialApp")
    public AnyTxnHttpResponse<InstallTrialResDTO> trialInstallFee(@Valid @RequestBody InstallEntryDTO installEntryDTO) {
        try {
            InstallTrialResDTO installTrialResDTO = installOrderAppService.trialInstallFeeApp(installEntryDTO);
            return AnyTxnHttpResponse.success(installTrialResDTO);
        } finally {
            InstallmentThreadLocalHolder.remove();
        }
    }
    /*@ApiOperation(value = "APP-分期交易录入走审核流程", notes = "APP-分期产品录入")
    @PostMapping(value = "/install/entryApp")
    public AnyTxnHttpResponse<InstallEntryAppResDTO> entryApp(@Valid @RequestBody InstallEntryAppDTO installEntryAppDTO) {
        InstallEntryAppResDTO installEntryAppResDTO = installOrderService.entryApp(installEntryAppDTO);
        return AnyTxnHttpResponse.success(installEntryAppResDTO, InstallRepDetailEnum.BI_IN_T.message());
    }*/

    @ApiOperation(value = "APP-分期交易明细查询", notes = "已提交审核的分期交易")
    @GetMapping(value = "/install/installmentRecordDetail/cardNumber/{cardNumber}/processStage/{processStage}")
    public AnyTxnHttpResponse<List<InstallAppResDTO>> installmentRecordDetail(@PathVariable String cardNumber, @PathVariable String processStage) {
        List<InstallAppResDTO> installEntryAppResDTOS = installOrderAppService.installmentRecordDetail(cardNumber,processStage);
        return AnyTxnHttpResponse.success(installEntryAppResDTOS);
    }
    @ApiOperation(value = "APP-根据订单id 查询订单表")
    @GetMapping(value = "/install/installOrderApp/orderId/{orderId}")
    public AnyTxnHttpResponse<InstallOrderAppResDTO> getOrderById(@PathVariable(value = "orderId") String orderId) {
        InstallOrderAppResDTO installOrderAppDTO = installOrderAppService.findOrderAppById(orderId);
        return AnyTxnHttpResponse.success(installOrderAppDTO);
    }
}

