package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.installment.base.domain.dto.InstallEntryDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallEntryResDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallTradingDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallTradingSearchKeyDTO;
import com.anytech.anytxn.installment.base.domain.dto.SingleInstallDTO;
import com.anytech.anytxn.installment.base.domain.dto.SingleInstallSearchKeyDTO;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.enums.InstallEntryFunctionCodeEnum;
import com.anytech.anytxn.installment.base.enums.InstallPostFlagEnum;
import com.anytech.anytxn.installment.base.enums.InstallPriceFlagEnum;
import com.anytech.anytxn.installment.base.enums.InstallmentDerateMethodEnum;
import com.anytech.anytxn.business.base.installment.enums.InstallmentTypeEnum;
import com.anytech.anytxn.common.core.utils.DesensitizedUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.monetary.service.CustReconciliationControlServiceImpl;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionMapper;
import com.anytech.anytxn.business.dao.transaction.model.PostedTransaction;
import com.anytech.anytxn.installment.base.constants.InstallmentConstant;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.enums.InstallRepDetailEnum;
import com.anytech.anytxn.installment.base.service.IInstallEntryService;
import com.anytech.anytxn.installment.base.service.IInstallNoFinTranServcie;
import com.anytech.anytxn.installment.base.service.IInstallSingleEntryService;
import com.anytech.anytxn.installment.base.service.IInstallStagingListService;
import com.anytech.anytxn.installment.service.manager.InstallManager;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallTypeSupportTxnResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallTypeSupportTxnService;
import com.anytech.anytxn.parameter.installment.mapper.InstallTypeSupportMccSelfMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallTypeSupportTxnSelfMapper;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeSupportMcc;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeSupportTxn;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 单笔分期业务逻辑实现
 *
 * <AUTHOR>
 * @date 2019/8/19
*/
@Service
public class InstallSingleEntryServiceImpl implements IInstallSingleEntryService {

    private Logger logger = LoggerFactory.getLogger(InstallStagingListServiceImpl.class);

    @Autowired
    private IInstallNoFinTranServcie installNoFinTranServcie;

    @Autowired
    private IInstallStagingListService installStagingListService;

    @Autowired
    private IInstallEntryService installEntryService;

    @Autowired
    private CustReconciliationControlServiceImpl custReconciliationControlService;

    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;

    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private IInstallTypeSupportTxnService installTypeSupportTxnService;
    @Autowired
    private PostedTransactionMapper postedTransactionMapper;
    @Autowired
    private InstallTypeSupportMccSelfMapper installTypeSupportMccSelfMapper;
    @Autowired
    private InstallTypeSupportTxnSelfMapper installTypeSupportTxnSelfMapper;

    @Autowired
    private InstallManager installManager;
    /**
     * 根据账号查询单笔分期
     *
     * @param accountManagementId 管理账户id
     * @return {@link SingleInstallDTO}
     */
    @Override
    public List<SingleInstallDTO> findByAccountManagementId(String accountManagementId, String installFlag) {
        List<SingleInstallDTO> singleInstallDTOList = new ArrayList<>();
        //必输项检查
        if (accountManagementId == null || "".equals(accountManagementId)) {
            logger.error("单笔分期查询 账号不能为空");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_IN_QU);
        }
        SingleInstallSearchKeyDTO singleInstallSearchKeyDTO = new SingleInstallSearchKeyDTO();
        singleInstallSearchKeyDTO.setOrganizationNumber(OrgNumberUtils.getOrg());
        singleInstallSearchKeyDTO.setAccountManagementId(accountManagementId);
        List<SingleInstallDTO> singleInstallDtos = new ArrayList<>();
        if (InstallmentTypeEnum.SINGLE_INSTALL.getCode().equals(installFlag)){
            singleInstallDtos = installNoFinTranServcie.singleInstallTranQuery(singleInstallSearchKeyDTO);
        }
        if (InstallmentTypeEnum.RESET_INSTALL.getCode().equals(installFlag)){
            singleInstallDtos = installNoFinTranServcie.resetInstallTranQuery(InstallmentConstant.RETRIEVAL_REFERENCE_NUMBER, singleInstallSearchKeyDTO.getCardNumber(), accountManagementId);
        }
        List<String> installTypeTransCodes =null;
        try {
            //单笔分期
            if (InstallmentTypeEnum.SINGLE_INSTALL.getCode().equals(installFlag)){
                List<InstallTypeSupportTxnResDTO> installTypeSupportTxns = installTypeSupportTxnService.getByTypeAndOrgNum(InstallmentTypeEnum.SINGLE_INSTALL.getCode(), singleInstallSearchKeyDTO.getOrganizationNumber());
                if (!CollectionUtils.isEmpty(installTypeSupportTxns)){
                    installTypeTransCodes = installTypeSupportTxns.stream().map(InstallTypeSupportTxnResDTO::getTransactionCode).collect(Collectors.toList());
                }
            }
            //重置分期
            if (InstallmentTypeEnum.RESET_INSTALL.getCode().equals(installFlag)){
                List<InstallTypeSupportTxn> installTypeSupportTxns = installTypeSupportTxnSelfMapper.selectAllTypeSupportTxn(singleInstallSearchKeyDTO.getOrganizationNumber());
                if (!CollectionUtils.isEmpty(installTypeSupportTxns)){
                    //交易码筛选条件增加G-分期交易
                    List<InstallTypeSupportTxn> collect = installTypeSupportTxns.stream().filter(x -> InstallmentTypeEnum.SINGLE_INSTALL.getCode().equals(x.getType()) || InstallmentTypeEnum.RESET_INSTALL.getCode().equals(x.getType())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect)){
                        installTypeTransCodes = collect.stream().map(InstallTypeSupportTxn::getTransactionCode).collect(Collectors.toList());
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }

        //不支持分期的MCC
        List<InstallTypeSupportMcc> installTypeMccs = installTypeSupportMccSelfMapper.selectByTypeAndOrgNum(InstallmentTypeEnum.SINGLE_INSTALL.getCode(), singleInstallSearchKeyDTO.getOrganizationNumber());
        List<String> mccList = new ArrayList<>();
        if(installTypeMccs !=null && !installTypeMccs.isEmpty()){
            mccList = installTypeMccs.stream().map(InstallTypeSupportMcc::getMcc).collect(Collectors.toList());
        }

        if (singleInstallDtos.isEmpty()) {
            //如果singleInstallDTOS为空说明该账号已分期
        } else {
            for (SingleInstallDTO s : singleInstallDtos) {
                boolean txnFlag = true;
                if(!CollectionUtils.isEmpty(installTypeTransCodes)){
                    txnFlag = installTypeTransCodes.contains(s.getPostingTransactionCode());
                }
                //只筛选列表返回的已入账交易
                if (Objects.equals(s.getPostFlag(), InstallPostFlagEnum.ALREADY_BOOKED.getCode()) && txnFlag && !mccList.contains(s.getMcc())) {
                    if (InstallmentTypeEnum.RESET_INSTALL.getCode().equals(installFlag)){
                        PostedTransaction postedTransaction = postedTransactionMapper.selectByPrimaryKey(s.getOriginTransactionId());
                        if (!ObjectUtils.isEmpty(postedTransaction)){
                            String retrievalReferenceNumber = postedTransaction.getRetrievalReferenceNumber();
                            if (StringUtils.isNotEmpty(retrievalReferenceNumber) && InstallmentConstant.RETRIEVAL_REFERENCE_NUMBER.equals(retrievalReferenceNumber)){
                                singleInstallDTOList.add(s);
                            }
                        }
                    }else if (InstallmentTypeEnum.SINGLE_INSTALL.getCode().equals(installFlag)){
                        singleInstallDTOList.add(s);
                    }
                }
            }
        }
        return singleInstallDTOList;
    }

    /**
     * 单笔分期列表查询
     * @return InstallTradingDTO
     */
    @Override
    public List<InstallTradingDTO> findInstallSingleList(InstallTradingSearchKeyDTO installTradingSearchKeyDTO) {
        if (installTradingSearchKeyDTO == null) {
            logger.error("单笔分期列表查询 参数不能为空");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_IN_Q);
        }
        //必输项检查
        checkInstallTrading(installTradingSearchKeyDTO);
        installTradingSearchKeyDTO.setOrganizationNumber(installTradingSearchKeyDTO.getOrganizationNumber());
        if (InstallmentTypeEnum.SINGLE_INSTALL.getCode().equals(installTradingSearchKeyDTO.getInstallFlag())){
            installTradingSearchKeyDTO.setType(InstallmentTypeEnum.SINGLE_INSTALL.getCode());
        }
        if (InstallmentTypeEnum.RESET_INSTALL.getCode().equals(installTradingSearchKeyDTO.getInstallFlag())){
            installTradingSearchKeyDTO.setType(InstallmentTypeEnum.RESET_INSTALL.getCode());
        }
        //installTradingSearchKeyDTO.setPaymentWay(InstallPaymentWayEnum.WAIT_FEE.getCode())
        //installTradingSearchKeyDTO.setFeeFlag(InstallFeeReceiveFlagEnum.HIRE_CHARGE.getCode())
        installTradingSearchKeyDTO.setTerm(0);
        installTradingSearchKeyDTO.setInstallAmount(installTradingSearchKeyDTO.getInstallAmount()
                .setScale(2, BigDecimal.ROUND_HALF_UP));

        return installStagingListService.findInstallTradingByOptions(installTradingSearchKeyDTO);
    }

    /**
     * 单笔分期交易录入
     *
     * @param installEntryDTO {@link InstallEntryDTO}
     * @return Long
     */
    @Override
    public InstallEntryResDTO singleInstallment(InstallEntryDTO installEntryDTO) {
        if(logger.isInfoEnabled()){
            logger.info("installEntryDTO:{}", DesensitizedUtils.getJson(installEntryDTO));
        }

        if (installEntryDTO == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_IN_TR);
        }
        //必输项检查
        if (StringUtils.isBlank(installEntryDTO.getAccountManagementId())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SU_IN_MA);
        }

        if (StringUtils.isBlank(installEntryDTO.getProductCode())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.IN_PR);
        }

        if (installEntryDTO.getInstallAmount() == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_IN_AM);
        }

        if (StringUtils.isBlank(installEntryDTO.getOriginTransactionId())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_OR_ID);
        }

        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installEntryDTO.getOrganizationNumber());
        if(organizationInfo == null){
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORG_INFO_PARAM_NOT_EXIST_FAULT);
        }

        String cardNumber = installManager.getCardNumberByAccountManagementId(installEntryDTO);

        AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(installEntryDTO.getAccountManagementId());
        CustReconciliationControlDTO control = custReconciliationControlService.getControl(accountManagementInfo.getCustomerId(), organizationInfo.getOrganizationNumber());
        LocalDate date = custReconciliationControlService.getBillingDate(control, organizationInfo.getAccruedThruDay(), organizationInfo.getToday(), organizationInfo.getNextProcessingDay());
        // 交易日期计算所得
        installEntryDTO.setTransactionDate(date);
        installEntryDTO.setFunctionCode(InstallEntryFunctionCodeEnum.INSTLL_ENTRY.getCode());
        installEntryDTO.setOrganizationNumber(installEntryDTO.getOrganizationNumber());
        installEntryDTO.setInstallType(InstallmentTypeEnum.SINGLE_INSTALL.getCode());
        installEntryDTO.setInstallCcy(accountManagementInfo.getCurrency());
        installEntryDTO.setInstallPriceFlag(InstallPriceFlagEnum.BASICPRICING_FLAG.getCode());
        installEntryDTO.setInstallTotalFee(BigDecimal.ZERO);
        installEntryDTO.setInstallFeeRate(BigDecimal.ZERO);
        installEntryDTO.setInstallDerateMethod(InstallmentDerateMethodEnum.REDUCTION_NO.getCode());
        installEntryDTO.setInstallDerateValue(BigDecimal.ZERO);
        installEntryDTO.setCardNumber(cardNumber);

        installManager.getInstallmentProductDesc(installEntryDTO);

        String transactionId = installEntryDTO.getOriginTransactionId();
        PostedTransaction pt = postedTransactionMapper.selectByPrimaryKey(transactionId);
        String merchantId = pt.getMerchantId();
        installEntryDTO.setMerchantId(merchantId);
        installEntryDTO.setMerchantName(pt.getMerchantName());


        String orderId = installEntryService.entry(installEntryDTO).getOrderId();

        if (StringUtils.isBlank(orderId)){
            logger.error("card number {}  installment failure ",
                    installEntryDTO.getCardNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_BILL_INST_ENTRY_FAIL_FAULT);
        }

        return InstallEntryResDTO.InstallEntryResDTOBuilder.anInstallEntryResDTO()
                .withCardNumber(cardNumber)
                .withOrderId(orderId).build();
    }

    /**
     * 必输项检查
     *
     * @param installTradingSearchKeyDTO {@link InstallTradingSearchKeyDTO}
     */
    private void checkInstallTrading(InstallTradingSearchKeyDTO installTradingSearchKeyDTO) {
        if (StringUtils.isEmpty(installTradingSearchKeyDTO.getOriginTransactionId())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.TR_IN);
        }

        if (StringUtils.isEmpty(installTradingSearchKeyDTO.getAccountManagementId())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_IN_QM);
        }

        if (StringUtils.isEmpty(installTradingSearchKeyDTO.getCardNumber())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_CA);
        }

        if (installTradingSearchKeyDTO.getInstallAmount() == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_AM);
        }
    }
}
