package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.common.core.enums.TransactionSourceEnum;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlamsDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.common.service.PartitionKeyInitService;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallPlanSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.dao.installment.model.InstallPlan;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallPlanDTO;
import com.anytech.anytxn.business.base.transaction.domain.bo.RecordedBO;
import com.anytech.anytxn.business.base.transaction.domain.dto.SettlementLogDTO;
import com.anytech.anytxn.installment.base.domain.bo.AdjustInstallTermBo;
import com.anytech.anytxn.installment.base.constants.InstallmentConstant;
import com.anytech.anytxn.installment.base.domain.dto.InstallParameterDTO;
import com.anytech.anytxn.installment.base.domain.dto.TerminalParameterDTO;
import com.anytech.anytxn.installment.base.enums.*;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.service.IInstallBookPretreatService;
import com.anytech.anytxn.installment.base.service.IInstallNormalAccountingService;
import com.anytech.anytxn.installment.base.service.IInstallOrderTerminatedService;
import com.anytech.anytxn.installment.base.service.IInstallProductEnquiryService;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallFeeCodeInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.transaction.base.service.IGlAmsService;
import com.anytech.anytxn.transaction.base.service.ISettlementLogService;
import com.anytech.anytxn.transaction.base.service.ITxnRecordedService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2019-06-13 16:29
 * 分期订单终止处理
 **/
@Service
public class InstallOrderTerminatedServiceImpl implements IInstallOrderTerminatedService {
    private BigDecimal hundred = new BigDecimal("100");
    private Logger logger = LoggerFactory.getLogger(InstallOrderTerminatedServiceImpl.class);
    @Autowired
    private IInstallProductEnquiryService installProductEnquiryService;
    @Autowired
    private ISettlementLogService settlementLogService;
    @Autowired
    private InstallOrderMapper installOrderMapper;
    @Autowired
    private InstallPlanSelfMapper installPlanSelfMapper;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private IInstallNormalAccountingService installNormalAccountingService;
    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Autowired
    private IInstallBookPretreatService installBookPretreatService;
    @Autowired
    private IInstallProductInfoService installProductInfoService;
    @Autowired
    private ITxnRecordedService txnRecordedService;

    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;

    @Autowired
    private IGlAmsService glAmsService;
    @Resource
    private PartitionKeyInitService partitionKeyInitService;


    private List<String> supportStatus = Arrays.asList(
            InstallmentFunctionCodeEnum.RETURNGOODS.getCode(),
            InstallmentFunctionCodeEnum.EARLYSETTLEMENT.getCode(),
            InstallmentFunctionCodeEnum.FORCESETTLEMENT.getCode(),
            InstallmentFunctionCodeEnum.REFUSEPAYMENT.getCode(),
            InstallmentFunctionCodeEnum.CANCEL.getCode(),
            InstallmentFunctionCodeEnum.SIX.getCode(),
            InstallmentFunctionCodeEnum.SENVEN.getCode(),
            InstallmentFunctionCodeEnum.PART_CANCEL.getCode());



    @Override
    @Transactional(rollbackFor = Exception.class)
    public int installmentOrderTerminated(String status, InstallOrderDTO installOrderDTO) {
        logger.info("开始分期订单终止处理,status:{},订单号：{}",status,installOrderDTO.getOrderId());
        //参数表集合
        InstallParameterDTO installTransEntrys = installProductEnquiryService.installmentProductEnquiry(installOrderDTO.getOrganizationNumber(), installOrderDTO.getProductCode());
        //功能码检查
        if (!supportStatus.contains(status)) {
            logger.error("分期订单终止处理 分期功能码输入错误 status:{}", status);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_STATUS_NOT_CORRECT_FAULT);
        }

        TerminalParameterDTO terminalParameterDTO = null;

        //退货
        if (Objects.equals(InstallmentFunctionCodeEnum.RETURNGOODS.getCode(), status)) {
            terminalParameterDTO = cancelProcess(installOrderDTO, installTransEntrys, status);
        }
        List<InstallPlan> installPlanList = installPlanSelfMapper.selectByOrderIdStatusAndOrgNum(installOrderDTO.getOrderId(),InstallmentConstant.NOT_ACCOUNTING,installOrderDTO.getOrganizationNumber());
        List<InstallPlanDTO> installPlans = BeanMapping.copyList(installPlanList, InstallPlanDTO.class);
        //提前结清处理
        if (Objects.equals(InstallmentFunctionCodeEnum.EARLYSETTLEMENT.getCode(), status)) {
            terminalParameterDTO = settlementProcessing(installOrderDTO, installTransEntrys, status,installPlans.get(0));
        }
        //强制结清处理
        if (Objects.equals(InstallmentFunctionCodeEnum.FORCESETTLEMENT.getCode(), status)) {
            terminalParameterDTO = settlementProcessing(installOrderDTO, installTransEntrys, status,installPlans.get(0));
        }
        //拒付处理
        if (Objects.equals(InstallmentFunctionCodeEnum.REFUSEPAYMENT.getCode(), status)) {
            terminalParameterDTO = cancelProcess(installOrderDTO, installTransEntrys, status);
        }
        //撤销处理
        if (Objects.equals(InstallmentFunctionCodeEnum.CANCEL.getCode(), status)) {
            terminalParameterDTO = cancelProcess(installOrderDTO, installTransEntrys, status);
        }
        //PT-部分提前结清期数缩短
        if (Objects.equals(InstallmentFunctionCodeEnum.SIX.getCode(), status)) {
            terminalParameterDTO = partEarlySettleShortTerm(installOrderDTO, installTransEntrys,installPlans);
        }
        //PP-部分提前结清期数不变
        if (Objects.equals(InstallmentFunctionCodeEnum.SENVEN.getCode(), status)) {
            terminalParameterDTO = partEarlySettleUnchangedTerm(installOrderDTO, installTransEntrys,installPlans);
        }
        //PR-部分退货
        if (Objects.equals(InstallmentFunctionCodeEnum.PART_CANCEL.getCode(), status)) {
            List<InstallPlan> installPlans1 = installPlanSelfMapper.selectPlansByOrderId(installOrderDTO.getOrderId());
            installPlans = BeanMapping.copyList(installPlans1,InstallPlanDTO.class);
            terminalParameterDTO = partCancelProcess(installOrderDTO, installTransEntrys,installPlans);
        }
        if(!Objects.equals(InstallmentFunctionCodeEnum.SIX.getCode(), status)
                && !Objects.equals(InstallmentFunctionCodeEnum.SENVEN.getCode(), status)
                && !Objects.equals(InstallmentFunctionCodeEnum.PART_CANCEL.getCode(), status)){
            //根据批量更新
            installPlanSelfMapper.updateInstallPlanStatusBatch(BeanMapping.copyList(installPlans, InstallPlan.class));
            //更新订单表
            if (terminalParameterDTO != null)
            {
                return  updateOrder(installOrderDTO, terminalParameterDTO);
            }
        }else {
            /*if(Objects.equals(InstallmentFunctionCodeEnum.SENVEN.getCode(), status)){
                installPlanSelfMapper.batchUpdateByOrderId(BeanMapping.copyList(installPlans, InstallPlan.class));
                int size = installPlans.stream().filter(x -> "Y".equals(x.getTermStutus())).collect(Collectors.toList()).size();
                BigDecimal unPostedAmount = installOrderDTO.getUnpostedAmount().subtract(installOrderDTO.getEarlySettleAmount());
                installOrderDTO.setPostedTerm(size);
                installOrderDTO.setUnpostedAmount(unPostedAmount);
                installOrderDTO.setStatus("1");
                updateOrderPartSettle(installOrderDTO, terminalParameterDTO);
            }else {*/
                List<InstallPlan> newInstallPlanList = terminalParameterDTO.getNewInstallPlanList();
                List<InstallPlanDTO> updateInstallPlans = installPlans.stream().filter(x -> "Y".equals(x.getTermStutus())).collect(Collectors.toList());
                if(null !=updateInstallPlans && !updateInstallPlans.isEmpty()){
                    installPlanSelfMapper.updateInstallPlanStatusBatch(BeanMapping.copyList(updateInstallPlans, InstallPlan.class));

                }
                Integer term = installPlans.size();
                if(null != newInstallPlanList && !newInstallPlanList.isEmpty()){
                    //按照term降序
                    term = newInstallPlanList.stream().sorted(Comparator.comparing(InstallPlan::getTerm, Comparator.reverseOrder())).findFirst().get().getTerm();

                    installPlanSelfMapper.deleteGtTermsByOrderId(installOrderDTO.getOrderId(), term);
                    installPlanSelfMapper.batchUpdateByOrderId(newInstallPlanList);
                }
                int alreadyRecordTerm = updateInstallPlans.size();
                //更新订单表
                if (terminalParameterDTO != null)
                {
                    BigDecimal unPostedAmount = newInstallPlanList.stream().filter(x -> "N".equals(x.getTermStutus())).map(InstallPlan::getTermAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    installOrderDTO.setTerm(term);
                    if(Objects.equals(InstallmentFunctionCodeEnum.PART_CANCEL.getCode(), status)){
                      installOrderDTO.setPostedTerm(alreadyRecordTerm);
                    }
                    installOrderDTO.setUnpostedAmount(unPostedAmount);
                    installOrderDTO.setStatus("1");
                    updateOrderPartSettle(installOrderDTO, terminalParameterDTO);
                }
//            }

            Boolean resultFlag = installNormalAccountingService.installNormalAccounting(installOrderDTO);
            if(!resultFlag){
                logger.error("批量调用正常下账出错");
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_BATCH_PROCESSINST_NORMAL_ACCT_FAULT);
            }
            return 1;
        }

        return 0;
    }

    /**
     * 部分提前结清期数缩短
     * @param installOrderDTO
     * @param installTransEntrys
     * @return TerminalParameterDTO
     **/
    private TerminalParameterDTO partEarlySettleShortTerm(InstallOrderDTO installOrderDTO, InstallParameterDTO installTransEntrys, List<InstallPlanDTO> installPlans) {
        //封装数据
        List<SettlementLogDTO> arrayList = new ArrayList<>();
        //累计退货金额
        BigDecimal totalReturnedAmount = installOrderDTO.getTotalReturnedAmount();
        //手续费累计金额
        BigDecimal totalReceivedFee = installOrderDTO.getTotalReceivedFee();
        //累计减免金额
        BigDecimal totalDerateFee = installOrderDTO.getTotalDerateFee();
        //提前结清金额
        BigDecimal earlySettleAmount = installOrderDTO.getEarlySettleAmount();
        //提前结清的剩余期数
//        Integer remainTerm = installOrderDTO.getRemainTerm();
        BigDecimal noRecodeAmount = installPlans.stream().filter(x -> "N".equals(x.getTermStutus())).map(InstallPlanDTO::getTermAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        //剩余总的本金与部分提前结清金额轧差
        BigDecimal restAmount = noRecodeAmount.subtract(earlySettleAmount);
        //本金处理
        //借记部分
        /*for (InstallPlanDTO installPlan : installPlans) {
            if("N".equals(installPlan.getTermStutus())){
                BigDecimal termAmount = installPlan.getTermAmount();
                if(earlySettleAmount.compareTo(termAmount) >=0){
                    earlySettleAmount = earlySettleAmount.subtract(termAmount);
                    installPlan.setTermStutus("Y");
                }else {
                    break;
                }
            }
        }*/

        SettlementLogDTO debit = partPrincipalDebit(installOrderDTO, installTransEntrys,earlySettleAmount);
        installNormalAccountingService.editSettlementLogAbsForPrincipal(installPlans.get(0),debit);
        addSettlementLog(arrayList,debit);

        TerminalParameterDTO terminalParameterDTO = new TerminalParameterDTO();

        settlementLogService.addBatch(arrayList);
        terminalParameterDTO.setTotalReturnedAmount(totalReturnedAmount);
        terminalParameterDTO.setTotalReceivedFee(totalReceivedFee);


        //重新生成分期订单

        installOrderDTO.setInstallmentAmount(restAmount);

        Integer remainTerm = 0;
        List<InstallPlan> newInstallPlanList = new ArrayList<>();
        for (InstallPlanDTO installPlan : installPlans) {
            if("N".equals(installPlan.getTermStutus()) && restAmount.compareTo(BigDecimal.ZERO) > 0){
                BigDecimal termAmount = installPlan.getTermAmount();
                if(restAmount.compareTo(termAmount) >=0){
                    restAmount = restAmount.subtract(termAmount);
                    remainTerm = remainTerm + 1;
                    newInstallPlanList.add(BeanMapping.copy(installPlan,InstallPlan.class));
                }else {
                    installPlan.setTermAmount(restAmount);
                    restAmount = BigDecimal.ZERO;
                    remainTerm = remainTerm + 1;
                    newInstallPlanList.add(BeanMapping.copy(installPlan,InstallPlan.class));
                    break;
                }
            }
        }
        int term = installOrderDTO.getPostedTerm() + remainTerm;
        BigDecimal derateFee = installPlans.stream().filter(x -> x.getTerm().compareTo(term) > 0).map(InstallPlanDTO::getFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        installOrderDTO.setTerm(term);
        installOrderDTO.setUnpostedFeeAmount(installOrderDTO.getUnpostedFeeAmount().subtract(derateFee));
        terminalParameterDTO.setTotalDerateFee(totalDerateFee.add(derateFee));
       /* if(restAmount.compareTo(BigDecimal.ZERO) >0){
            BigDecimal newEachAmount = restAmount.divide(BigDecimal.valueOf(remainTerm), RoundingMode.HALF_UP);
            BigDecimal newOtherAmount = restAmount.subtract(newEachAmount.multiply(BigDecimal.valueOf(remainTerm).subtract(BigDecimal.ONE)));
            List<InstallPlanDTO> noAlreadyInstallPlans = installPlans.stream().filter(x -> "N".equals(x.getTermStutus())).collect(Collectors.toList());
            List<InstallPlan> newInstallPlanList = new ArrayList<>();
            for(int i=0;i<remainTerm; i++){
                InstallPlanDTO installPlanDTO = noAlreadyInstallPlans.get(i);
                if(remainTerm.compareTo(i+1)==0){
                    installPlanDTO.setTermAmount(newOtherAmount);
                }else {
                    installPlanDTO.setTermAmount(newEachAmount);
                }
                newInstallPlanList.add(BeanMapping.copy(installPlanDTO,InstallPlan.class));
            }
            terminalParameterDTO.setNewInstallPlanList(newInstallPlanList);
        }*/
        terminalParameterDTO.setNewInstallPlanList(newInstallPlanList);
        return terminalParameterDTO;
    }

    /**
     * 部分提前结清期数不变
     * @param installOrderDTO
     * @param installTransEntrys
     * @param installPlans
     * @return TerminalParameterDTO
     **/
    private TerminalParameterDTO partEarlySettleUnchangedTerm(InstallOrderDTO installOrderDTO, InstallParameterDTO installTransEntrys,List<InstallPlanDTO> installPlans) {
        //封装数据
        List<SettlementLogDTO> arrayList = new ArrayList<>();
        //累计退货金额
        BigDecimal totalReturnedAmount = installOrderDTO.getTotalReturnedAmount();
        //手续费累计金额
        BigDecimal totalReceivedFee = installOrderDTO.getTotalReceivedFee();
        //累计减免金额
        BigDecimal totalDerateFee = installOrderDTO.getTotalDerateFee();
        //本金处理
        //借记部
        //提前结清金额
        BigDecimal earlySettleAmount = installOrderDTO.getEarlySettleAmount();


        SettlementLogDTO debit = partPrincipalDebit(installOrderDTO, installTransEntrys,earlySettleAmount);
        installNormalAccountingService.editSettlementLogAbsForPrincipal(installPlans.get(0),debit);
        addSettlementLog(arrayList,debit);




        List<InstallPlanDTO> noAlreadyInstallPlans = installPlans.stream().filter(x -> "N".equals(x.getTermStutus())).collect(Collectors.toList());
        BigDecimal noRecodeAmount = noAlreadyInstallPlans.stream().map(InstallPlanDTO::getTermAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal restAmount = noRecodeAmount.subtract(earlySettleAmount);
        int remainTerm = noAlreadyInstallPlans.size();
        BigDecimal newEachAmount = restAmount.divide(BigDecimal.valueOf(remainTerm), RoundingMode.HALF_UP);
        BigDecimal newOtherAmount = restAmount.subtract(newEachAmount.multiply(BigDecimal.valueOf(remainTerm).subtract(BigDecimal.ONE)));
        List<InstallPlan> newInstallPlanList = new ArrayList<>();
        for(int i=0;i<remainTerm; i++){
            InstallPlanDTO installPlanDTO = noAlreadyInstallPlans.get(i);
            if(remainTerm == (i+1)){
                installPlanDTO.setTermAmount(newOtherAmount);
            }else {
                installPlanDTO.setTermAmount(newEachAmount);
            }
            newInstallPlanList.add(BeanMapping.copy(installPlanDTO,InstallPlan.class));
        }

        //交易金额累加到本次手续费入账金额
        TerminalParameterDTO terminalParameterDTO = new TerminalParameterDTO();
        settlementLogService.addBatch(arrayList);
        terminalParameterDTO.setTotalReturnedAmount(totalReturnedAmount);
        terminalParameterDTO.setTotalReceivedFee(totalReceivedFee);
        terminalParameterDTO.setTotalDerateFee(totalDerateFee);
        terminalParameterDTO.setNewInstallPlanList(newInstallPlanList);
        return terminalParameterDTO;
    }

    /**
     * 退货、拒付、撤销
     * @param installOrderDTO
     * @param installTransEntrys
     * @param status
     * @return TerminalParameterDTO
     **/
    private TerminalParameterDTO cancelProcess(InstallOrderDTO installOrderDTO, InstallParameterDTO installTransEntrys, String status ) {
        //封装数据
        List<SettlementLogDTO> arrayList = new ArrayList<>();
        //累计退货金额
        BigDecimal totalReturnedAmount = installOrderDTO.getTotalReturnedAmount();
        //手续费累计金额
        BigDecimal totalReceivedFee = installOrderDTO.getTotalReceivedFee();
        //累计减免金额
        BigDecimal totalDerateFee = installOrderDTO.getTotalDerateFee();
        //本金处理  写清算流水表
        //借记部分
        SettlementLogDTO debit = principalDebit(installOrderDTO, installTransEntrys);
        editSettlementLogAbsForCancel(debit);
        addSettlementLog(arrayList,debit);
        //交易金额(订单表未入账本金UNPOSTED_AMOUNT)累加到本次退货金额
        totalReturnedAmount = totalReturnedAmount.add(installOrderDTO.getInstallmentAmount());
        //账单分期、自动账单分期不需要贷记交易
        if(!Objects.equals(InstallmentTypeEnum.STATEMENT_INSTALL.getCode(), installOrderDTO.getType())
                &&!Objects.equals(InstallmentTypeEnum.AUTO_INSTALL.getCode(), installOrderDTO.getType())){
            //贷记部分
            SettlementLogDTO credit = principalcredit(installOrderDTO, installTransEntrys);
            editSettlementLogAbsForCancel(credit);
            addSettlementLog(arrayList,credit);
        }
        //费用处理
        //取分期提前终止参数表退货手续费处理方式
        String returnFeeFlag = installTransEntrys.getInstallEarlyTermina().getReturnFeeFlag();
        String dishonourFeeFlag = installTransEntrys.getInstallEarlyTermina().getDishonourFeeFlag();
        String reversalFeeFlag = installTransEntrys.getInstallEarlyTermina().getReversalFeeFlag();


        if (Objects.equals(InstallmentFunctionCodeEnum.REFUSEPAYMENT.getCode(), status)){
            returnFeeFlag = dishonourFeeFlag;
        }

        if (Objects.equals(InstallmentFunctionCodeEnum.CANCEL.getCode(), status)){
            returnFeeFlag = reversalFeeFlag;
        }
        //如果为全收
        if (Objects.equals(InstallFeeFlagEnum.FULL_ACCEPTANCE.getCode(), returnFeeFlag)) {
            SettlementLogDTO full = fullAcceptance(installOrderDTO, installTransEntrys, InstallmentConstant.INSTALLRETURNFEE);
            //交易金额()累加到本次手续费累计金额；
            totalReceivedFee = totalReceivedFee.add(installOrderDTO.getUnpostedFeeAmount());
            editSettlementLogAbsForCancel(full);
            addSettlementLog(arrayList,full);
        }
        //全不收
        if (Objects.equals(InstallFeeFlagEnum.NOT_ALL.getCode(), returnFeeFlag)) {
            SettlementLogDTO noAllDebit = notAllDebit(installOrderDTO, installTransEntrys, InstallmentConstant.INSTALLRETURNFEE);
            editSettlementLogAbsForCancel(noAllDebit);
            addSettlementLog(arrayList,noAllDebit);
            //交易金额(分期订单表UNPOSTED_FEE_AMOUNT)累加到本次手续费累计金额；
            totalReceivedFee = totalReceivedFee.add(installOrderDTO.getUnpostedFeeAmount());
            //贷记部分
            SettlementLogDTO noAllCredit = notAllCredit(installOrderDTO, installTransEntrys, InstallmentConstant.INSTALLRETURNFEE);
            editSettlementLogAbsForCancel(noAllCredit);
            addSettlementLog(arrayList,noAllCredit);
            BigDecimal charge = installOrderDTO.getUnpostedFeeAmount().add(installOrderDTO.getTotalReceivedFee()).subtract(totalDerateFee);
            //交易金额累加到本次手续费贷调金额
            totalDerateFee = totalDerateFee.add(charge);
        }



        TerminalParameterDTO terminalParameterDTO = new TerminalParameterDTO();
        //收部分
        if (Objects.equals(InstallFeeFlagEnum.RECEIVING_PART.getCode(), returnFeeFlag)){
            terminalParameterDTO.setPostedFeeTermFlag(InstallmentConstant.POSTEDFEETERMFLAG);
        }
        terminalParameterDTO.setTotalReturnedAmount(totalReturnedAmount);
        terminalParameterDTO.setTotalReceivedFee(totalReceivedFee);
        terminalParameterDTO.setTotalDerateFee(totalDerateFee);
        //交易渠道
        String transactionChanne = installOrderDTO.getTransactionChannel();
        boolean flag = (InstallmentFunctionCodeEnum.RETURNGOODS.getCode().equals(status) || InstallmentFunctionCodeEnum.REFUSEPAYMENT.getCode().equals(status))
                && Objects.equals(TransactionChannelEnum.TRANS_MODIFY.getCode(), transactionChanne);
        if (flag) {
            SettlementLogDTO returnGoods = clearingAccount(installOrderDTO, installTransEntrys, terminalParameterDTO);
            editSettlementLogAbsForCancel(returnGoods);
            addSettlementLog(arrayList,returnGoods);
        }
        settlementLogService.addBatch(arrayList);
        return terminalParameterDTO;

    }

    /**
     * 结清处理
     * @param installOrderDTO
     * @param installTransEntrys
     * @param status
     * @return TerminalParameterDTO
     **/
    private TerminalParameterDTO settlementProcessing(InstallOrderDTO installOrderDTO, InstallParameterDTO installTransEntrys, String status, InstallPlanDTO installPlan) {
        //订单封装
        installTransEntrys.setInstallOrder(installOrderDTO);
        //封装数据
        List<SettlementLogDTO> arrayList = new ArrayList<>();
        //累计退货金额
        BigDecimal totalReturnedAmount = installOrderDTO.getTotalReturnedAmount();
        //手续费累计金额
        BigDecimal totalReceivedFee = installOrderDTO.getTotalReceivedFee();
        //累计减免金额
        BigDecimal totalDerateFee = installOrderDTO.getTotalDerateFee();
        //提前结清手续费处理方式
        String prepaymentFeeFlag = installTransEntrys.getInstallEarlyTermina().getPrepaymentFeeFlag();
        String forcePrepaymentFeeFlag = installTransEntrys.getInstallEarlyTermina().getForcePrepaymentFeeFlag();

        if (Objects.equals(InstallmentFunctionCodeEnum.FORCESETTLEMENT.getCode(), status)){
            prepaymentFeeFlag = forcePrepaymentFeeFlag;
        }

        InstallFeeCodeInfoResDTO installFeeCode = installTransEntrys.getInstallFeeCode();

        //如果利息计算方式为 T F, 则将费用的金额合并到本金中
        if (StringUtils.equalsAny(installFeeCode.getChargeOption(), ChargeOptionEnum.T_TERM.getCode(),
                ChargeOptionEnum.FLAT_RATE.getCode())) {

            //本金部分
            SettlementLogDTO principalSettle = principalDebit(installOrderDTO, installTransEntrys);
            principalSettle.setTxnReferenceNumber(InstallmentConstant.RETRIEVAL_REFERENCE_NUMBER);
            principalSettle.setTxnInstallmentTerm(String.valueOf(installPlan.getTerm()));
            principalSettle.setTxnTransactionDescription(principalSettle.getTxnTransactionDescription() + " FULL ");
            installNormalAccountingService.editSettlementLogAbsForPrincipal(installPlan,principalSettle);

            if (Objects.equals(InstallFeeFlagEnum.FULL_ACCEPTANCE.getCode(), prepaymentFeeFlag)) {

                logger.info("分期订单{},费用全收",installOrderDTO.getOrderId());
                principalSettle.setTxnBillingAmount(principalSettle.getTxnBillingAmount().add(installOrderDTO.getUnpostedFeeAmount()));
                principalSettle.setTxnTransactionAmount(principalSettle.getTxnTransactionAmount().add(installOrderDTO.getUnpostedFeeAmount()));
                totalReceivedFee = totalReceivedFee.add(installOrderDTO.getUnpostedFeeAmount());
                addSettlementLog(arrayList,principalSettle);

            }else if (Objects.equals(InstallFeeFlagEnum.NOT_ALL.getCode(), prepaymentFeeFlag)){

                principalSettle.setTxnBillingAmount(principalSettle.getTxnBillingAmount().add(installOrderDTO.getUnpostedFeeAmount()));
                principalSettle.setTxnTransactionAmount(principalSettle.getTxnTransactionAmount().add(installOrderDTO.getUnpostedFeeAmount()));
                totalReceivedFee = totalReceivedFee.add(installOrderDTO.getUnpostedFeeAmount());
                addSettlementLog(arrayList,principalSettle);

                //分期订单全不收, 需要将已收取的费用金额贷调
                SettlementLogDTO noAllCredit = notAllCredit(installOrderDTO, installTransEntrys, InstallmentConstant.HANDFEEADVANCE);
                installNormalAccountingService.editSettlementLogAbsForFee(installPlan,noAllCredit,installTransEntrys);
                addSettlementLog(arrayList,noAllCredit);

                BigDecimal charge = installOrderDTO.getUnpostedFeeAmount().add(installOrderDTO.getTotalReceivedFee()).subtract(totalDerateFee);
                //交易金额累加到本次手续费贷调金额
                totalDerateFee = totalDerateFee.add(charge);
            }else {
                //指收本金 费用忽略
                addSettlementLog(arrayList,principalSettle);
            }
        }else {
            //本金处理
            //借记部分
            SettlementLogDTO debit = principalDebit(installOrderDTO, installTransEntrys);
            debit.setTxnReferenceNumber(InstallmentConstant.RETRIEVAL_REFERENCE_NUMBER);
            debit.setTxnInstallmentTerm(String.valueOf(installPlan.getTerm()));
            debit.setTxnTransactionDescription(debit.getTxnTransactionDescription() + " FULL ");
            installNormalAccountingService.editSettlementLogAbsForPrincipal(installPlan,debit);
            addSettlementLog(arrayList,debit);

            //费用处理
            //如果为全收
            if (Objects.equals(InstallFeeFlagEnum.FULL_ACCEPTANCE.getCode(), prepaymentFeeFlag)) {
                SettlementLogDTO full = fullAcceptance(installOrderDTO, installTransEntrys, InstallmentConstant.HANDFEEADVANCE);
                installNormalAccountingService.editSettlementLogAbsForFee(installPlan,full,installTransEntrys);
                addSettlementLog(arrayList,full);
                //交易金额(分期订单表UNPOSTED_FEE_AMOUNT)累加到本次手续费入账金额
                totalReceivedFee = totalReceivedFee.add(installOrderDTO.getUnpostedFeeAmount());
            }
            //全不收
            if (Objects.equals(InstallFeeFlagEnum.NOT_ALL.getCode(), prepaymentFeeFlag)) {
                SettlementLogDTO noAllDebit = notAllDebit(installOrderDTO, installTransEntrys, InstallmentConstant.HANDFEEADVANCE);
                installNormalAccountingService.editSettlementLogAbsForFee(installPlan,noAllDebit,installTransEntrys);
                addSettlementLog(arrayList,noAllDebit);

                //交易金额(分期订单表UNPOSTED_FEE_AMOUNT)累加到本次手续费入账金额
                totalReceivedFee = totalReceivedFee.add(installOrderDTO.getUnpostedFeeAmount());
                //贷记部分
                SettlementLogDTO noAllCredit = notAllCredit(installOrderDTO, installTransEntrys, InstallmentConstant.HANDFEEADVANCE);
                installNormalAccountingService.editSettlementLogAbsForFee(installPlan,noAllCredit,installTransEntrys);
                addSettlementLog(arrayList,noAllCredit);

                BigDecimal charge = installOrderDTO.getUnpostedFeeAmount().add(installOrderDTO.getTotalReceivedFee()).subtract(totalDerateFee);
                //交易金额累加到本次手续费贷调金额
                totalDerateFee = totalDerateFee.add(charge);
            }
        }


        TerminalParameterDTO terminalParameterDTO = new TerminalParameterDTO();
        //收部分
        if (Objects.equals(InstallFeeFlagEnum.RECEIVING_PART.getCode(), prepaymentFeeFlag)){
            terminalParameterDTO.setPostedFeeTermFlag(InstallmentConstant.POSTEDFEETERMFLAG);
        }
        //违约金处理
        String feeFlag = installOrderDTO.getFeeFlag();
        //提前结清, 收取违约金
        //if (!Objects.equals(FeeReceiveFagEnum.ONE_TIME.getCode(), feeFlag)) {
            SettlementLogDTO penalty = liquidatedDamages(installOrderDTO, installTransEntrys, status, terminalParameterDTO);
            if(penalty != null){
                installNormalAccountingService.editSettlementLogAbsForFee(installPlan,penalty,installTransEntrys);
                addSettlementLog(arrayList,penalty);
            }
        //}


        carryOver(installTransEntrys, installOrderDTO, installPlan);


        settlementLogService.addBatch(arrayList);
        terminalParameterDTO.setTotalReturnedAmount(totalReturnedAmount);
        terminalParameterDTO.setTotalReceivedFee(totalReceivedFee);
        terminalParameterDTO.setTotalDerateFee(totalDerateFee);
        return terminalParameterDTO;
    }

    /**
     * 结转处理
     *
     */
    private void carryOver(InstallParameterDTO installTransEntrys,
                           InstallOrderDTO installOrderDTO,
                           InstallPlanDTO installPlan) {

        List<InstallPlan> installPlans = installPlanSelfMapper.selectPlansByOrderId(installOrderDTO.getOrderId());

        BigDecimal reduce = installPlans.stream()
                .filter(e -> Objects.equals(e.getTermStutus(), "N"))
                .map(InstallPlan::getAmortizeInterest)
                .reduce(BigDecimal.ZERO, BigDecimal::add);


        if (reduce.compareTo(BigDecimal.ZERO) <= 0){
            return;
        }


        String amortizeTransactionCode = installTransEntrys.getInstallAccountTran().getInterestAmortizeTransactionCode();

        AccountManagementInfo accountManagementInfo = accountManagementInfoSelfMapper.selectFinanceStatus(
                installOrderDTO.getAccountManagementId());

        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installOrderDTO.getOrganizationNumber());

        AccountantGlamsDTO accountantGlamsDTO = new AccountantGlamsDTO();
        accountantGlamsDTO.setAccountManagementId(installOrderDTO.getAccountManagementId());
        accountantGlamsDTO.setAcctLogo(accountManagementInfo.getProductNumber());
        accountantGlamsDTO.setCrdNumber(installOrderDTO.getCardNumber());
        accountantGlamsDTO.setPostingDate(organizationInfo.getNextProcessingDay());
        accountantGlamsDTO.setTxnSource(TransactionSourceEnum.LOCAL.getCode());
        accountantGlamsDTO.setTxnInd("4");
        accountantGlamsDTO.setCurrencyRate("1");


        accountantGlamsDTO.setTxnAmt(reduce);
        accountantGlamsDTO.setTxnCurrency(installOrderDTO.getInstallmentCcy());

        accountantGlamsDTO.setPostingAmt(reduce);
        accountantGlamsDTO.setPostingCurrencyCode(installOrderDTO.getInstallmentCcy());

        accountantGlamsDTO.setSettleAmt(reduce);
        accountantGlamsDTO.setSettleCurrency(installOrderDTO.getInstallmentCcy());

        accountantGlamsDTO.setTxnCode(amortizeTransactionCode);
        accountantGlamsDTO.setTxnCodeOrig(amortizeTransactionCode);

        glAmsService.buildInstallmentGlAms(accountantGlamsDTO,installOrderDTO);
    }




    private void addSettlementLog(List<SettlementLogDTO> arrayList, SettlementLogDTO settlementLog){
        if (settlementLog.getTxnBillingAmount().compareTo(BigDecimal.ZERO) > 0){
            arrayList.add(settlementLog);
        }
    }

    private void addSumAmountSettlementLog(List<SettlementLogDTO> arrayList, SettlementLogDTO settlementLog,InstallParameterDTO installTransEntrys){
        InstallFeeCodeInfoResDTO installFeeCode = installTransEntrys.getInstallFeeCode();
        InstallOrderDTO installOrder = installTransEntrys.getInstallOrder();

        if (settlementLog.getTxnBillingAmount().compareTo(BigDecimal.ZERO) > 0){
            //F T 金额合并
            if (StringUtils.equalsAny(installFeeCode.getChargeOption(), ChargeOptionEnum.T_TERM.getCode(), ChargeOptionEnum.FLAT_RATE.getCode())){

                BigDecimal sumAmountAndFee = installOrder.getUnpostedAmount().add(installOrder.getUnpostedFeeAmount())
                        .subtract(Optional.ofNullable(installOrder.getTotalDerateFee()).orElse(BigDecimal.ZERO))
                        .subtract(Optional.ofNullable(installOrder.getPrincipalDerateTotalAmount()).orElse(BigDecimal.ZERO));
                settlementLog.setTxnTransactionAmount(sumAmountAndFee);
                settlementLog.setTxnBillingAmount(sumAmountAndFee);
                settlementLog.setTxnSettlementAmount(sumAmountAndFee);
                arrayList.add(settlementLog);
            }else {
                arrayList.add(settlementLog);
            }

        }
    }

    private void editSettlementLogAbsForCancel(SettlementLogDTO settlementLog){
        settlementLog.setAbsStatus("N");
        settlementLog.setBalFlag("0");
    }
    /**
     * 清算流水赋值
     * @param installOrderDTO 分期订单
     * @return SettlementLogDTO
     **/
    public SettlementLogDTO getSettlementLog(InstallOrderDTO installOrderDTO) {
        SettlementLogDTO settlementLog = new SettlementLogDTO();
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installOrderDTO.getOrganizationNumber());
        //入账方式 1=批量入账
        //因为批量入账移到日切前了,需要赋值入账方式为实时入账
        settlementLog.setTxnPostMethod(PostMethodEnum.REAL_TIME.getCode());
        //拒绝重入账标志 0=普通交易
        settlementLog.setTxnRepostFromSuspend(RepostFromSuspendEnum.NORMAL_TRANS.getCode());
        //冲减交易费用标识
        settlementLog.setTxnReverseFeeIndicator(InstallmentConstant.REVERSE_FEE_INDICATOR_NO);
        //交易来源
        settlementLog.setTxnTransactionSource(TransactionSourceEnum.LOCAL.getCode());
        //授权匹配标志
        settlementLog.setTxnAuthMatchIndicator(AuthMatchIndicatorEnum.NOT_MATCH_AUTH.getCode());
        //是否恢复授权占用额度标志
        settlementLog.setTxnReleaseAuthAmount(ReleaseAuthAmountEnum.RECOVER.getCode());
        settlementLog.setTxnVisaChargeFlag("");
        settlementLog.setTxnReimbursementAttribute("");
        settlementLog.setTxnIfiIndicator("");
        settlementLog.setTxnPsvIndicator("");
        settlementLog.setTxnDccIndicator("");
        settlementLog.setTxnForcePostIndicator("");
        settlementLog.setTxnFallBackIndicator("");
        //分期标识 1-整笔 2-单笔
        settlementLog.setTxnInstallmentIndicator("1");
        settlementLog.setTxnStateCode("");
        settlementLog.setTxnPosEntryMode("");
        settlementLog.setTxnCountryCode("");
        settlementLog.setTxnAuthorizationCode("");
        settlementLog.setTxnLimitNodeId("");
        settlementLog.setTxnInterestTableId("");
        settlementLog.setTxnFeeTableId("");
        settlementLog.setTxnMerchantId("");
        settlementLog.setTxnCardNumber(installOrderDTO.getCardNumber());
        settlementLog.setTxnAccountManageId(installOrderDTO.getAccountManagementId());
        settlementLog.setTxnParentTxnAccountId("");
        settlementLog.setTxnOriginalTxnBalanceId("");
        //全局业务流水号
        settlementLog.setTxnGlobalFlowNumber("");
        //原全局业务流水号
        settlementLog.setTxnOriginalGlobalFlowNum("");
        settlementLog.setTxnZipCode("");
        settlementLog.setTxnMerchantName(installOrderDTO.getMerchantName());
        settlementLog.setTxnReferenceNumber(installOrderDTO.getRetrievalReferenceNumber());
        //原始交易日期
        settlementLog.setTxnOriginalTxnDate(null);
        LocalDate nextProcessDate = organizationInfo.getNextProcessingDay();
        settlementLog.setTxnTransactionDate(nextProcessDate.atTime(LocalTime.now()));
        settlementLog.setTxnTransactionDescription(installOrderDTO.getTransactionDesc());
        settlementLog.setTxnBillingDate(nextProcessDate);
        settlementLog.setTxnOutstandingAmount(BigDecimal.ZERO);
        settlementLog.setTxnExchangeRate(BigDecimal.ZERO);
        settlementLog.setVersionNumber(1L);
        settlementLog.setCreateTime(LocalDateTime.now());
        settlementLog.setUpdateTime(LocalDateTime.now());
        settlementLog.setTxnSecondMerchantId("");
        settlementLog.setUpdateBy("admin");
        settlementLog.setTxnOpponentAccountName("");
        settlementLog.setTxnOpponentAccountNum("");
        settlementLog.getTxnCityCode();
        settlementLog.setTxnOpponentBankNum("");
        settlementLog.setTxnOpponentAccountNum("");

        String customerId = null;
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(settlementLog.getTxnCardNumber(),settlementLog.getOrganizationNumber());
        if (InstallmentConstant.PRIMARY_INDICATOR.equals(cardAuthorizationInfo.getRelationshipIndicator())) {
            //主卡
            customerId = cardAuthorizationInfo.getPrimaryCustomerId();
        } else {
            //附卡
            customerId = cardAuthorizationInfo.getSupplementaryCustomerId();
        }
        settlementLog.setCustomerId(customerId);
        CardAuthorizationDTO cardAuthorizationDTO = BeanMapping.copy(cardAuthorizationInfo, CardAuthorizationDTO.class);
        int partitionKey = partitionKeyInitService.partitionKeyGenerator(cardAuthorizationDTO, null);
        settlementLog.setPartitionKey(partitionKey);
        return settlementLog;
    }

    /**
     * 本金借记流水赋值
     * @param installOrderDTO 分期订单
     * @param installTransEntrys
     * return SettlementLogDTO
     **/
    private SettlementLogDTO principalDebit(InstallOrderDTO installOrderDTO,InstallParameterDTO installTransEntrys )
    {
        //分期交易码
        String principalTransactionCode = installTransEntrys.getInstallAccountTran().getPrincipalTransactionCode();
        //取订单表 未入账本金
        BigDecimal unpostedAmount = installOrderDTO.getUnpostedAmount();
        SettlementLogDTO debit = getSettlementLog(installOrderDTO);
        debit.setTxnCardNumber(installOrderDTO.getCardNumber());
        //分期交易码
        debit.setTxnTransactionCode(principalTransactionCode);
        debit.setTxnTransactionDescription(installOrderDTO.getTransactionDesc());
        debit.setTxnTransactionCurrency(installOrderDTO.getInstallmentCcy());
        debit.setTxnBillingCurrency(installOrderDTO.getInstallmentCcy());
        debit.setTxnSettlementCurrency(installOrderDTO.getInstallmentCcy());
        //未入账金额
        debit.setTxnTransactionAmount(unpostedAmount);
        debit.setTxnBillingAmount(unpostedAmount);
        debit.setTxnSettlementAmount(unpostedAmount);
        debit.setTxnMerchantCategoryCode(installOrderDTO.getMcc());
        debit.setTxnReferenceNumber(installOrderDTO.getAcquireReferenceNo());
        debit.setTxnInstallmentTerm(String.valueOf(installOrderDTO.getPostedTerm() + 1));
        debit.setTxnInstallmentOrderId(installOrderDTO.getOrderId());
        //分期单笔
        debit.setTxnInstallmentIndicator("2");
        return debit;
    }
    /**
     * 本金贷记流水赋值
     * @param installOrderDTO 分期订单
     * @param installTransEntrys
     * return
     **/
    private SettlementLogDTO principalcredit(InstallOrderDTO installOrderDTO,InstallParameterDTO installTransEntrys )
    {
        //本金撤销交易码
        String principalReversalTransCode = installTransEntrys.getInstallAccountTran().getPrincipalReversalTransCode();
        //分期金额
        BigDecimal installmentAmount = installOrderDTO.getInstallmentAmount();
        SettlementLogDTO credit = getSettlementLog(installOrderDTO);
        credit.setTxnCardNumber(installOrderDTO.getCardNumber());
        //撤销交易码
        credit.setTxnTransactionCode(principalReversalTransCode);
        credit.setTxnTransactionDescription(installOrderDTO.getTransactionDesc());
        credit.setTxnTransactionCurrency(installOrderDTO.getInstallmentCcy());
        credit.setTxnBillingCurrency(installOrderDTO.getInstallmentCcy());
        credit.setTxnBillingCurrency(installOrderDTO.getInstallmentCcy());
        //未入账金额
        credit.setTxnTransactionAmount(installmentAmount);
        credit.setTxnBillingAmount(installmentAmount);
        credit.setTxnSettlementAmount(installmentAmount);
        credit.setTxnMerchantCategoryCode(installOrderDTO.getMcc());
        credit.setTxnAcquireReferencNo(InstallmentConstant.MERCHANTCATEGORYCODE);

        credit.setTxnInstallmentTerm(String.valueOf(installOrderDTO.getPostedTerm() + 1));
        credit.setTxnInstallmentOrderId(installOrderDTO.getOrderId());
        //分期单笔
//        credit.setTxnInstallmentIndicator(InstallmentConstant.INSTALMENT_INDICATOR_SINGLE)
        return credit;
    }


    /**
     * 本金贷记流水赋值
     * @param installOrderDTO 分期订单
     * @param installTransEntrys
     * return
     **/
    private SettlementLogDTO partPrincipalcredit(InstallOrderDTO installOrderDTO,InstallParameterDTO installTransEntrys ,BigDecimal principalAmountSum)
    {
        //本金撤销交易码
        String principalReversalTransCode = installTransEntrys.getInstallAccountTran().getPrincipalReversalTransCode();
        SettlementLogDTO credit = getSettlementLog(installOrderDTO);
        credit.setTxnCardNumber(installOrderDTO.getCardNumber());
        //撤销交易码
        credit.setTxnTransactionCode(principalReversalTransCode);
        credit.setTxnTransactionDescription(installOrderDTO.getTransactionDesc());
        credit.setTxnTransactionCurrency(installOrderDTO.getInstallmentCcy());
        credit.setTxnBillingCurrency(installOrderDTO.getInstallmentCcy());
        credit.setTxnBillingCurrency(installOrderDTO.getInstallmentCcy());
        //未入账金额
        credit.setTxnTransactionAmount(principalAmountSum);
        credit.setTxnBillingAmount(principalAmountSum);
        credit.setTxnSettlementAmount(principalAmountSum);
        credit.setTxnMerchantCategoryCode(installOrderDTO.getMcc());
        credit.setTxnAcquireReferencNo(InstallmentConstant.MERCHANTCATEGORYCODE);

        credit.setTxnInstallmentTerm(String.valueOf(installOrderDTO.getPostedTerm() + 1));
        credit.setTxnInstallmentOrderId(installOrderDTO.getOrderId());
        //分期单笔
//        credit.setTxnInstallmentIndicator(InstallmentConstant.INSTALMENT_INDICATOR_SINGLE)
        return credit;
    }

    /**
     * 费用处理 全收
     * @param installOrderDTO
     * @param installTransEntrys
     * @param desc
     * return SettlementLog
     **/
    private SettlementLogDTO fullAcceptance(InstallOrderDTO installOrderDTO,InstallParameterDTO installTransEntrys,String desc)
    {
        //费用入账交易码
        String feeTransactionCode = installTransEntrys.getInstallAccountTran().getFeeTransactionCode();
        SettlementLogDTO full = getSettlementLog(installOrderDTO);
        //费用入账交易码
        full.setTxnTransactionCode(feeTransactionCode);
        full.setTxnTransactionDescription(installOrderDTO.getTransactionDesc());
        full.setTxnAuthorizationCode(InstallmentConstant.SPACE);
        full.setTxnTransactionCurrency(installOrderDTO.getInstallmentCcy());
        full.setTxnBillingCurrency(installOrderDTO.getInstallmentCcy());
        full.setTxnSettlementCurrency(installOrderDTO.getInstallmentCcy());
        full.setTxnTransactionAmount(installOrderDTO.getUnpostedFeeAmount());
        full.setTxnBillingAmount(installOrderDTO.getUnpostedFeeAmount());
        full.setTxnSettlementAmount(installOrderDTO.getUnpostedFeeAmount());
        full.setTxnMerchantCategoryCode(InstallmentConstant.MERCHANTCATEGORYCODE);
        full.setTxnAcquireReferencNo(installOrderDTO.getAcquireReferenceNo());
        full.setTxnInstallmentTerm(String.valueOf(installOrderDTO.getPostedTerm() + 1));
        full.setTxnInstallmentOrderId(installOrderDTO.getOrderId());
        return full;
    }
    /**
     * 费用处理 全不收借记
     * @param installOrderDTO
     * @param installTransEntrys
     * @param desc
     * return SettlementLog
     **/
    private SettlementLogDTO notAllDebit(InstallOrderDTO installOrderDTO,InstallParameterDTO installTransEntrys,String desc){
        SettlementLogDTO noAllDebit = getSettlementLog(installOrderDTO);
        //费用入账交易码
        String feeTransactionCode = installTransEntrys.getInstallAccountTran().getFeeTransactionCode();
        //费用入账交易码
        noAllDebit.setTxnTransactionCode(feeTransactionCode);
        noAllDebit.setTxnTransactionDescription(installOrderDTO.getTransactionDesc());
        noAllDebit.setTxnAuthorizationCode(InstallmentConstant.SPACE);
        noAllDebit.setTxnTransactionCurrency(installOrderDTO.getInstallmentCcy());
        noAllDebit.setTxnBillingCurrency(installOrderDTO.getInstallmentCcy());
        noAllDebit.setTxnSettlementCurrency(installOrderDTO.getInstallmentCcy());
        noAllDebit.setTxnTransactionAmount(installOrderDTO.getUnpostedFeeAmount());
        noAllDebit.setTxnBillingAmount(installOrderDTO.getUnpostedFeeAmount());
        noAllDebit.setTxnSettlementAmount(installOrderDTO.getUnpostedFeeAmount());
        noAllDebit.setTxnMerchantCategoryCode(InstallmentConstant.MERCHANTCATEGORYCODE);
        noAllDebit.setTxnAcquireReferencNo(installOrderDTO.getAcquireReferenceNo());
        noAllDebit.setTxnInstallmentTerm(String.valueOf(installOrderDTO.getPostedTerm() + 1));
        noAllDebit.setTxnInstallmentOrderId(installOrderDTO.getOrderId());
        return noAllDebit;
    }
    /**
     * 费用处理 全不收贷记
     * @param installOrderDTO
     * @param installTransEntrys
     * @param desc
     * return SettlementLog
     **/
    private SettlementLogDTO notAllCredit(InstallOrderDTO installOrderDTO,InstallParameterDTO installTransEntrys,String desc)
    {
        //费用入账撤销交易码
        String feeReversalTransactionCode = installTransEntrys.getInstallAccountTran().getFeeReversalTransactionCode();
        //累计减免金额
        BigDecimal totalDerateFee = installOrderDTO.getTotalDerateFee();
        SettlementLogDTO noAllCredit = getSettlementLog(installOrderDTO);
        noAllCredit.setTxnTransactionCode(feeReversalTransactionCode);
        noAllCredit.setTxnTransactionDescription(installOrderDTO.getTransactionDesc());
        noAllCredit.setTxnAuthorizationCode(InstallmentConstant.SPACE);
        noAllCredit.setTxnTransactionCurrency(installOrderDTO.getInstallmentCcy());
        noAllCredit.setTxnBillingCurrency(installOrderDTO.getInstallmentCcy());
        noAllCredit.setTxnSettlementCurrency(installOrderDTO.getInstallmentCcy());

        BigDecimal charge = installOrderDTO.getUnpostedFeeAmount().add(installOrderDTO.getTotalReceivedFee()).subtract(totalDerateFee);
        noAllCredit.setTxnTransactionAmount(charge);
        noAllCredit.setTxnBillingAmount(charge);
        noAllCredit.setTxnSettlementAmount(charge);
        noAllCredit.setTxnMerchantCategoryCode(InstallmentConstant.MERCHANTCATEGORYCODE);
        noAllCredit.setTxnAcquireReferencNo(installOrderDTO.getAcquireReferenceNo());

        noAllCredit.setTxnInstallmentTerm(String.valueOf(installOrderDTO.getPostedTerm() + 1));
        noAllCredit.setTxnInstallmentOrderId(installOrderDTO.getOrderId());

        return noAllCredit;
    }
    /**
     * 费用处理 违约金
     * @param installOrderDTO
     * @param installTransEntrys
     * return
     **/
    private SettlementLogDTO liquidatedDamages(InstallOrderDTO installOrderDTO, InstallParameterDTO installTransEntrys, String status, TerminalParameterDTO terminalParameterDTO)
    {
        //提前结清违约金收取值
        BigDecimal penaltyValueBd = installTransEntrys.getInstallEarlyTermina().getPenaltyValue();
        //提前结清手续费处理方式
        //违约金处理方式
        String penaltyFlag = installTransEntrys.getInstallEarlyTermina().getPenaltyFlag();
        if (Objects.equals(InstallmentFunctionCodeEnum.FORCESETTLEMENT.getCode(), status))
        {
            //强制结清违约金
            penaltyValueBd = installTransEntrys.getInstallEarlyTermina().getForcePenaltyValue();
            penaltyFlag = installTransEntrys.getInstallEarlyTermina().getForcePenaltyFlag();
        }
        //取订单表 未入账本金
        BigDecimal unpostedAmount = installOrderDTO.getUnpostedAmount();
        //违约金
        BigDecimal penaltyBreach = BigDecimal.ZERO;
        //未入账本金比率
        if (Objects.equals(InstallPenaltyFlagEnum.NO_BOOKED_PRINCI_RATIO.getCode(), penaltyFlag)) {
            // 违约金
            penaltyBreach = unpostedAmount.multiply(penaltyValueBd).divide(hundred);
        }
        //收取固定期数费用
        if (Objects.equals(InstallPenaltyFlagEnum.FIXED_PERIOD_FEE.getCode(), penaltyFlag)) {
            //每期费用
            BigDecimal termFee = installOrderDTO.getTermFee();
            // 违约金
            penaltyBreach = termFee.multiply(penaltyValueBd);
        }
        //按未入账手续费比率
        if (Objects.equals(InstallPenaltyFlagEnum.NO_BOOKED_COMMISSION_RATIO.getCode(), penaltyFlag)) {
            //未入账手续
            BigDecimal unpostedFeeAmount = installOrderDTO.getUnpostedFeeAmount();
            // 违约金
            penaltyBreach = unpostedFeeAmount.multiply(penaltyValueBd).divide(hundred);
        }
        //按违约固定金额
        if (Objects.equals(InstallPenaltyFlagEnum.FIXED_AMOUNT_FOR_BREACH.getCode(), penaltyFlag)) {
            // 违约金
            penaltyBreach = penaltyValueBd;
        }
        terminalParameterDTO.setPenaltyBreach(penaltyBreach);

        //违约金清算流水
        if (!InstallPenaltyFlagEnum.NO_CHARGE.getCode().equals(penaltyFlag) || penaltyBreach.compareTo(BigDecimal.ZERO) !=0 )
        {
           return liquidatedSetLog(installOrderDTO,installTransEntrys,penaltyBreach);
        }
       return null;
    }
    /**
     * 费用处理 全不收贷记
     * @param installOrderDTO
     * @param installTransEntrys
     * @param penaltyBreach
     * return SettlementLog
     **/
    private SettlementLogDTO liquidatedSetLog(InstallOrderDTO installOrderDTO,InstallParameterDTO installTransEntrys,BigDecimal penaltyBreach)
    {
        //违约金入账交易码
        String penatlyTransactionCode = installTransEntrys.getInstallAccountTran().getPenatlyTransactionCode();
        SettlementLogDTO penalty = getSettlementLog(installOrderDTO);
        penalty.setTxnTransactionCode(penatlyTransactionCode);
        penalty.setTxnTransactionDescription(installOrderDTO.getTransactionDesc());
        penalty.setTxnAuthorizationCode(InstallmentConstant.SPACE);
        penalty.setTxnTransactionCurrency(installOrderDTO.getInstallmentCcy());
        penalty.setTxnBillingCurrency(installOrderDTO.getInstallmentCcy());
        penalty.setTxnSettlementCurrency(installOrderDTO.getInstallmentCcy());
        penalty.setTxnTransactionAmount(penaltyBreach);
        penalty.setTxnBillingAmount(penaltyBreach);
        penalty.setTxnSettlementAmount(penaltyBreach);
        penalty.setTxnMerchantCategoryCode(InstallmentConstant.MERCHANTCATEGORYCODE);
        penalty.setTxnAcquireReferencNo(installOrderDTO.getAcquireReferenceNo());
        penalty.setTxnTransactionDescription(penalty.getTxnTransactionDescription() + " EARLY REPAYMENT FEE ");
        //分期提前结清既不属于单笔也不属于整笔
        penalty.setTxnInstallmentIndicator(null);
        penalty.setTxnInstallmentTerm(String.valueOf(installOrderDTO.getPostedTerm() + 1));
        penalty.setTxnInstallmentOrderId(installOrderDTO.getOrderId());
        return penalty;
    }
    /**
     * 整笔退货
     * @param installOrderDTO
     * @param installTransEntrys
     * @param terminalParameterDTO
     * return SettlementLog
     **/
    private SettlementLogDTO clearingAccount(InstallOrderDTO installOrderDTO, InstallParameterDTO installTransEntrys, TerminalParameterDTO terminalParameterDTO)
    {
        //分期整笔退货交易码
        String installmentTransactionCode = installTransEntrys.getInstallAccountTran().getInstallReturnCodeRev();
        //分期金额
        BigDecimal installmentAmount = installOrderDTO.getInstallmentAmount();
        //清算交易入账
        SettlementLogDTO returnGoods = getSettlementLog(installOrderDTO);
        returnGoods.setTxnTransactionCode(installmentTransactionCode);
        returnGoods.setTxnTransactionDescription(installOrderDTO.getTransactionDesc());
        returnGoods.setTxnAuthorizationCode(InstallmentConstant.SPACE);
        returnGoods.setTxnTransactionCurrency(installOrderDTO.getInstallmentCcy());
        returnGoods.setTxnBillingCurrency(installOrderDTO.getInstallmentCcy());
        returnGoods.setTxnSettlementCurrency(installOrderDTO.getInstallmentCcy());
        //修复退货金额为空的bug
//        terminalParameterDTO.setTotalReturnedAmount(installOrder.getTotalReturnedAmount())
        returnGoods.setTxnTransactionAmount(installmentAmount.subtract(installOrderDTO.getTotalReturnedAmount()));
        returnGoods.setTxnBillingAmount(installmentAmount.subtract(installOrderDTO.getTotalReturnedAmount()));
        returnGoods.setTxnSettlementAmount(installmentAmount.subtract(installOrderDTO.getTotalReturnedAmount()));
        returnGoods.setTxnMerchantCategoryCode(InstallmentConstant.MERCHANTCATEGORYCODE);
        returnGoods.setTxnAcquireReferencNo(InstallmentConstant.ACQUIREREFERENCNO);

        returnGoods.setTxnInstallmentOrderId(installOrderDTO.getOrderId());
        returnGoods.setTxnInstallmentTerm(String.valueOf(installOrderDTO.getPostedTerm() + 1));
        return returnGoods;
    }
    /**
     * 更新订单
     * @param installOrderDTO
     * @param terminalParameterDTO
     * return int
     **/
    private int updateOrder(InstallOrderDTO installOrderDTO, TerminalParameterDTO terminalParameterDTO)
    {
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installOrderDTO.getOrganizationNumber());
        InstallOrder order = new InstallOrder();
        order.setOrderId(installOrderDTO.getOrderId());
        order.setPostedTerm(installOrderDTO.getTerm());
        String postedFeeTermFlag = terminalParameterDTO.getPostedFeeTermFlag();
        if (!InstallmentConstant.POSTEDFEETERMFLAG.equals(postedFeeTermFlag))
        {
            order.setPostedFeeTerm(installOrderDTO.getFeeTerm());
        }
        order.setTotalReceivedFee(terminalParameterDTO.getTotalReceivedFee());
        order.setTotalDerateFee(terminalParameterDTO.getTotalDerateFee());
        order.setTotalReturnedAmount(terminalParameterDTO.getTotalReturnedAmount());
        order.setReceivedPenatlyAmount(terminalParameterDTO.getPenaltyBreach());
        order.setLastMaintainDate(organizationInfo.getNextProcessingDay());
        order.setUnpostedAmount(BigDecimal.ZERO);
        order.setUnpostedFeeAmount(BigDecimal.ZERO);
        order.setUpdateTime(LocalDateTime.now());
        if(InstallmentOrderStatusEnum.PASSIVE_ADVANCESETTLEMENT_STATUS.getCode().equals(installOrderDTO.getStatus())){
            order.setStatus(installOrderDTO.getStatus());
        }

        return installOrderMapper.updateByPrimaryKeySelective(order);
    }

    /**
     * 本金借记流水赋值
     * @param installOrderDTO 分期订单
     * @param installTransEntrys
     * return SettlementLogDTO
     **/
    private SettlementLogDTO partPrincipalDebit(InstallOrderDTO installOrderDTO,InstallParameterDTO installTransEntrys,BigDecimal principalAmount )
    {
        //分期交易码
        String principalTransactionCode = installTransEntrys.getInstallAccountTran().getPrincipalTransactionCode();
        //取订单表 未入账本金
        SettlementLogDTO debit = getSettlementLog(installOrderDTO);
        debit.setTxnCardNumber(installOrderDTO.getCardNumber());
        //分期交易码
        debit.setTxnTransactionCode(principalTransactionCode);
        debit.setTxnTransactionDescription(installOrderDTO.getTransactionDesc());
        debit.setTxnTransactionCurrency(installOrderDTO.getInstallmentCcy());
        debit.setTxnBillingCurrency(installOrderDTO.getInstallmentCcy());
        debit.setTxnSettlementCurrency(installOrderDTO.getInstallmentCcy());
        //未入账金额
        debit.setTxnTransactionAmount(principalAmount);
        debit.setTxnBillingAmount(principalAmount);
        debit.setTxnSettlementAmount(principalAmount);
        debit.setTxnMerchantCategoryCode(installOrderDTO.getMcc());
        debit.setTxnReferenceNumber(installOrderDTO.getAcquireReferenceNo());

        debit.setTxnInstallmentTerm(String.valueOf(installOrderDTO.getPostedTerm() + 1));
        debit.setTxnInstallmentOrderId(installOrderDTO.getOrderId());
        //分期单笔
        debit.setTxnInstallmentIndicator("2");
        return debit;
    }


    /**
     * 费用处理 全收
     * @param installOrderDTO
     * @param installTransEntrys
     * return SettlementLog
     **/
    private SettlementLogDTO partAcceptance(InstallOrderDTO installOrderDTO,InstallParameterDTO installTransEntrys,BigDecimal feeAmount)
    {
        //费用入账交易码
        String feeTransactionCode = installTransEntrys.getInstallAccountTran().getFeeTransactionCode();
        SettlementLogDTO full = getSettlementLog(installOrderDTO);
        full.setTxnInstallmentIndicator(null);
        //费用入账交易码
        full.setTxnTransactionCode(feeTransactionCode);
        full.setTxnTransactionDescription(installOrderDTO.getTransactionDesc());
        full.setTxnAuthorizationCode("");
        full.setTxnTransactionCurrency(installOrderDTO.getInstallmentCcy());
        full.setTxnBillingCurrency(installOrderDTO.getInstallmentCcy());
        full.setTxnSettlementCurrency(installOrderDTO.getInstallmentCcy());
        full.setTxnTransactionAmount(feeAmount);
        full.setTxnBillingAmount(feeAmount);
        full.setTxnSettlementAmount(feeAmount);
        full.setTxnMerchantCategoryCode(InstallmentConstant.MERCHANTCATEGORYCODE);
        full.setTxnAcquireReferencNo(installOrderDTO.getAcquireReferenceNo());

        full.setTxnInstallmentTerm(String.valueOf(installOrderDTO.getPostedTerm() + 1));
        full.setTxnInstallmentOrderId(installOrderDTO.getOrderId());
        return full;
    }


    /**
     * 更新订单
     * @param installOrderDTO
     * @param terminalParameterDTO
     * return int
     **/
    private int updateOrderPartSettle(InstallOrderDTO installOrderDTO, TerminalParameterDTO terminalParameterDTO){
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installOrderDTO.getOrganizationNumber());
        InstallOrder order = new InstallOrder();
        order.setStatus(installOrderDTO.getStatus());
        order.setOrderId(installOrderDTO.getOrderId());
        order.setTerm(installOrderDTO.getTerm());
        order.setPostedTerm(installOrderDTO.getPostedTerm());
        order.setTotalReceivedFee(terminalParameterDTO.getTotalReceivedFee());
        order.setTotalDerateFee(terminalParameterDTO.getTotalDerateFee());
        order.setTotalReturnedAmount(terminalParameterDTO.getTotalReturnedAmount());
        order.setReceivedPenatlyAmount(terminalParameterDTO.getPenaltyBreach());
        order.setLastMaintainDate(organizationInfo.getNextProcessingDay());
        order.setUnpostedAmount(installOrderDTO.getUnpostedAmount());
        order.setUnpostedFeeAmount(installOrderDTO.getUnpostedFeeAmount());
        order.setUpdateTime(LocalDateTime.now());
        order.setFirstTermAmount(installOrderDTO.getFirstTermAmount());
        order.setFirstTermFee(installOrderDTO.getFirstTermFee());
        order.setTermFee(installOrderDTO.getTermFee());
        order.setTermAmount(installOrderDTO.getTermAmount());
        order.setTotalFeeAmount(installOrderDTO.getTotalFeeAmount());
        //更新订单缓存
        installOrderDTO.setTotalReceivedFee(order.getTotalReceivedFee());
        installOrderDTO.setTotalDerateFee(order.getTotalDerateFee());
        installOrderDTO.setTotalReturnedAmount(order.getTotalReturnedAmount());
        installOrderDTO.setReceivedPenatlyAmount(order.getReceivedPenatlyAmount());
        installOrderDTO.setLastMaintainDate(organizationInfo.getNextProcessingDay());
        return installOrderMapper.updateByPrimaryKeySelective(order);
    }



    /**
     * 部分退货
     * @param installOrderDTO
     * @param installTransEntrys
     * @param installPlans
     * @return TerminalParameterDTO
     **/
    private TerminalParameterDTO partCancelProcess(InstallOrderDTO installOrderDTO, InstallParameterDTO installTransEntrys,List<InstallPlanDTO> installPlans ) {

        //封装数据
        List<SettlementLogDTO> arrayList = new ArrayList<>();
        //累计退货金额
        BigDecimal totalReturnedAmount = installOrderDTO.getTotalReturnedAmount();
        //手续费累计金额
        BigDecimal totalReceivedFee = BigDecimal.ZERO;
        //累计减免金额
        BigDecimal totalDerateFee = installOrderDTO.getTotalDerateFee();
        //提前结清金额
        BigDecimal earlySettleAmount = installOrderDTO.getEarlySettleAmount();
        BigDecimal principalAmountSum = earlySettleAmount;
        //已经抛账的金额
        List<InstallPlanDTO> alreadyInstalls = installPlans.stream().filter(x -> "Y".equals(x.getTermStutus())).collect(Collectors.toList());


        //分期金额
        BigDecimal installAmount = installOrderDTO.getInstallmentAmount();
        //部分退货后的分期金额
        BigDecimal newInstallAmount = installAmount.subtract(earlySettleAmount);

        //借记部分
        SettlementLogDTO debit = partPrincipalDebit(installOrderDTO, installTransEntrys,principalAmountSum);
        editSettlementLogAbsForCancel(debit);
        addSettlementLog(arrayList,debit);

        //本金处理  写清算流水表
        //贷记部分
        SettlementLogDTO credit = partPrincipalcredit(installOrderDTO, installTransEntrys,principalAmountSum);
        editSettlementLogAbsForCancel(credit);
        addSettlementLog(arrayList,credit);
        //整笔退货
        SettlementLogDTO returnGoods = partClearingAccount(installOrderDTO, installTransEntrys, principalAmountSum);
        editSettlementLogAbsForCancel(returnGoods);
        addSettlementLog(arrayList,returnGoods);

        //重新创建订单
        installOrderDTO.setInstallmentAmount(newInstallAmount);
        installOrderDTO.setInstallPriceFlag(InstallPriceFlagEnum.BASICPRICING_FLAG.getCode());
        AdjustInstallTermBo adjustInstallTermBo = installBookPretreatService.orderAdjustTerm(installOrderDTO);
        InstallOrderDTO newInstallOrderDto = adjustInstallTermBo.getInstallOrderDto();
        List<InstallPlan> newInstallPlanList = adjustInstallTermBo.getInstallPlanList();
        newInstallOrderDto.setOrderId(installOrderDTO.getOrderId());
        installOrderDTO.setFirstTermFee(newInstallOrderDto.getFirstTermFee());
        installOrderDTO.setTermFee(newInstallOrderDto.getTermFee());
        installOrderDTO.setFirstTermAmount(newInstallOrderDto.getFirstTermAmount());
        installOrderDTO.setTermAmount(newInstallOrderDto.getTermAmount());
        installOrderDTO.setTotalFeeAmount(newInstallOrderDto.getTotalFeeAmount());
        BigDecimal newRecordAmount = BigDecimal.ZERO;
        BigDecimal newRecordFeeAmount = BigDecimal.ZERO;
        for (InstallPlan installPlan : newInstallPlanList) {
            installPlan.setOrderId(installOrderDTO.getOrderId());
            if(installPlan.getTerm()<= alreadyInstalls.size()){
                totalReceivedFee = totalReceivedFee.add(installPlan.getFeeAmount());
                installPlan.setTermStutus("Y");
                newRecordAmount = newRecordAmount.add(installPlan.getTermAmount());
                newRecordFeeAmount = newRecordFeeAmount.add(installPlan.getFeeAmount());
            }
        }
        BigDecimal unpostedFeeAmount = newInstallPlanList.stream().filter(x -> "N".equals(x.getTermStutus())).map(InstallPlan::getFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        installOrderDTO.setUnpostedFeeAmount(unpostedFeeAmount);
        if(null !=alreadyInstalls && !alreadyInstalls.isEmpty()){
            BigDecimal recodeAmount = alreadyInstalls.stream().map(InstallPlanDTO::getTermAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal recodeFeeAmount = alreadyInstalls.stream().map(InstallPlanDTO::getFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            //退货金额和已入账金额的轧差
            /*SettlementLogDTO credit2 = partPrincipalcredit(installOrderDTO, installTransEntrys,recodeAmount.subtract(newRecordAmount));
            //分期标识 1-整笔 2-单笔
            credit2.setTxnInstallmentIndicator("2");
            credit2.setTxnInstallmentOrderId(installOrderDTO.getOrderId());
            editSettlementLogAbsForCancel(credit2);
            addSettlementLog(arrayList,credit2);*/
            InstallProductInfoResDTO installProductInfo = installProductInfoService.findByIndex(installOrderDTO.getOrganizationNumber(), installOrderDTO.getProductCode());
            String principalReversalTransCode = installTransEntrys.getInstallAccountTran().getPrincipalReversalTransCode();
            RecordedBO recorded = getRecorded(installOrderDTO,principalReversalTransCode, installProductInfo.getLimitProcessMode(),recodeAmount.subtract(newRecordAmount),"2");
            txnRecordedService.txnRecorded(recorded);


            SettlementLogDTO feeCredit = partFeeCredit(installOrderDTO, installTransEntrys,recodeFeeAmount.subtract(newRecordFeeAmount));
            editSettlementLogAbsForCancel(feeCredit);
            addSettlementLog(arrayList,feeCredit);
        }




        //交易金额累加到本次退货金额
        totalReturnedAmount = totalReturnedAmount.add(principalAmountSum);
        TerminalParameterDTO terminalParameterDTO = new TerminalParameterDTO();
        terminalParameterDTO.setTotalReturnedAmount(totalReturnedAmount);
        terminalParameterDTO.setTotalReceivedFee(totalReceivedFee);
        terminalParameterDTO.setTotalDerateFee(totalDerateFee);

        terminalParameterDTO.setNewInstallPlanList(newInstallPlanList);
        settlementLogService.addBatch(arrayList);
        return terminalParameterDTO;

    }


    /**
     * 费用处理贷调
     * @param installOrderDTO
     * @param installTransEntrys
     * return SettlementLog
     **/
    private SettlementLogDTO partFeeCredit(InstallOrderDTO installOrderDTO,InstallParameterDTO installTransEntrys,BigDecimal feeAmount)
    {
        //费用入账交易码
        String feeTransactionCode = installTransEntrys.getInstallAccountTran().getFeeReversalTransactionCode();
        SettlementLogDTO full = getSettlementLog(installOrderDTO);
        full.setTxnInstallmentIndicator(null);
        //费用入账交易码
        full.setTxnTransactionCode(feeTransactionCode);
        full.setTxnTransactionDescription(installOrderDTO.getTransactionDesc());
        full.setTxnAuthorizationCode("");
        full.setTxnTransactionCurrency(installOrderDTO.getInstallmentCcy());
        full.setTxnBillingCurrency(installOrderDTO.getInstallmentCcy());
        full.setTxnSettlementCurrency(installOrderDTO.getInstallmentCcy());
        full.setTxnTransactionAmount(feeAmount);
        full.setTxnBillingAmount(feeAmount);
        full.setTxnSettlementAmount(feeAmount);
        full.setTxnMerchantCategoryCode("");
        full.setTxnAcquireReferencNo(installOrderDTO.getAcquireReferenceNo());

        full.setTxnInstallmentTerm(String.valueOf(installOrderDTO.getPostedTerm() + 1));
        full.setTxnInstallmentOrderId(installOrderDTO.getOrderId());
        return full;
    }



    /**
     * 部分整笔退货
     * @param installOrderDTO
     * @param installTransEntrys
     * return SettlementLog
     **/
    private SettlementLogDTO partClearingAccount(InstallOrderDTO installOrderDTO, InstallParameterDTO installTransEntrys, BigDecimal amount)
    {
        //分期整笔退货交易码
        String installmentTransactionCode = installTransEntrys.getInstallAccountTran().getInstallReturnCodeRev();
        //清算交易入账
        SettlementLogDTO returnGoods = getSettlementLog(installOrderDTO);
        returnGoods.setTxnTransactionCode(installmentTransactionCode);
        returnGoods.setTxnTransactionDescription(installOrderDTO.getTransactionDesc());
        returnGoods.setTxnAuthorizationCode(InstallmentConstant.SPACE);
        returnGoods.setTxnTransactionCurrency(installOrderDTO.getInstallmentCcy());
        returnGoods.setTxnBillingCurrency(installOrderDTO.getInstallmentCcy());
        returnGoods.setTxnSettlementCurrency(installOrderDTO.getInstallmentCcy());
        //修复退货金额为空的bug
//        terminalParameterDTO.setTotalReturnedAmount(installOrder.getTotalReturnedAmount())
        returnGoods.setTxnTransactionAmount(amount);
        returnGoods.setTxnBillingAmount(amount);
        returnGoods.setTxnSettlementAmount(amount);
        returnGoods.setTxnMerchantCategoryCode(InstallmentConstant.MERCHANTCATEGORYCODE);
        returnGoods.setTxnAcquireReferencNo(InstallmentConstant.ACQUIREREFERENCNO);

        returnGoods.setTxnInstallmentOrderId(installOrderDTO.getOrderId());
        returnGoods.setTxnInstallmentTerm(String.valueOf(installOrderDTO.getPostedTerm() + 1));
        return returnGoods;
    }



    /**
     * 初始化核心入账实体
     *
     * @param installOrder
     * @param transactionCode
     * @return RecordedBO
     */
    public RecordedBO getRecorded(InstallOrderDTO installOrder, String transactionCode, String installLimitProcessIndicator, BigDecimal amount, String installIndicator) {
        //获取系统当前业务日期
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installOrder.getOrganizationNumber());
        if (organizationInfo == null) {
            logger.error("机构参数不存在，机构id：{}", installOrder.getOrganizationNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORG_INFO_PARAM_NOT_EXIST_FAULT);
        }
        //调用账户入帐接口进行入帐（贷调交易码取分期产品贷记交易码）
        RecordedBO recorded = new RecordedBO();
        recorded.setTxnAccountManageId(installOrder.getAccountManagementId());
        recorded.setTxnAuthorizationCode("");
        //授权匹配标志
        recorded.setTxnAuthorizationMatchIndicator(AuthMatchIndicatorEnum.NOT_MATCH_AUTH.getCode());
        //入账金额
        recorded.setTxnBillingAmount(amount);
        //入账币种
        recorded.setTxnBillingCurrency(installOrder.getInstallmentCcy());

        if (!Objects.equals(InstallmentTypeEnum.AUTO_INSTALL.getCode(), installOrder.getType())) {
            // 整笔分期赋值订单交易日期
            recorded.setTxnTransactionDate(installOrder.getTransactionDate());
        }

        recorded.setTxnCardNumber(installOrder.getCardNumber());
        recorded.setTxnCityCode("");
        recorded.setTxnCountryCode("");
        recorded.setTxnDccIndicator("");
        recorded.setTxnExchangeRate(BigDecimal.ZERO);
        recorded.setTxnFallBackIndicator("");
        recorded.setTxnFeeTableId("");
        recorded.setTxnForcePostIndicator("");
        recorded.setTxnGlobalFlowNumber("");
        recorded.setTxnIfiIndicator("");
        //分期标识
        recorded.setTxnInstallmentIndicator(installIndicator);
        //分期订单号
        recorded.setTxnInstallmentOrderId(installOrder.getOrderId());
        recorded.setTxnInstallmentTerm("");
        recorded.setTxnInterestTableId("");
        recorded.setTxnLimitNodeId("");
        recorded.setTxnMerchantCategoryCode("");
        recorded.setTxnMerchantId("");
        recorded.setTxnMerchantName("");
        recorded.getTxnOpponentAccountName();
        recorded.setTxnOpponentAccountNumber("");
        recorded.setTxnOpponentBankNumber("");
        //原全局业务流水号
        recorded.setTxnOriginalGlobalFlowNumber("");
        //授权额度占用金额
        recorded.setTxnOutstandingAmount(BigDecimal.ZERO);
        //父级交易账户ID
        recorded.setTxnParentTransactionAccountId("");
        //POS输入方式
        recorded.setTxnPosEntryMode("");
        //入账方式
        recorded.setTxnPostMethod(PostMethodEnum.REAL_TIME.getCode());
        recorded.setTxnPsvIndicator("");
        recorded.setTxnReferenceNumber("");
        recorded.setTxnReimbursementAttribute("");
        //是否恢复授权占用额度标志
        recorded.setTxnReleaseAuthorizationAmount(ReleaseAuthAmountEnum.NOT_RECOVER.getCode());
        //拒绝重入账标志
        recorded.setTxnRepostFromSuspend(RepostFromSuspendEnum.NORMAL_TRANS.getCode());
        recorded.setTxnReverseFeeIndicator("");
        recorded.setTxnSecondMerchantId("");
        recorded.setTxnSecondMerchantName("");
        //清算金额
        recorded.setTxnSettlementAmount(amount);
        //清算币种
        recorded.setTxnSettlementCurrency(installOrder.getInstallmentCcy());
        recorded.setTxnStateCode("");
        //交易金额
        recorded.setTxnTransactionAmount(amount);
        //入账交易码
        recorded.setTxnTransactionCode(transactionCode);
        recorded.setTxnTransactionDescription(installOrder.getTransactionDesc());
        //交易币种
        recorded.setTxnTransactionCurrency(installOrder.getInstallmentCcy());

        //0=本地输入1=本行外部输入2=内生交易
        recorded.setTxnTransactionSource(TransactionSourceEnum.LOCAL.getCode());
        recorded.setTxnVisaChargeFlag("");
        recorded.setTxnZipCode("");
        recorded.setTxnCardNumber(installOrder.getCardNumber());
        recorded.setTxnInstallmentOrderId(installOrder.getOrderId());
        //分期类型
        recorded.setInstallmentType(installOrder.getType());
        //添加分期额度处理模式
        recorded.setInstallmentLimitProcessIndicator(installLimitProcessIndicator);
        return recorded;
    }
}
