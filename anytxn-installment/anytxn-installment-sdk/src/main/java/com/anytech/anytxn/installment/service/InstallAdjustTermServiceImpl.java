package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.common.core.enums.TransactionSourceEnum;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderSelfMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallPlanSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.dao.installment.model.InstallPlan;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.business.base.transaction.domain.bo.RecordedBO;
import com.anytech.anytxn.installment.base.domain.bo.AdjustInstallTermBo;
import com.anytech.anytxn.installment.base.domain.dto.AdjustInstallTermDTO;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.enums.AuthMatchIndicatorEnum;
import com.anytech.anytxn.installment.base.enums.InstallPriceFlagEnum;
import com.anytech.anytxn.business.base.installment.enums.InstallmentTypeEnum;
import com.anytech.anytxn.installment.base.enums.PostMethodEnum;
import com.anytech.anytxn.installment.base.enums.ReleaseAuthAmountEnum;
import com.anytech.anytxn.installment.base.enums.RepostFromSuspendEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.service.IInstallAdjustTermService;
import com.anytech.anytxn.installment.base.service.IInstallBookPretreatService;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallAccountingTransParmResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallAccountingTransParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.transaction.base.service.ITxnRecordedService;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 支持分期调整还款期数（缩短或延长），每期还款本金重算
 *
 * <AUTHOR>
 * @date 2020-09-11
 **/
@Service
@Slf4j
public class InstallAdjustTermServiceImpl implements IInstallAdjustTermService {
    @Autowired
    private InstallOrderMapper installOrderMapper;
    @Autowired
    private InstallOrderSelfMapper installOrderSelfMapper;
    @Autowired
    private InstallPlanSelfMapper installPlanSelfMapper;
    @Autowired
    private IInstallBookPretreatService installBookPretreatService;
    @Autowired
    private IInstallProductInfoService installProductInfoService;
    @Autowired
    private IInstallAccountingTransParmService installAccountingTransParmService;
    @Autowired
    private ITxnRecordedService txnRecordedService;
    @Autowired
    private IOrganizationInfoService organizationInfoService;

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = AnyTxnInstallException.class)
    @Override
    public void adjustInstallTerm(AdjustInstallTermDTO adjustTermDto){
        String orderId = adjustTermDto.getOrderId();
        InstallOrder installOrder = installOrderMapper.selectByPrimaryKey(orderId);
        Integer adjustTerm = adjustTermDto.getTerm();
        Integer oldTerm = installOrder.getTerm();
        if(oldTerm.equals(adjustTerm)){
            log.info("期数相同");
            return;
        }
        List<InstallPlan> installPlans = installPlanSelfMapper.selectPlansByOrderId(orderId);
        List<InstallPlan> alreadyInstallPlans = installPlans.stream().filter(x -> "Y".equals(x.getTermStutus())).collect(Collectors.toList());
        /*//重算费用
        InstallTradingSearchKeyDTO installDto = new InstallTradingSearchKeyDTO();
        installDto.setCardNumber(installOrder.getCardNumber());
        installDto.setTerm(adjustTermDto.getTerm());
        installDto.setType(installOrder.getType());
        installDto.setInstallAmount(installOrder.getInstallmentAmount());
        installDto.setPaymentWay(installOrder.getPaymentWay());
        installDto.setFeeFlag(installOrder.getFeeFlag());
        //重算分期金额
        InstallTradingDTO installTradingDto = installStagingListService.calculateFee(installDto);*/
        InstallOrderDTO installOrderDto = BeanMapping.copy(installOrder, InstallOrderDTO.class);
        installOrderDto.setTerm(adjustTerm);
        installOrderDto.setInstallPriceFlag(InstallPriceFlagEnum.BASICPRICING_FLAG.getCode());
        AdjustInstallTermBo adjustInstallTermBo = installBookPretreatService.orderAdjustTerm(installOrderDto);
        InstallOrderDTO newInstallOrderDto = adjustInstallTermBo.getInstallOrderDto();
        List<InstallPlan> newInstallPlanList = adjustInstallTermBo.getInstallPlanList();
        newInstallOrderDto.setOrderId(orderId);
        int alreadyInstallSize = 0;
        if(null != alreadyInstallPlans && !alreadyInstallPlans.isEmpty()){
            //抛过账
            alreadyInstallSize = alreadyInstallPlans.size();
        }
        for (InstallPlan installPlan : newInstallPlanList) {
            installPlan.setOrderId(orderId);
            if(installPlan.getTerm()<= alreadyInstallSize){
                installPlan.setTermStutus("Y");
            }
        }
        newInstallOrderDto.setPostedTerm(alreadyInstallSize);
        newInstallOrderDto.setPostedFeeTerm(alreadyInstallSize);
        newInstallOrderDto.setFirstTermFee(newInstallPlanList.get(0).getFeeAmount());
        //本金
        BigDecimal amountNewSum = BigDecimal.ZERO;
        BigDecimal feeNewSum = BigDecimal.ZERO;
        //抛过账
        if(alreadyInstallSize > 0){
            //本金
            BigDecimal amountSum = alreadyInstallPlans.stream().map(InstallPlan::getTermAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            //费用
            BigDecimal feeSum = alreadyInstallPlans.stream().map(InstallPlan::getFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            List<InstallPlan> alreadyNewInstallPlans = newInstallPlanList.stream().filter(x -> "Y".equals(x.getTermStutus())).collect(Collectors.toList());
            //本金
            amountNewSum = alreadyNewInstallPlans.stream().map(InstallPlan::getTermAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            //费用
            feeNewSum = alreadyNewInstallPlans.stream().map(InstallPlan::getFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal subAmount = amountNewSum.subtract(amountSum);
            BigDecimal subFee = feeNewSum.subtract(feeSum);
            InstallProductInfoResDTO installProductInfo = installProductInfoService.findByIndex(installOrder.getOrganizationNumber(), installOrder.getProductCode());
            InstallAccountingTransParmResDTO installAccountingTrans= installAccountingTransParmService.selectByIndex(installOrder.getOrganizationNumber(), installProductInfo.getPostingTransactionParmId());
            //本金交易码
            String principalTransactionCode;
            //费用交易码
            String feeTransactionCode;
            if(subAmount.compareTo(BigDecimal.ZERO)>0){
                principalTransactionCode = installAccountingTrans.getPrincipalTransactionCode();
            }else {
                principalTransactionCode = installAccountingTrans.getPrincipalReversalTransCode();
                subAmount = subAmount.abs();
            }
            if(subFee.compareTo(BigDecimal.ZERO)>0){
                feeTransactionCode = installAccountingTrans.getFeeTransactionCode();
            }else {
                feeTransactionCode = installAccountingTrans.getFeeReversalTransactionCode();
                subFee = subFee.abs();
            }
            RecordedBO recorded = getRecorded(newInstallOrderDto,principalTransactionCode, installProductInfo.getLimitProcessMode(),subAmount,"2");
            txnRecordedService.txnRecorded(recorded);
            RecordedBO feeRecorded = getRecorded(newInstallOrderDto,feeTransactionCode, installProductInfo.getLimitProcessMode(),subFee,"");
            txnRecordedService.txnRecorded(feeRecorded);
        }
        newInstallOrderDto.setUnpostedAmount(newInstallOrderDto.getInstallmentAmount().subtract(amountNewSum));
        newInstallOrderDto.setUnpostedFeeAmount(newInstallOrderDto.getTotalFeeAmount().subtract(feeNewSum));
        installOrderMapper.updateByPrimaryKey(BeanMapping.copy(newInstallOrderDto,InstallOrder.class));
        if(oldTerm.compareTo(adjustTerm)>0){
            installPlanSelfMapper.deleteGtTermsByOrderId(orderId,adjustTerm);
            installPlanSelfMapper.batchUpdateByOrderId(newInstallPlanList);

        }else if(oldTerm.compareTo(adjustTerm)<0){
            List<InstallPlan> upateInstallPlans = newInstallPlanList.stream().filter(x -> x.getTerm().compareTo(oldTerm) <= 0).collect(Collectors.toList());
            List<InstallPlan> insertInstallPlans = newInstallPlanList.stream().filter(x -> x.getTerm().compareTo(oldTerm) > 0).collect(Collectors.toList());
            installPlanSelfMapper.batchUpdateByOrderId(upateInstallPlans);
            installPlanSelfMapper.insertInstallPlanBatch(insertInstallPlans);
        }


    }


    /**
     * 初始化核心入账实体
     *
     * @param installOrder
     * @param transactionCode
     * @return RecordedBO
     */
    public RecordedBO getRecorded(InstallOrderDTO installOrder, String transactionCode, String installLimitProcessIndicator,BigDecimal amount,String installIndicator) {
        //获取系统当前业务日期
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installOrder.getOrganizationNumber());
        if (organizationInfo == null) {
            log.error("机构参数不存在，机构id：{}", installOrder.getOrganizationNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORG_INFO_PARAM_NOT_EXIST_FAULT);
        }
        //调用账户入帐接口进行入帐（贷调交易码取分期产品贷记交易码）
        RecordedBO recorded = new RecordedBO();
        recorded.setTxnAccountManageId(installOrder.getAccountManagementId());
        recorded.setTxnAuthorizationCode("");
        //授权匹配标志
        recorded.setTxnAuthorizationMatchIndicator(AuthMatchIndicatorEnum.NOT_MATCH_AUTH.getCode());
        //入账金额
        recorded.setTxnBillingAmount(amount);
        //入账币种
        recorded.setTxnBillingCurrency(installOrder.getInstallmentCcy());

        if (!Objects.equals(InstallmentTypeEnum.AUTO_INSTALL.getCode(), installOrder.getType())) {
            // 整笔分期赋值订单交易日期
            recorded.setTxnTransactionDate(installOrder.getTransactionDate());
        }

        recorded.setTxnCardNumber(installOrder.getCardNumber());
        recorded.setTxnCityCode("");
        recorded.setTxnCountryCode("");
        recorded.setTxnDccIndicator("");
        recorded.setTxnExchangeRate(BigDecimal.ZERO);
        recorded.setTxnFallBackIndicator("");
        recorded.setTxnFeeTableId("");
        recorded.setTxnForcePostIndicator("");
        recorded.setTxnGlobalFlowNumber("");
        recorded.setTxnIfiIndicator("");
        //分期标识
        recorded.setTxnInstallmentIndicator(installIndicator);
        //分期订单号
        recorded.setTxnInstallmentOrderId("");
        recorded.setTxnInstallmentTerm("");
        recorded.setTxnInterestTableId("");
        recorded.setTxnLimitNodeId("");
        recorded.setTxnMerchantCategoryCode("");
        recorded.setTxnMerchantId("");
        recorded.setTxnMerchantName("");
        recorded.getTxnOpponentAccountName();
        recorded.setTxnOpponentAccountNumber("");
        recorded.setTxnOpponentBankNumber("");
        //原全局业务流水号
        recorded.setTxnOriginalGlobalFlowNumber("");
        //授权额度占用金额
        recorded.setTxnOutstandingAmount(BigDecimal.ZERO);
        //父级交易账户ID
        recorded.setTxnParentTransactionAccountId("");
        //POS输入方式
        recorded.setTxnPosEntryMode("");
        //入账方式
        recorded.setTxnPostMethod(PostMethodEnum.REAL_TIME.getCode());
        recorded.setTxnPsvIndicator("");
        recorded.setTxnReferenceNumber("");
        recorded.setTxnReimbursementAttribute("");
        //是否恢复授权占用额度标志
        recorded.setTxnReleaseAuthorizationAmount(ReleaseAuthAmountEnum.NOT_RECOVER.getCode());
        //拒绝重入账标志
        recorded.setTxnRepostFromSuspend(RepostFromSuspendEnum.NORMAL_TRANS.getCode());
        recorded.setTxnReverseFeeIndicator("");
        recorded.setTxnSecondMerchantId("");
        recorded.setTxnSecondMerchantName("");
        //清算金额
        recorded.setTxnSettlementAmount(installOrder.getInstallmentAmount());
        //清算币种
        recorded.setTxnSettlementCurrency(installOrder.getInstallmentCcy());
        recorded.setTxnStateCode("");
        //交易金额
        recorded.setTxnTransactionAmount(amount);
        //入账交易码
        recorded.setTxnTransactionCode(transactionCode);
        //交易币种
        recorded.setTxnTransactionCurrency(installOrder.getInstallmentCcy());
        recorded.setTxnTransactionDescription("");
        //0=本地输入1=本行外部输入2=内生交易
        recorded.setTxnTransactionSource(TransactionSourceEnum.LOCAL.getCode());
        recorded.setTxnVisaChargeFlag("");
        recorded.setTxnZipCode("");
        recorded.setTxnCardNumber(installOrder.getCardNumber());
        recorded.setTxnInstallmentOrderId(installOrder.getOrderId());
        //分期类型
        recorded.setInstallmentType(installOrder.getType());
        //添加分期额度处理模式
        recorded.setInstallmentLimitProcessIndicator(installLimitProcessIndicator);
        return recorded;
    }
}
