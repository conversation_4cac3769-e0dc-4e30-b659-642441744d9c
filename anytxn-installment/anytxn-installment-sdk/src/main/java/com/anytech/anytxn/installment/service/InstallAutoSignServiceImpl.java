package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.installment.base.domain.dto.InstallAutoSignDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallAutoSignSearchKeyDTO;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.enums.AutoSignStatusEnum;
import com.anytech.anytxn.business.base.installment.enums.InstallmentTypeEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.enums.InstallRepDetailEnum;
import com.anytech.anytxn.installment.base.service.IInstallAutoSignService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.installment.mapper.InstallAutoSignMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallAutoSignSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallAutoSign;
import com.anytech.anytxn.common.sequence.manager.IdGeneratorManager;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 分期自动分期协议管理
 *
 * <AUTHOR>
 * @date 2019/7/8
 */
@Service
public class InstallAutoSignServiceImpl implements IInstallAutoSignService {

    private Logger logger = LoggerFactory.getLogger(InstallAutoSignServiceImpl.class);


    @Autowired
    private IInstallProductInfoService installProductInfoService;
    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Autowired
    private InstallAutoSignMapper installAutoSignMapper;
    @Autowired
    private InstallAutoSignSelfMapper installAutoSignSelfMapper;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;

    @Override
    public int getCount(String partitionKey, String organizationNumber) {
        String partitionKey0 = null;
        String partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = partitionKey.split("-")[0];
            partitionKey1 = partitionKey.split("-")[1];
        }
        return installAutoSignSelfMapper.getCount(partitionKey0, partitionKey1, organizationNumber);
    }

    @Override
    public List<String> queryIds(String partitionKey, List<Integer> rowNumbers, String organizationNumber) {
        String partitionKey0 = null;
        String partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = partitionKey.split("-")[0];
            partitionKey1 = partitionKey.split("-")[1];
        }
        return installAutoSignSelfMapper.queryIds(partitionKey0, partitionKey1, rowNumbers, organizationNumber);
    }

    /**
     * 添加分期自动分期协议参数
     *
     * @param installAutoSignDTO
     * @return String
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public InstallAutoSignDTO addInstallAutoSign(InstallAutoSignDTO installAutoSignDTO) {
        logger.info("添加分期自动分期协议参数");
        if (installAutoSignDTO != null) {
            //判断必输字段是否为空
            checkRequiredInputs(installAutoSignDTO);
            //判断输入值是否正确
            checkCorrectInputs(installAutoSignDTO);
            //通过唯一索引查询是否存在
            InstallAutoSign selectByIndex = installAutoSignSelfMapper.selectByIndex(installAutoSignDTO.getOrganizationNumber(), installAutoSignDTO.getCardNumber(),installAutoSignDTO.getAutoSignType(),installAutoSignDTO.getCurrencyCode());
            if (selectByIndex != null) {
                logger.error("分期自动分期协议参数已存在,organizationNumber={},cardNumber={}", installAutoSignDTO.getOrganizationNumber(), installAutoSignDTO.getCardNumber());
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_AUTO_SIGN_ALREADY_EXIST_FAULT);
            }
            InstallAutoSign installAutoSign = BeanMapping.copy(installAutoSignDTO, InstallAutoSign.class);
            //设置默认值
            installAutoSign.setUpdateTime(LocalDateTime.now());
            installAutoSign.setUpdateBy("SYSTEM");
            //根据卡号和自动分期类型查询
            InstallAutoSign signByCardAndType = installAutoSignSelfMapper.selectSignByCardAndType(installAutoSignDTO.getCardNumber(), installAutoSignDTO.getAutoSignType(),installAutoSignDTO.getCurrencyCode());
            if (signByCardAndType != null) {
                logger.error("添加分期自动分期协议，数据已存在,cardNumber={},autoSignType={}", installAutoSignDTO.getCardNumber(), installAutoSignDTO.getAutoSignType());
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_AUTO_SIGN_INSERT_ALREADY_EXIST_FAULT);
            }
            //根据id查询
            InstallAutoSign autoSign = installAutoSignMapper.selectByPrimaryKey(installAutoSignDTO.getId());
            if (autoSign == null) {
                installAutoSign.setId(IdGeneratorManager.sequenceIdGenerator().generateSeqId());
                installAutoSign.setCreateTime(LocalDateTime.now());
                installAutoSign.setVersionNumber(Long.valueOf("1"));
                installAutoSign.setBaseAmount(installAutoSignDTO.getBaseAmount().setScale(2, BigDecimal.ROUND_HALF_UP));

                try {
                    int i = installAutoSignMapper.insertSelective(installAutoSign);
                    if (i < 0) {
                        logger.error("新建分期自动分期协议参数 插入数据库影响条数小于0");
                        throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_AUTO_SIGN_INSERT_FAULT);
                    }
                } catch (Exception e) {
                    logger.error("新建分期自动分期协议参数 插入数据库失败");
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INSERT_DATABASE_FAULT);
                }
            } else {
                logger.error("添加分期自动分期协议，数据已存在");
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_AUTO_SIGN_INSERT_ALREADY_EXIST_FAULT);
            }
            //为协议期数赋值 失效日期月份-生效日期月份
            Period period = Period.between(installAutoSign.getFromDate(), installAutoSign.getEndDate());
            InstallAutoSignDTO autoSignDTO = BeanMapping.copy(installAutoSign, InstallAutoSignDTO.class);
            autoSignDTO.setAgreementTerm(period.getYears() * 12 + period.getMonths());
            return autoSignDTO;
        } else {
            logger.warn("新增分期自动分期协议参数表传参为空");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.PR_AU);
        }
    }

    /**
     * 修改分期自动分期协议参数
     *
     * @param installAutoSignDTO
     * @return String
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public InstallAutoSignDTO modifyInstallAutoSign(InstallAutoSignDTO installAutoSignDTO) {
        if (installAutoSignDTO != null) {
            //判断必输字段是否为空
            checkRequiredInputs(installAutoSignDTO);
            //判断输入值是否正确
            checkCorrectInputs(installAutoSignDTO);
            InstallAutoSign installAutoSign = BeanMapping.copy(installAutoSignDTO, InstallAutoSign.class);
            //设置默认值
            installAutoSign.setUpdateTime(LocalDateTime.now());
            installAutoSign.setUpdateBy("SYSTEM");
            //根据卡号和自动分期类型查询
            InstallAutoSign signByCardAndType = installAutoSignSelfMapper.selectSignByCardAndType(installAutoSignDTO.getCardNumber(), installAutoSignDTO.getAutoSignType(),installAutoSignDTO.getCurrencyCode());
            //根据id查询
            InstallAutoSign autoSign = installAutoSignMapper.selectByPrimaryKey(installAutoSignDTO.getId());
            //判断协议是否存在
            if (signByCardAndType != null && autoSign != null) {
                try {
                    installAutoSign.setBaseAmount(installAutoSignDTO.getBaseAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                    int i = installAutoSignMapper.updateByPrimaryKey(installAutoSign);
                    if (i < 0) {
                        logger.error("修改分期自动分期协议参数 修改数据库影响条数小于0");
                        throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_AUTO_SIGN_UPDATE_FAULT);
                    }
                } catch (Exception e) {
                    logger.error("修改分期自动分期协议参数 修改数据库失败");
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_UPDATE_DATABASE_FAULT);
                }
            } else {
                logger.error("修改分期自动分期协议，数据不存在");
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_AUTO_SIGN_NOT_EXIST_FAULT);
            }
            //为协议期数赋值 失效日期月份-生效日期月份
            Period period = Period.between(installAutoSign.getFromDate(), installAutoSign.getEndDate());
            InstallAutoSignDTO autoSignDTO = BeanMapping.copy(installAutoSign, InstallAutoSignDTO.class);
            autoSignDTO.setAgreementTerm(period.getYears() * 12 + period.getMonths());
            return autoSignDTO;
        } else {
            logger.error("分期自动分期协议参数表为空");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.PR_AU);
        }
    }

    /**
     * 通过卡号和自动分期类型删除分期自动分期协议参数
     *
     * @param cardNumber
     * @param autoSignType
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeInstallAutoSign(String cardNumber, String autoSignType, String currencyCode) {
        logger.info("通过卡号:{}、自动分期类型:{} 删除分期自动分期协议", cardNumber, autoSignType);
        if (StringUtils.isEmpty(cardNumber)) {
            logger.error("自动分期协议卡号不能为空或空字符");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.AU_IN_AG);
        }
        if (StringUtils.isEmpty(autoSignType)) {
            logger.error("自动分期协议自动分期类型不能为空或空字符");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.AU_ST_PR);
        }
        InstallAutoSign installAutoSign = installAutoSignSelfMapper.selectSignByCardAndType(cardNumber, autoSignType,currencyCode);
        if (installAutoSign == null) {
            logger.error("删除分期自动分期协议参数信息，通过cardNumber:{},autoSignType:{}未查到数据", cardNumber, autoSignType);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_AUTO_SIGN_NOT_EXIST_FAULT);
        }
        if (Objects.equals(AutoSignStatusEnum.INVALID.getCode(), installAutoSign.getStatus())) {
            logger.error("删除分期自动分期协议参数信息,通过cardNumber:{},autoSignType:{}查询，数据已被删除", cardNumber, autoSignType);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_AUTO_SIGN_STATUS_FAULT);
        }
        try {
            int deleteRows = installAutoSignSelfMapper.updateSignByCardTypeAndCurrency(cardNumber, autoSignType,currencyCode);
            return deleteRows > 0;
        } catch (Exception e) {
            logger.error("删除分期自动分期协议失败,卡号：{},自动分期类型:{},原因：{}", cardNumber, autoSignType, e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_DELETE_DATABASE_FAULT);
        }
    }

    /**
     * 查询分期自动分期协议参数
     *
     * @param id
     * @return InstallAutoSignDTO
     */
    @Override
    public InstallAutoSignDTO findById(String id) {
        logger.info("查询分期自动分期协议");
        //参数校验
        if (id == null) {
            logger.error("根据id查询分期自动分期协议参数信息,id为空");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.PR_E);
        }
        InstallAutoSign installAutoSign = installAutoSignMapper.selectByPrimaryKey(id);
        //查询结果判断
        if (installAutoSign == null) {
            logger.error("根据id查询分期自动分期协议参数信息,通过id:{}未查到数据", id);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_AUTO_SIGN_NOT_EXIST_FAULT);
        }
        //为协议期数赋值 失效日期-生效日期
        Period period = Period.between(installAutoSign.getFromDate(), installAutoSign.getEndDate());
        InstallAutoSignDTO installAutoSignDTO = BeanMapping.copy(installAutoSign, InstallAutoSignDTO.class);
        installAutoSignDTO.setAgreementTerm(period.getYears() * 12 + period.getMonths());
        return installAutoSignDTO;
    }

    /**
     * 分页查询分期自动分期协议参数
     *
     * @param installAutoSignSearchKeyDTO
     * @return PageResultDTO<InstallAutoSignDTO>
     */
    @Override
    public PageResultDTO<InstallAutoSignDTO> findAll(InstallAutoSignSearchKeyDTO installAutoSignSearchKeyDTO) {
        logger.debug("分页查询分期自动分期协议参数信息");
        Page page = PageHelper.startPage(installAutoSignSearchKeyDTO.getPage(), installAutoSignSearchKeyDTO.getRows());
        Map<String, Object> searchKeyMap = new HashMap<>(1);
        searchKeyMap.put("cardNumber", installAutoSignSearchKeyDTO.getCardNumber());
        searchKeyMap.put("organizationNumber", OrgNumberUtils.getOrg());
        List<InstallAutoSign> installAutoSigns = installAutoSignSelfMapper.searchBySearchKey(searchKeyMap);
        List<InstallAutoSignDTO> installAutoSignDtos = BeanMapping.copyList(installAutoSigns, InstallAutoSignDTO.class);
        return new PageResultDTO<>(installAutoSignSearchKeyDTO.getPage(), installAutoSignSearchKeyDTO.getRows(), page.getTotal(), page.getPages(), installAutoSignDtos);
    }

    /**
     * 检查必输项字段是否为空
     */
    public void checkRequiredInputs(InstallAutoSignDTO installAutoSignDTO) {
        if (installAutoSignDTO.getOrganizationNumber() == null || "".equals(installAutoSignDTO.getOrganizationNumber())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.ORG_N);
        }
        if (installAutoSignDTO.getCardNumber() == null || "".equals(installAutoSignDTO.getCardNumber())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.CA_E);
        }
        if (installAutoSignDTO.getStatus() == null || "".equals(installAutoSignDTO.getStatus())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.ST_E);
        }
        if (installAutoSignDTO.getAutoSignType() == null || "".equals(installAutoSignDTO.getAutoSignType())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.AU_ST_TY);
        }
        if (installAutoSignDTO.getFromDate() == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.EF_DA_E);
        }
        if (installAutoSignDTO.getAgreementTerm() == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.PR_PE);
        }
        if (installAutoSignDTO.getEndDate() == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.EX_DA_E);
        }
        if (installAutoSignDTO.getInstallProductCode() == null || "".equals(installAutoSignDTO.getInstallProductCode())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.ST_PR_E);
        }
        if (installAutoSignDTO.getBaseAmount() == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.ST_AM_E);
        }
    }

    /**
     * 检查必输项字段是否正确
     */
    public void checkCorrectInputs(InstallAutoSignDTO installAutoSignDTO) {
        if (!Objects.equals(InstallmentTypeEnum.AUTO_INSTALL.getCode(), installAutoSignDTO.getAutoSignType())
                && !Objects.equals("RD", installAutoSignDTO.getAutoSignType())
                && !Objects.equals("LD", installAutoSignDTO.getAutoSignType())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_AUTO_SIGN_TYPE_FAULT);
        }
        if (!Objects.equals(AutoSignStatusEnum.TAKE_EFFECT.getCode(), installAutoSignDTO.getStatus())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_AUTO_SIGN_STATUS_FAULT);
        }
        if (installAutoSignDTO.getBaseAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_AUTO_SIGN_AMOUNT_FAULT);
        }
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installAutoSignDTO.getOrganizationNumber());
        if (organizationInfo == null) {
            logger.error("机构参数不存在,机构号：{}", installAutoSignDTO.getOrganizationNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORG_INFO_PARAM_NOT_EXIST_FAULT);

        }
        LocalDate nextProcessingDay = organizationInfo.getNextProcessingDay();
        if (installAutoSignDTO.getFromDate().isBefore(nextProcessingDay)) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_AUTO_SIGN_FROM_DATE_FAULT);
        }

        //判断卡号是否存在于卡片授权信息表
        CardAuthorizationDTO cardAuthorizationInfo = getAuthorizationInfoByCardNumber(installAutoSignDTO.getCardNumber());
        if (cardAuthorizationInfo == null) {
            logger.error("所输入的卡号不存在 cardNumber={}", installAutoSignDTO.getCardNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT,  InstallRepDetailEnum.CA_NE);
        }
//        String accountManagementId;
//        if (Objects.equals(installAutoSignDTO.getCurrencyCode(), cardAuthorizationInfo.getAccount1Currency())) {
//            accountManagementId = cardAuthorizationInfo.getAccount1Id();
//        } else if (Objects.equals(installAutoSignDTO.getCurrencyCode(), cardAuthorizationInfo.getAccount2Currency())) {
//            accountManagementId = cardAuthorizationInfo.getAccount2Id();
//        } else {
//            logger.info("币种既不是卡片授权信息表主币种，也不是外币币种，卡号：{},币种：{}", cardAuthorizationInfo.getCardNumber(), cardAuthorizationInfo.getAccount1Currency());
//            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_CURRENCY_NOT_CORRECT_FAULT);
//        }
//        installAutoSignDTO.setAccountManagementId(accountManagementId);
//        AccountManagementInfo managementInfo = accountManagementInfoMapper.selectByPrimaryKey(installAutoSignDTO.getAccountManagementId());
//        if(managementInfo == null){
//            logger.error("所输入的管理账户id不存在 AccountManagementId={}", installAutoSignDTO.getAccountManagementId());
//            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, "所输入的管理账户id不存在");
//        }
        installAutoSignDTO.setCustomerId(cardAuthorizationInfo.getPrimaryCustomerId());

        //判断分期产品是否存在于分期产品参数表
        InstallProductInfoResDTO installProConf = installProductInfoService.findByIndex(installAutoSignDTO.getOrganizationNumber(), installAutoSignDTO.getInstallProductCode());
        //检查录入分期产品的对应分期类型必须和页面输入的自动分期类型一致
        String prodType = installProConf.getProdType();
        if (!Objects.equals(installAutoSignDTO.getAutoSignType(), prodType)) {
            logger.error("分期产品对应的分期类型和页面输入的自动分期类型不一致 autoSignType={}", installAutoSignDTO.getAutoSignType());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_TYPE_DISAGREE_FAULT);
        }
    }

    public CardAuthorizationDTO getAuthorizationInfoByCardNumber(String txnCardNumber) {
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(
                txnCardNumber, OrgNumberUtils.getOrg());
        if (cardAuthorizationInfo == null) {
            return null;
        }
        CardAuthorizationDTO cardAuthorizationDTO = new CardAuthorizationDTO();
        BeanUtils.copyProperties(cardAuthorizationInfo, cardAuthorizationDTO);
        return cardAuthorizationDTO;
    }
}
