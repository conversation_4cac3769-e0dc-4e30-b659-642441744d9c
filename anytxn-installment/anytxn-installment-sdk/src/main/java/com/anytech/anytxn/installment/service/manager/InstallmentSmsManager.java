package com.anytech.anytxn.installment.service.manager;


import com.anytech.anytxn.notification.domain.dto.upi.OTPGeneRequestDTO;
import com.anytech.anytxn.notification.domain.dto.upi.OTPGeneResponseDTO;
import com.anytech.anytxn.notification.domain.dto.upi.OTPVerifyRequestDTO;
import com.anytech.anytxn.notification.domain.dto.upi.OTPVerifyResponseDTO;
import com.anytech.anytxn.notification.service.INotificationEventHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.ManagedBean;
import javax.annotation.Resource;
import java.util.Objects;
@ManagedBean
@Slf4j
public class InstallmentSmsManager {
    @Resource
    private INotificationEventHandler smsEventHandler;

    @Value("${anytxn.otp.userName}")
    private String userName;
    @Value("${anytxn.otp.userPasswd}")
    private String userPasswd;

    /**
     * 发送otp给用户
     */
    public OTPGeneResponseDTO sendOtpToEai(String cardNumber, String otpkey, String mobile){
        OTPGeneRequestDTO reqDTO = new OTPGeneRequestDTO();
        reqDTO.setId(userName);
        reqDTO.setPasswd(userPasswd);
        reqDTO.setCardnum(cardNumber);
        reqDTO.setResend("0");
        reqDTO.setMobile(mobile);
        log.info("分期---发送短信验证----，mobile:{}",mobile);
        return smsEventHandler.otpGeneration(reqDTO);
    }

    /**
     * OTP验证
     * @param cardNumber
     * @param otp
     * @return
     */
    public boolean verifyOtpToEai(String cardNumber, String otp){
        OTPVerifyRequestDTO reqDTO = new OTPVerifyRequestDTO();
        reqDTO.setId(userName);
        reqDTO.setPasswd(userPasswd);
        if (StringUtils.isNotBlank(cardNumber) && StringUtils.isNotBlank(otp)) {
            OTPVerifyResponseDTO respDTO = smsEventHandler.otpVerify(reqDTO, cardNumber, otp);
            log.info("InstallmentSmsManager verifyOtpToEai response : {}", respDTO);
            return null != respDTO && Objects.equals("200", respDTO.getResponseCode());
        }
        return false;
    }
}
