package com.anytech.anytxn.installment.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.authorization.base.service.dci.IDciTransactionClassifyService;
import com.anytech.anytxn.authorization.service.auth.AuthDetailDataModifyServiceImpl;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.enums.AuthItemCheckResCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthResponseCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthTrancactionStatusEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthTransTypeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthTransactionSourceCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthTypeCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.ReversalTypeEnum;
import com.anytech.anytxn.business.base.authorization.enums.StatusEnum;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionMapper;
import com.anytech.anytxn.business.monetary.service.CustReconciliationControlServiceImpl;
import com.anytech.anytxn.common.core.exception.AnyTxnException;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.DesensitizedUtils;
import com.anytech.anytxn.common.core.utils.PartitionKeyUtils;
import com.anytech.anytxn.business.base.account.domain.dto.AccountStatementInfoDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountBalanceInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountBalanceInfo;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatementInfo;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerBasicInfoSelfMapper;
import com.anytech.anytxn.business.dao.installment.mapper.*;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.dao.installment.model.InstallPlan;
import com.anytech.anytxn.business.dao.installment.model.InstallRecord;
import com.anytech.anytxn.business.dao.installment.model.InstallmentLimitUnitCross;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallRecordDTO;
import com.anytech.anytxn.business.base.transaction.domain.dto.PostedTransactionDTO;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionSelfMapper;
import com.anytech.anytxn.business.dao.transaction.model.PostedTransaction;
import com.anytech.anytxn.installment.base.domain.dto.*;
import com.anytech.anytxn.installment.base.constants.InstallmentConstant;
import com.anytech.anytxn.installment.base.enums.*;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.service.IInstallBillEntryAppService;
import com.anytech.anytxn.installment.base.service.IInstallEntryAppService;
import com.anytech.anytxn.installment.base.service.IInstallRecordService;
import com.anytech.anytxn.installment.service.interest.FixInterestRate;
import com.anytech.anytxn.installment.service.interest.TermLoanInterest;
import com.anytech.anytxn.installment.base.utils.InstallmentThreadLocalHolder;
import com.anytech.anytxn.limit.base.domain.dto.payload.CalLimitUnitReqDTO;
import com.anytech.anytxn.limit.base.domain.dto.payload.LimitReqDTO;
import com.anytech.anytxn.limit.service.CustomerLimitUpdateService;
import com.anytech.anytxn.common.sequence.manager.IdGeneratorManager;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmTransactionType;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallAccountingTransParmResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallTypeParmResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallTypeSupportTxnResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.model.ParmInstallInterestInfo;
import com.anytech.anytxn.parameter.base.installment.service.IInstallAccountingTransParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallTypeParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallTypeSupportTxnService;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmTransactionTypeSelfMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallTypeSupportMccSelfMapper;
import com.anytech.anytxn.parameter.installment.mapper.ParmInstallInterestInfoMapper;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeSupportMcc;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.transaction.base.domain.model.PostedTranAccountRelationInfo;
import com.anytech.anytxn.transaction.base.enums.LimitRequestSourceEnum;
import com.anytech.anytxn.transaction.base.enums.LimitRequestTypeEnum;
import com.anytech.anytxn.transaction.mapper.PostedTranAccountRelationInfoSelfMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-07-18
 **/
@Service
@Slf4j
public class InstallEntryAppServiceImpl implements IInstallEntryAppService {
    private static final Logger logger = LoggerFactory.getLogger(InstallEntryAppServiceImpl.class);
    @Autowired
    private PostedTransactionMapper postedTransactionMapper;
    @Autowired
    private PostedTransactionSelfMapper postedTransactionSelfMapper;
    @Autowired
    private InstallRecordMapper installRecordMapper;
    @Autowired
    private InstallRecordSelfMapper installRecordSelfMapper;
    @Autowired
    private IInstallRecordService installRecordService;
    @Autowired
    private AccountStatementInfoSelfMapper accountStatementInfoSelfMapper;
    @Autowired
    private IInstallTypeParmService installTypeParmService;
    @Autowired
    private IInstallProductInfoService installProductInfoService;
    @Autowired
    private IInstallAccountingTransParmService installAccountingTransParmService;
    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Autowired
    private IInstallTypeSupportTxnService installTypeSupportTxnService;
    @Autowired
    private InstallTypeSupportMccSelfMapper installTypeSupportMccSelfMapper;
    @Autowired
    private IDciTransactionClassifyService dciTransactionClassifyService;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;
    @Autowired
    private CustomerBasicInfoSelfMapper customerBasicInfoSelfMapper;
    @Autowired
    private InstallmentThreadLocalHolder installmentThreadLocalHolder;
    @Autowired
    private CustReconciliationControlServiceImpl custReconciliationControlService;
    @Autowired
    private PostedTranAccountRelationInfoSelfMapper postedTranAccountRelationInfoSelfMapper;
    @Autowired
    private AccountBalanceInfoSelfMapper accountBalanceInfoSelfMapper;
    @Autowired
    private ParmTransactionTypeSelfMapper parmTransactionTypeSelfMapper;

    @Resource
    private IInstallBillEntryAppService iInstallBillEntryAppService;

    @Resource
    private ParmInstallInterestInfoMapper parmInstallInterestInfoMapper;

    @Resource
    private AuthDetailDataModifyServiceImpl authDetailDataModifyService;

    @Autowired
    private InstallOrderSelfMapper installOrderSelfMapper;

    @Resource
    private InstallOrderMapper installOrderMapper;

    @Resource
    private InstallPlanSelfMapper installPlanSelfMapper;

    @Resource
    private InstallmentLimitUnitCrossSelfMapper limitUnitCrossSelfMapper;

    @Autowired
    private CustomerLimitUpdateService customerLimitUpdateService;



    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public InstallmentOrderChangeDTO orderChange(InstallmentOrderChangeDTO installmentOrderChangeDTO) {

        checkInstallOrderChange(installmentOrderChangeDTO);

        InstallOrder installOrder = installOrderMapper.selectByPrimaryKey(installmentOrderChangeDTO.getOrderId());

        if (StringUtils.equalsAny(installOrder.getStatus(), InstallmentOrderStatusEnum.PASSIVE_ADVANCESETTLEMENT_STATUS.getCode(),
                InstallmentOrderStatusEnum.ACTIVE_ADVANCESETTLEMENT_STATUS.getCode()) && installOrder.getUnpostedAmount().compareTo(BigDecimal.ZERO) == 0){
            //已结清的分期订单
            if (installmentOrderChangeDTO.getContinueInstallmentTerm() == null
                    || installmentOrderChangeDTO.getContinueInstallmentTerm() <= 0
                    || installmentOrderChangeDTO.getContinueInstallmentTerm() > installOrder.getTerm()){
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_ERROR);
            }


            //复制一个旧的分期订单
            InstallOrder newInstallmentOrder = BeanMapping.copy(installOrder, InstallOrder.class);

            newInstallmentOrder.setCardNumber(installmentOrderChangeDTO.getNewCardNumber());
            newInstallmentOrder.setAccountManagementId(installmentOrderChangeDTO.getNewAccountManagerId());
            newInstallmentOrder.setOrderId(IdGeneratorManager.sequenceIdGenerator().generateSeqId());
            newInstallmentOrder.setCreateTime(LocalDateTime.now());
            newInstallmentOrder.setStatus(InstallmentOrderStatusEnum.NORMAL_STATUS.getCode());
            newInstallmentOrder.setUpdateBy(installmentOrderChangeDTO.getUpdateBy());
            newInstallmentOrder.setPostedTerm(installmentOrderChangeDTO.getContinueInstallmentTerm() - 1);
            newInstallmentOrder.setReceivedPenatlyAmount(BigDecimal.ZERO);
            newInstallmentOrder.setStatusUpdateDate(LocalDate.now());
            newInstallmentOrder.setEarlySettleAmount(BigDecimal.ZERO);
            newInstallmentOrder.setPartitionKey(PartitionKeyUtils.partitionKey(newInstallmentOrder.getCustomerId()));



            OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installmentOrderChangeDTO.getOrganizationNumber());
            List<InstallmentLimitUnitCross> installmentLimitUnitCrosses = limitUnitCrossSelfMapper.selectByInstallOrderId(installOrder.getOrderId());
            List<InstallPlan> installPlans = installPlanSelfMapper.selectPlansByOrderId(installOrder.getOrderId());

            //复制一个分期额度管控单元
            List<InstallmentLimitUnitCross> newLimitUnitCrosses = BeanMapping.copyList(installmentLimitUnitCrosses, InstallmentLimitUnitCross.class);

            newLimitUnitCrosses.forEach(e -> {
                e.setAccountManagementId(installmentOrderChangeDTO.getNewAccountManagerId());
                e.setId(IdGeneratorManager.sequenceIdGenerator().generateSeqId());
                e.setInstallmentOrderId(newInstallmentOrder.getOrderId());
            });



            //复制一份分期计划
            List<InstallPlan> newInstallPlan = BeanMapping.copyList(installPlans, InstallPlan.class);
            newInstallPlan.forEach(e -> {
                e.setOrderId(newInstallmentOrder.getOrderId());
                if (Objects.equals(e.getTerm(), installmentOrderChangeDTO.getContinueInstallmentTerm())){
                    e.setTermPostDate(organizationInfo.getNextProcessingDay());
                    e.setTermStutus("N");
                    return;
                }

                if (e.getTerm() > installmentOrderChangeDTO.getContinueInstallmentTerm()){
                    e.setTermStutus("N");
                    e.setTermPostDate(organizationInfo.getNextProcessingDay().plusMonths(
                            e.getTerm() - installmentOrderChangeDTO.getContinueInstallmentTerm()
                    ));
                }
            });


            newInstallmentOrder.setUnpostedAmount(
                    newInstallPlan.stream().filter(t -> Objects.equals(t.getTermStutus(), "N"))
                            .map(InstallPlan::getTermAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
            );

            newInstallmentOrder.setUnpostedFeeAmount(newInstallPlan.stream().filter(t -> Objects.equals(t.getTermStutus(), "N"))
                    .map(InstallPlan::getFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

            newInstallmentOrder.setPostedFeeTerm(Math.toIntExact(newInstallPlan.stream()
                    .filter(t -> Objects.equals(t.getTermStutus(), "Y")
                            && t.getFeeAmount().compareTo(BigDecimal.ZERO) > 0).count())
            );

            newInstallmentOrder.setTotalReceivedFee(
                    newInstallPlan.stream().filter(t -> Objects.equals(t.getTermStutus(), "Y")
                                    && t.getFeeAmount().compareTo(BigDecimal.ZERO) > 0).map(InstallPlan::getFeeAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
            );

            newInstallmentOrder.setTotalDerateFee( newInstallPlan.stream().filter(t -> Objects.equals(t.getTermStutus(), "Y")
                            && t.getDerateAmount().compareTo(BigDecimal.ZERO) > 0).map(InstallPlan::getDerateAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));


            AccountManagementInfo newAcct = accountManagementInfoMapper.selectByPrimaryKey(installmentOrderChangeDTO.getNewAccountManagerId());


            //复制一份分期未抛额度占用
            LimitReqDTO limitReqDTO = new LimitReqDTO();
            limitReqDTO.setOrganizationNumber(installOrder.getOrganizationNumber());
            limitReqDTO.setCustomerId(newInstallmentOrder.getCustomerId());
            limitReqDTO.setGlobalFlowNumber(newInstallmentOrder.getGlobalFlowNumber());
            limitReqDTO.setAccountProductCode(newAcct.getProductNumber());
            limitReqDTO.setCardNumber(newInstallmentOrder.getCardNumber());
            limitReqDTO.setAccountCurrency(newAcct.getCurrency());
            limitReqDTO.setInstalmentProductCode(newInstallmentOrder.getProductCode());
            limitReqDTO.setCalLimitUnits(installStatementSingleChangeInit(newLimitUnitCrosses, newInstallmentOrder.getUnpostedAmount()));
            limitReqDTO.setTransactionAmount(newInstallmentOrder.getUnpostedAmount());
            limitReqDTO.setDebitCreditIndicator("D");
            limitReqDTO.setInstalmentOrderId(newInstallmentOrder.getOrderId());
            limitReqDTO.setAccountManagementId(newInstallmentOrder.getAccountManagementId());


            installOrderMapper.insertSelective(newInstallmentOrder);
            installPlanSelfMapper.insertInstallPlanBatch(newInstallPlan);
            limitUnitCrossSelfMapper.insertBatchInstallLimitUnitCross(newLimitUnitCrosses);



            customerLimitUpdateService.calculateLimitAndUpdate(limitReqDTO);

            return installmentOrderChangeDTO;
        }else{

            InstallOrderDTO installOrderDTO = new InstallOrderDTO();
            installOrderDTO.setOrderId(installmentOrderChangeDTO.getOrderId());
            installOrderDTO.setCardNumber(installmentOrderChangeDTO.getNewCardNumber());
            installOrderDTO.setAccountManagementId(installmentOrderChangeDTO.getNewAccountManagerId());
            installOrderDTO.setUpdateBy(installmentOrderChangeDTO.getUpdateBy());
            installOrderDTO.setVersionNumber(installmentOrderChangeDTO.getVersionNumber());
            int newCardNewAcct = installOrderSelfMapper.updateNewCardNewAcct(installOrderDTO);

            if (newCardNewAcct != 1){
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_UPDATE_DATABASE_FAULT);
            }

            return installmentOrderChangeDTO;
        }
    }


    public List<CalLimitUnitReqDTO> installStatementSingleChangeInit(List<InstallmentLimitUnitCross> newLimitUnitCrosses,
                                                                     BigDecimal billingAmount){

        return  newLimitUnitCrosses.stream().map( e -> {
            //额度管控单元循环域赋值
            CalLimitUnitReqDTO calLimitUnitReqDTO = new CalLimitUnitReqDTO();
            calLimitUnitReqDTO.setLimitUnitCode(e.getLimitUnitCode());
            calLimitUnitReqDTO.setLimitUnitVersion(e.getLimitUnitVersion());
            calLimitUnitReqDTO.setOverdrawAmount(e.getBalancePercent().multiply(billingAmount));

            //分期抛帐
            calLimitUnitReqDTO.setRequestType(LimitRequestTypeEnum.INSTALL_UN_THROW.getCode());
            //分期模块
            calLimitUnitReqDTO.setRequestSource(LimitRequestSourceEnum.INSTALL_SOURCE.getCode());
            return calLimitUnitReqDTO;
        }).collect(Collectors.toList());

    }

    @Override
    public void checkInstallOrderChange(InstallmentOrderChangeDTO installmentOrderChangeDTO) {

       if (StringUtils.isBlank(installmentOrderChangeDTO.getNewCardNumber()) && StringUtils.isBlank(installmentOrderChangeDTO.getNewAccountManagerId())){
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_ERROR);
        }

        if (StringUtils.isBlank(installmentOrderChangeDTO.getNewCardNumber())){
            installmentOrderChangeDTO.setNewCardNumber(installmentOrderChangeDTO.getOriginalCardNumber());
        }

        if (Objects.equals(installmentOrderChangeDTO.getOriginalAccountManagerId(), installmentOrderChangeDTO.getNewAccountManagerId())){
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_ERROR);
        }


        if (StringUtils.isNotBlank(installmentOrderChangeDTO.getNewAccountManagerId())){
            AccountManagementInfo oldAcct = accountManagementInfoMapper.selectByPrimaryKey(installmentOrderChangeDTO.getOriginalAccountManagerId());
            AccountManagementInfo newAcct = accountManagementInfoMapper.selectByPrimaryKey(installmentOrderChangeDTO.getNewAccountManagerId());

            if (!Objects.equals(oldAcct.getCustomerId(), newAcct.getCustomerId())){
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_ERROR);
            }

        }

        if (StringUtils.isNotBlank(installmentOrderChangeDTO.getNewCardNumber())){
            CardAuthorizationInfo oldCard = cardAuthorizationInfoMapper.selectByPrimaryKey(installmentOrderChangeDTO.getOriginalCardNumber(), installmentOrderChangeDTO.getOrganizationNumber());
            CardAuthorizationInfo newCard = cardAuthorizationInfoMapper.selectByPrimaryKey(installmentOrderChangeDTO.getNewCardNumber(), installmentOrderChangeDTO.getOrganizationNumber());

            if (Objects.equals(oldCard.getLiability(), "C")
                    && Objects.equals(newCard.getLiability(), "C")
                    && !Objects.equals(newCard.getCorporateCustomerId(), oldCard.getCorporateCustomerId())){
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_ERROR);
            }

            if (!Objects.equals(oldCard.getPrimaryCustomerId(),newCard.getPrimaryCustomerId())){
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_ERROR);
            }

        }
    }


    /**
     * 分期录入（7x24小时共用）
     *
     * @param installEntryDTO 分期录入参数
     * @return Long
     */
    @Override
    public InstallEntryResDTO entry(InstallEntryDTO installEntryDTO) {
        if (log.isInfoEnabled()){
            log.info( "分期交易录入, 参数为{}", DesensitizedUtils.getJson(installEntryDTO));
        }
        AuthRecordedDTO authRecordedDTO = new AuthRecordedDTO();

        try {
            String originTransactionId = installEntryDTO.getOriginTransactionId();
            String type = installEntryDTO.getInstallType();
            //必输字段检查
            checkInstallRecoder(installEntryDTO);
            CardAuthorizationInfo cardAuthInfo = installmentThreadLocalHolder.setCardAuthorization(installEntryDTO.getCardNumber());
            // 卡授权表
            CardAuthorizationDTO cardAuthorization = BeanMapping.copy(cardAuthInfo, CardAuthorizationDTO.class);
            // 如果是批中需要检查缓存是否更新
            if (CustAccountBO.isBatch()) {
                CustAccountBO.threadCustAccountBO.get().getAuthBO().checkCardAuthorization(cardAuthorization);
            }

            installEntryDTO.setCustomerId(cardAuthInfo.getPrimaryCustomerId());

            if (InstallEntryFunctionCodeEnum.INSTLL_ENTRY.getCode().equals(installEntryDTO.getFunctionCode())
                    || InstallEntryFunctionCodeEnum.AUTO_INSTALL_ENTRY.getCode().equals(installEntryDTO.getFunctionCode())) {
                entryOrder(installEntryDTO, originTransactionId, type, authRecordedDTO);
                // 撤销不涉及批次，暂不处理
            } else if (Objects.equals(InstallEntryFunctionCodeEnum.INSTALL_CANCLE.getCode(), installEntryDTO.getFunctionCode())) {
                cancleOrder(installEntryDTO, authRecordedDTO);
            }
        } catch (Exception e) {
            insertInstallRecord(authRecordedDTO, installEntryDTO, false, e);
            throw e;
        }

        return insertInstallRecord(authRecordedDTO, installEntryDTO, true, null);
    }

    @Override
    public InstallEntryDTO getAccountManagement(InstallEntryDTO installEntryDTO) {
        List<AccountManagementRecordedDTO> accountManagementRecordedDTOList = null;
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKeyAndOrganizationNumber(installEntryDTO.getCardNumber(), installEntryDTO.getOrganizationNumber());
        if (ObjectUtils.isEmpty(cardAuthorizationInfo)) {
            logger.error("分期交易录入 卡片授权表数据不存在");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_CARD_AUTH_INFO_NOT_EXIST_FAULT);
        }
        List<AccountManagementInfo> accountManagementInfoList = accountManagementInfoMapper.selectByCustomerIdAndOrganizationNumberAndCurrency(cardAuthorizationInfo.getPrimaryCustomerId(), installEntryDTO.getOrganizationNumber(), installEntryDTO.getInstallCcy());
        if (CollectionUtils.isEmpty(accountManagementInfoList)) {
            logger.error("无相关账户记录");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ACCOUNT_MANAGEMENT_RECORDED_NOT_EXIST_FAULT);
        }
        accountManagementRecordedDTOList = new ArrayList<>();
        for (AccountManagementInfo accountManagementInfo : accountManagementInfoList) {
            AccountManagementRecordedDTO accountManagementRecordedDTO = BeanMapping.copy(accountManagementInfo, AccountManagementRecordedDTO.class);
            accountManagementRecordedDTOList.add(accountManagementRecordedDTO);
        }
        installEntryDTO.setAccountManagementRecordedDTOList(accountManagementRecordedDTOList);

        return installEntryDTO;
    }



    /**
     * 分期录入（7x24小时批次共用只处理自动分期）
     *
     * @param installEntryDTO     分期参数
     * @param originTransactionId 已入账交易id(批次时传入空)
     * @param type                分期类型
     * @param authRecordedDTO     授权参数
     */

    public void entryOrder(InstallEntryDTO installEntryDTO, String originTransactionId, String type, AuthRecordedDTO authRecordedDTO) {
        PostedTransactionDTO postedTransactionDTO = null;
        // 单笔分期检查是否已分期
        if (Objects.equals(InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode(), installEntryDTO.getInstallType())
                && StringUtils.isNotBlank(originTransactionId)) {
            logger.info("单笔分期录入--------+++");
            postedTransactionDTO = checkAreadyInstall(installEntryDTO.getInstallAmount(), originTransactionId, installEntryDTO.getProductCode(), installEntryDTO.getOrganizationNumber(), installEntryDTO.getCardNumber(), installEntryDTO.getInstallFlag());
            InstallmentThreadLocalHolder.setPotedTransaction(originTransactionId,postedTransactionDTO);
            installEntryDTO.setAccountManagementId(postedTransactionDTO.getAccountManagementId());

            installEntryDTO.setMerchantId(postedTransactionDTO.getMerchantId());
            installEntryDTO.setMerchantName(postedTransactionDTO.getMerchantName());
        }

        if (StringUtils.isNotEmpty(installEntryDTO.getCardNumber()) && StringUtils.isEmpty(installEntryDTO.getAccountManagementId())
                && Objects.equals(InstallmentTypeEnum.APP_STATEMENT_INSTALL.getCode(), installEntryDTO.getInstallType())) {
            AccountManagementRecordedDTO accountManagementRecordedDTO = installEntryDTO.getAccountManagementRecordedDTO();
             logger.info("账单分期录入--------+++");
            if (!ObjectUtils.isEmpty(accountManagementRecordedDTO)) {
                installEntryDTO.setAccountManagementId(accountManagementRecordedDTO.getAccountManagementId());
            }
        }

        if (!Objects.equals(InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode(), installEntryDTO.getInstallType())
                && !Objects.equals(InstallmentTypeEnum.APP_STATEMENT_INSTALL.getCode(), installEntryDTO.getInstallType())) {
            //不支持分期的MCC
            InstallTypeSupportMcc installTypeSupportMcc = installTypeSupportMccSelfMapper.selectByOrgTypeAndMcc(installEntryDTO.getOrganizationNumber()
                    , installEntryDTO.getInstallType(), installEntryDTO.getMcc());
            if (null != installTypeSupportMcc) {
                logger.error("原始交易的MCC不允许分期,,分期类型:{},mcc：{}", installEntryDTO.getInstallType(), installEntryDTO.getMcc());
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORIGIN_TRANS_NOT_ALLOWED_INST_FAULT, InstallRepDetailEnum.MCC_OR_TR);
            }
        }
        AccountManagementInfo accountManagementInfo = null;
        if (installEntryDTO.getAccountManagementId() != null && !Objects.equals(installEntryDTO.getAccountManagementId(), "")) {
            accountManagementInfo = installmentThreadLocalHolder.setAccountManagementInfo(installEntryDTO.getAccountManagementId());
        }

        // 账单分期检查是否已分期(批次暂不处理)
        if (Objects.equals(InstallmentTypeEnum.APP_STATEMENT_INSTALL.getCode(), installEntryDTO.getInstallType())) {
            checkStatementInstall(installEntryDTO, accountManagementInfo);
        }
        // 构建授权
        AuthRecordedDTO authRecordDTO = getAuthRecordDTO(installEntryDTO, authRecordedDTO);
        //授权
        long l = System.currentTimeMillis();
        authProcess(authRecordDTO);
        logger.info("授权+账户花费----{}毫秒",System.currentTimeMillis()-l);
        //分期订单id
        installEntryDTO.setOrderId(authRecordDTO.getOrderId());

        // 单笔分期处理(批次暂不处理)
        if (Objects.equals(InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode(), type) && postedTransactionDTO != null) {

            //授权后 修改已入账明细表的已分期金额
            BigDecimal alreadyAmount = Optional.ofNullable(postedTransactionDTO.getInstAmount())
                    .orElse(BigDecimal.ZERO)
                    .add(installEntryDTO.getInstallAmount());


            //最后一期分期
            String installmentIndicator = postedTransactionDTO.getInstallmentIndicator();
            if (alreadyAmount.compareTo(postedTransactionDTO.getPostingAmount()) == 0) {
                installmentIndicator = InstallmentIndicatorEnum.ALREADY_INSTALL.getCode();
            }

            int i = postedTransactionSelfMapper.updateInstAmountByPostIdAndVersionNum(postedTransactionDTO.getPartitionKey(),
                    alreadyAmount, installmentIndicator, originTransactionId, postedTransactionDTO.getVersionNumber());

            if (i != 1) {
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_POSTED_TRANS_UPDATE_FAULT);
            }
        }
    }

        /**
     * 分期撤销
     *
     */

    public void cancleOrder(InstallEntryDTO installEntryDTO, AuthRecordedDTO authRecordedDTO) {
        if (!Objects.equals(InstallmentTypeEnum.CASH_INSTALL.getCode(), installEntryDTO.getInstallType())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_TYPE_NOT_CASH_INST_FAULT);
        }
        if (installEntryDTO.getId() == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_ORDER_ID_NOT_NULL_FAULT);
        }
        InstallRecord installRecord = installRecordMapper.selectByPrimaryKey(installEntryDTO.getId());
        if (!Objects.equals(InstallmentTypeEnum.CASH_INSTALL.getCode(), installRecord.getInstallType())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_TYPE_NOT_CASH_INST_FAULT);
        }
        if (!Objects.equals(InstallTransactionIndEnum.SUCCESS.getCode(), installRecord.getTransactionInd())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_TRANS_IND_NOT_SUCCESS_FAULT);
        }
        if (!Objects.equals(FundProcessFlagEnum.UNHANDLED.getCode(), installRecord.getFundProcessFlag()) && !Objects.equals(FundProcessFlagEnum.FAIL_PAYMENT.getCode(), installRecord.getFundProcessFlag())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_FUND_PROCESS_FLAG_NOT_CORRECT_FAULT);
        }
        authRecordedDTO = getAuthRecordDTO(installEntryDTO, authRecordedDTO);
        authRecordedDTO.setAuthOriginalTransmissionTime(installRecord.getAuthTransmisionTime());
        authRecordedDTO.setAuthOriginalGlobalFlowNumber(installRecord.getGlobalFlowNumber());
        //授权
        authProcess(authRecordedDTO);
        InstallRecord installRecordUpdate = new InstallRecord();
        installRecordUpdate.setId(installEntryDTO.getId());
        installRecordUpdate.setTransactionInd(InstallTransactionIndEnum.ALREADY_CANCLE.getCode());
        installRecordMapper.updateByPrimaryKeySelective(installRecordUpdate);

    }

    /**
     * 根据参数构建授权
     */
    private AuthRecordedDTO getAuthRecordDTO(InstallEntryDTO installEntryDTO, AuthRecordedDTO authRecordedDTO) {

        //查分期类型表 获取交易大类 细类
        InstallTypeParmResDTO installmentType = getIntallTypeByOrgNumAndType(installEntryDTO.getOrganizationNumber(),
                installEntryDTO.getInstallType());

        String authTransactionType = installmentType.getAuthTransactionType();
        String authTransactionTypeDetail = installmentType.getAuthTransactionTypeDetail();


        InstallProductInfoResDTO installProConf = installProductInfoService.findByIndex(
                installEntryDTO.getOrganizationNumber(), installEntryDTO.getProductCode());

        if (installProConf == null || !Objects.equals(installProConf.getProdType(), installEntryDTO.getInstallType())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PRO_PARAM_NOT_EXIST_FAULT);
        }


        InstallAccountingTransParmResDTO installAccountTranConf = installAccountingTransParmService
                .selectByIndex(installEntryDTO.getOrganizationNumber(), installProConf.getPostingTransactionParmId());


        if (installAccountTranConf == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ACCT_TRANS_PARAM_NOT_EXIST_FAULT);
        }

        /*
           如果该分期产品含有利息参数表id , 则 分期金额需要加上利息的金额
         */
        setInstallmentInterestAmount(installEntryDTO, installProConf);

        return buildAuthRecordedDTO(installEntryDTO, installAccountTranConf, installProConf.getTerm(),
                authTransactionType, authTransactionTypeDetail,authRecordedDTO);
    }



    private void setInstallmentInterestAmount(InstallEntryDTO installEntryDTO,
                                              InstallProductInfoResDTO installProConf) {

        installEntryDTO.setInstallInterestAmount(BigDecimal.ZERO);

        if (StringUtils.isBlank(installProConf.getInstalmentInterestParmTableId())){
            return;
        }

        ParmInstallInterestInfo parmInstallInterestInfo = parmInstallInterestInfoMapper.selectByTableIdAndOrgNum(
                installProConf.getInstalmentInterestParmTableId(), installEntryDTO.getOrganizationNumber());

        if (parmInstallInterestInfo == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ACCT_TRANS_PARAM_NOT_EXIST_FAULT);
        }

        if (Objects.equals(StatusEnum.INVALID.getCode(),parmInstallInterestInfo.getStatus())
                || Objects.equals("N",parmInstallInterestInfo.getInterestCalculationFlag())){
            return;
        }


        InstallmentInterestDTO interestDTO = InstallmentInterestDTO
                    .InstallmentInterestDTOBuilder
                    .anInstallmentInterestDTO()
                    .withAnnualInterestRate(parmInstallInterestInfo.getAnnualInterestRate())
                    .withCostAmount(installEntryDTO.getInstallAmount())
                    .withTerm(installProConf.getTerm())
                    .withBalanceMethod(installProConf.getBalanceMethod())
                    .build();



        if (Objects.equals(ChargeOptionEnum.FLAT_RATE.getCode(),parmInstallInterestInfo.getInterestCalculationMethod())){
            new FixInterestRate().getInterestResult(interestDTO);
        }else if (Objects.equals(ChargeOptionEnum.T_TERM.getCode(), parmInstallInterestInfo.getInterestCalculationMethod())){
            new TermLoanInterest().getInterestResult(interestDTO);
        }

        installEntryDTO.setInstallInterestAmount(interestDTO.getInterestAmount());
        installEntryDTO.setInstallAmount(installEntryDTO.getInstallAmount().add(interestDTO.getInterestAmount()));

    }



    /**
     * 分期授权
     */
    private void authProcess(AuthRecordedDTO authRecordedDTO) {
        if (logger.isDebugEnabled()) {
            logger.debug("分期授权,接口参数:{}", JSON.toJSONString(authRecordedDTO));
        }
        //授权
        int result = -1;
        try {
            long l = System.currentTimeMillis();
            result = dciTransactionClassifyService.processAuthTrans(authRecordedDTO,null);
            logger.info("授权花费时间----{}毫秒",System.currentTimeMillis()-l);
        } catch (AnyTxnException anytxnException){
            throw anytxnException;
        } catch (Exception e) {
            logger.error("授权异常,原因", e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.S_CALL_AUTH_INTERFACE_FAIL_FAULT, e);
        }
        if (AuthItemCheckResCodeEnum.APPROVE_CODE.getCode() != result) {
            logger.error("分期录入授权失败,卡号：[{}]", authRecordedDTO.getAuthCardNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.S_AUTH_FAIL_FAULT, InstallRepDetailEnum.NULL, authRecordedDTO.getAuthCardNumber());
        }
        if (authRecordedDTO.getAuthTrancactionStatus() == null || AuthTrancactionStatusEnum.ERROR_STATUS.getCode().equals(authRecordedDTO.getAuthTrancactionStatus())) {
            logger.error("分期录入授权检查不通过,卡号：[{}]", authRecordedDTO.getAuthCardNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ENTRY_AUTH_FAIL_FAULT, InstallRepDetailEnum.AU_CH, authRecordedDTO.getAuthCardNumber());
        }
    }

    /**
     * 存储分期入账
     *
     */
    private InstallEntryResDTO insertInstallRecord(AuthRecordedDTO authRecordedDTO,
                                                   InstallEntryDTO installEntryDTO,
                                                   Boolean flag,
                                                   Exception e) {
        //分期流水
        InstallRecordDTO installCreatJrnDTO = buildInstallRecordDTO(installEntryDTO);
        installCreatJrnDTO.setAuthTransmisionTime(authRecordedDTO.getAuthTransmissionTime());

        if (flag) {
            installCreatJrnDTO.setAccountManagementId(authRecordedDTO.getInstallAccountManagerId());
            installCreatJrnDTO.setGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
            installCreatJrnDTO.setTransactionInd(InstallTransactionIndEnum.SUCCESS.getCode());
            installCreatJrnDTO.setAuthorizationCode(authRecordedDTO.getAuthAuthCode());
            installCreatJrnDTO.setInstallTerm(authRecordedDTO.getTerm());
            installCreatJrnDTO.setLimitCode(authRecordedDTO.getAuthCreditLimitNodeId());
            if (Objects.equals(InstallmentTypeEnum.CASH_INSTALL.getCode(), installEntryDTO.getInstallType())) {
                installCreatJrnDTO.setFundProcessFlag(FundProcessFlagEnum.UNHANDLED.getCode());
            }
            installRecordService.add(installCreatJrnDTO);
            installEntryDTO.setInstallRecordId(installCreatJrnDTO.getId());

            return InstallEntryResDTO.InstallEntryResDTOBuilder.anInstallEntryResDTO()
                    .withOrderId(installEntryDTO.getOrderId())
                    .withCardNumber(installEntryDTO.getCardNumber())
                    .withAuthorizationCode(authRecordedDTO.getAuthAuthCode())
                    .withInstallmentProductCode(installEntryDTO.getProductCode())
                    .build();

        } else {
            installCreatJrnDTO.setTransactionInd(InstallTransactionIndEnum.FAIL.getCode());
            installCreatJrnDTO.setFailCode(authRecordedDTO.getAuthResponseCode());
            installCreatJrnDTO.setFailReason(getFailReason(authRecordedDTO, e));
            installRecordService.add(installCreatJrnDTO);
            installEntryDTO.setInstallRecordId(installCreatJrnDTO.getId());

            if (AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode().equals(authRecordedDTO.getAuthTrancactionStatus())) {
                authDetailDataModifyService.addAuthorizationLog(authRecordedDTO);
            }
        }

        return InstallEntryResDTO.InstallEntryResDTOBuilder.anInstallEntryResDTO()
                .withOrderId(null)
                .build();
    }

    /**
     * 检查是否已分期
     *
     * @param instalAmount        分期金额
     * @param originTransactionId 原交易id
     */
    private PostedTransactionDTO checkAreadyInstall( BigDecimal instalAmount,
                                                    String originTransactionId,
                                                    String installProductCode,
                                                    String orgNum,
                                                    String cardNumber,
                                                    String installFlag) {
        //判断单笔分期是否入账

        //查看已入账交易信息表
        PostedTransaction postTransaction = postedTransactionMapper.selectByPrimaryKey(originTransactionId);


        if (ObjectUtils.isEmpty(postTransaction)) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_POSTED_TRANSACTION_ID_NOT_NULL_FAULT);
        }
        if (!Objects.equals(postTransaction.getCardNumber(), cardNumber)) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_POSTED_TRANSACTION_ID_NOT_CORRESPONDENCE_NULL_FAULT);
        }
        if (InstallmentTypeEnum.RESET_INSTALL.getCode().equals(installFlag)) {
            if (!Objects.equals(postTransaction.getDebitCreditIndcator(), "D") || Objects.equals(postTransaction.getReversalIndicator(), "Y")
                    || Objects.equals(postTransaction.getInstallmentIndicator(), "Y")) {
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORIGIN_TRANS_NOT_ALLOWED_INST_FAULT);
            }
        }
        if (InstallmentTypeEnum.SINGLE_INSTALL.getCode().equals(installFlag)) {
            if (!Objects.equals(postTransaction.getDebitCreditIndcator(), "D") || Objects.equals(postTransaction.getReversalIndicator(), "Y")
                    || Objects.equals(postTransaction.getInstallmentIndicator(), "Y") || (!Objects.equals(postTransaction.getStatementDate(), "")
                    && postTransaction.getStatementDate() != null) || (postTransaction.getInstallmentOrderId() != null && !Objects.equals(postTransaction.getInstallmentOrderId(), ""))) {
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORIGIN_TRANS_NOT_ALLOWED_INST_FAULT);
            }
        }

        PostedTransactionDTO postedTransaction = BeanMapping.copy(postTransaction, PostedTransactionDTO.class);
        //分期交易标志
        if (postedTransaction != null) {
            InstallProductInfoResDTO installProduct = installProductInfoService.findByIndex(orgNum, installProductCode);
            List<String> installTypeTransCodes = null;
            try {
                List<InstallTypeSupportTxnResDTO> installTypeSupportTxns = installTypeSupportTxnService.getByTypeAndOrgNum(installProduct.getProdType(), orgNum);
                installTypeTransCodes = installTypeSupportTxns.stream().map(InstallTypeSupportTxnResDTO::getTransactionCode).collect(Collectors.toList());
            } catch (Exception e) {
                logger.error(e.getMessage());
            }
            if ((!CollectionUtils.isEmpty(installTypeTransCodes) && !installTypeTransCodes.contains(postedTransaction.getPostingTransactionCode()))) {
                logger.error("原始交易不允许分期,已入账id：{},分期类型:{},交易码：{}",
                        postedTransaction.getPostedTransactionId(), installProduct.getProdType(), postedTransaction.getPostingTransactionCode());
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORIGIN_TRANS_NOT_ALLOWED_INST_FAULT);
            }
            //不支持分期的MCC
            InstallTypeSupportMcc installTypeSupportMcc = installTypeSupportMccSelfMapper.selectByOrgTypeAndMcc(orgNum, installProduct.getProdType(), postedTransaction.getMcc());
            if (null != installTypeSupportMcc) {
                logger.error("原始交易的MCC不允许分期,已入账id：{},分期类型:{},交易码：{}",
                        postedTransaction.getPostedTransactionId(), installProduct.getProdType(), postedTransaction.getPostingTransactionCode());
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORIGIN_TRANS_NOT_ALLOWED_INST_FAULT, InstallRepDetailEnum.MCC_OR_TR);
            }
            BigDecimal alreadyInstAmount = postedTransaction.getInstAmount() == null ? BigDecimal.ZERO : postedTransaction.getInstAmount();
            List<PostedTranAccountRelationInfo> tranAccountRelationInfos = postedTranAccountRelationInfoSelfMapper.selectTransIdAndAmountByPostId(postedTransaction.getPostedTransactionId(), postedTransaction.getOrganizationNumber());
            List<String> transList = tranAccountRelationInfos.stream().map(PostedTranAccountRelationInfo::getTransactionBalanceId).collect(Collectors.toList());
            List<AccountBalanceInfo> accountBalanceInfos = accountBalanceInfoSelfMapper.selectBalancesByTransIds(postedTransaction.getOrganizationNumber(), transList);
            BigDecimal totalBalance = BigDecimal.ZERO;
            for(AccountBalanceInfo accountBalanceInfo : accountBalanceInfos){
                ParmTransactionType parmTransactionType = parmTransactionTypeSelfMapper.selectByTypeCode(accountBalanceInfo.getTransactionTypeCode(), accountBalanceInfo.getOrganizationNumber());
                if(DebitCreditIndEnum.DEBIT_INDICATOR.getCode().equals(parmTransactionType.getBalanceLoanDirection())){
                    totalBalance = totalBalance.add(accountBalanceInfo.getBalance());
                }
            }

            BigDecimal restBalance = postedTransaction.getPostingAmount().subtract(alreadyInstAmount);
            BigDecimal allowInstallBalance = restBalance.compareTo(totalBalance) >0 ? totalBalance : restBalance;
            //（“交易金额-已分期金额”与“交易对应的借记交易账户的余额”的较小值）
            if (instalAmount.compareTo(allowInstallBalance) > 0) {
                logger.error("累计转分期金额已超出原始交易金额,已入账id：{},分期金额：{}",
                        postedTransaction.getPostedTransactionId(), instalAmount);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_AMOUNT_BEYOND_ORIGIN_TRANS_MOUNT_FAULT);
            }
        }
        return postedTransaction;
    }

    /**
     * 检查账单分期
     *
     * @param installEntryDTO 分期参数
     */
    private void checkStatementInstall(InstallEntryDTO installEntryDTO, AccountManagementInfo accountManagementInfo) {
        List<AccountStatementInfoDTO> statementInfoList = findLastedStatementInfo(installEntryDTO.getAccountManagementId());
        if (CollectionUtils.isEmpty(statementInfoList)) {
            logger.error("根据管理账户{},查询ACCOUNT_STATEMENT_INFO表,未查到数据", installEntryDTO.getAccountManagementId());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ACCT_STATEMENT_INFO_NOT_EXIST_FAULT);
        }
        AccountStatementInfoDTO statementInfo = statementInfoList.get(0);

        //账单分期金额校验取公共逻辑的计算结果
        BigDecimal availableStatementAmount = iInstallBillEntryAppService.getAvailableStatementAmount(accountManagementInfo.getAccountManagementId(), statementInfo,accountManagementInfo.getStatementDueAmount());

        if (installEntryDTO.getInstallAmount().compareTo(availableStatementAmount) > 0) {
            logger.error("账单分期金额{}大于可分期金额{}",installEntryDTO.getInstallAmount(), availableStatementAmount);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_AMOUNT_NOT_CORRECT_FAULT);
        }

        if (installEntryDTO.getTransactionDate().isAfter(statementInfo.getPaymentDueDate())) {
            logger.error("交易日期{}大于还款日{}", installEntryDTO.getTransactionDate(), statementInfo.getPaymentDueDate());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_TRANS_DATE_GREATER_THAN_PAYMENT_DUE_DATE_FAULT);
        }
        // 账单分期生成订单时，app账单分期和TXN账单分期互斥
        List<String> installTypes = Arrays.asList(InstallmentTypeEnum.APP_STATEMENT_INSTALL.getCode(), InstallmentTypeEnum.STATEMENT_INSTALL.getCode());
        List<InstallRecord> installRecords = installRecordSelfMapper.selectByOrgManageIdAndInstallTypes(installEntryDTO.getOrganizationNumber(), installEntryDTO.getAccountManagementId(), installTypes);
        for (InstallRecord installRecord : installRecords) {
            if (Objects.equals(InstallTransactionIndEnum.SUCCESS.getCode(), installRecord.getTransactionInd()) && installRecord.getStatementDate().isEqual(statementInfo.getStatementDate())) {
                logger.error("当期账单已经分期,账单id：{}", statementInfo.getStatementId());
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_CURRENT_STATEMENT_ALREADY_INST_FAULT);
            }
        }
        installEntryDTO.setStatementDate(statementInfo.getStatementDate());
    }

    /**
     * 获取上一账单账户
     */
    private List<AccountStatementInfoDTO> findLastedStatementInfo(String accountManageInfoId) {
        List<AccountStatementInfo> infos = this.accountStatementInfoSelfMapper.selectLastedStatementInfoByAccountManagementIdAndDate(accountManageInfoId);
        return CollectionUtils.isEmpty(infos) ? null : BeanMapping.copyList(infos, AccountStatementInfoDTO.class);
    }

    /**
     * 构建授权接口数据
     *
     * @param installEntryDTO           页面参数
     * @param installAccountTranConf    分期产品
     * @param term                      期数
     * @param authTransactionType       交易大类
     * @param authTransactionTypeDetail 交易细类
     * @param authRecordedDTO
     * @return AuthRecordedDTO
     */
    private AuthRecordedDTO buildAuthRecordedDTO(InstallEntryDTO installEntryDTO,
                                                 InstallAccountingTransParmResDTO installAccountTranConf,
                                                 int term,
                                                 String authTransactionType,
                                                 String authTransactionTypeDetail,
                                                 AuthRecordedDTO authRecordedDTO) {

        logger.info("分期类型:{},管理账户ID:{}", installEntryDTO.getInstallType(), installEntryDTO.getAccountManagementId());
        if (Objects.equals(InstallmentTypeEnum.SINGLE_INSTALL.getCode(), installEntryDTO.getInstallType())
                || Objects.equals(InstallmentTypeEnum.STATEMENT_INSTALL.getCode(), installEntryDTO.getInstallType())
                || Objects.equals(InstallmentTypeEnum.AUTO_INSTALL.getCode(), installEntryDTO.getInstallType())
                || Objects.equals(InstallmentTypeEnum.APP_STATEMENT_INSTALL.getCode(), installEntryDTO.getInstallType())
                || Objects.equals(InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode(), installEntryDTO.getInstallType())
                || Objects.equals("RD", installEntryDTO.getInstallType())
                || Objects.equals("LD", installEntryDTO.getInstallType())) {
            authRecordedDTO.setInstallAccountManagerId(installEntryDTO.getAccountManagementId());
        }
        authRecordedDTO.setCustomerRegion(installEntryDTO.getCustomerRegion());
        authRecordedDTO.setOrganizationNumber(installEntryDTO.getOrganizationNumber());
        authRecordedDTO.setAuthCardNumber(installEntryDTO.getCardNumber());
        authRecordedDTO.setInstallmentProductCode(installEntryDTO.getProductCode());
        authRecordedDTO.setInstallmentType(installEntryDTO.getInstallType());
        authRecordedDTO.setOriginTransactionId(installEntryDTO.getOriginTransactionId());
        authRecordedDTO.setAcquireReferenceNo(installEntryDTO.getAcquireReferenceNo());
        authRecordedDTO.setInstallmentPriceFlag(installEntryDTO.getInstallPriceFlag());
        authRecordedDTO.setInstallmentDerateMethod(installEntryDTO.getInstallDerateMethod());
        authRecordedDTO.setInstallmentDerateValue(installEntryDTO.getInstallDerateValue());
        authRecordedDTO.setMerchantId(installEntryDTO.getMerchantId());
        authRecordedDTO.setAuthCardAcceptorNameLocation(installEntryDTO.getMerchantName());
        authRecordedDTO.setAuthTransactionAmount(installEntryDTO.getInstallAmount());
        authRecordedDTO.setAuthCardholderBillingAmount(BigDecimal.ZERO);
        String cardExpirationDate = installEntryDTO.getCardExpirationDate();
        if (StringUtils.isEmpty(cardExpirationDate)) {
            CardAuthorizationInfo cardAuthorizationInfo = installmentThreadLocalHolder.setCardAuthorization(installEntryDTO.getCardNumber());
            // 批中需要检查缓存
            if (CustAccountBO.isBatch()) {
                CardAuthorizationDTO cardAuthorization = BeanMapping.copy(cardAuthorizationInfo, CardAuthorizationDTO.class);
                CustAccountBO.threadCustAccountBO.get().getAuthBO().checkCardAuthorization(cardAuthorization);
                cardExpirationDate = cardAuthorization.getExpireDate();
            } else {
                cardExpirationDate = cardAuthorizationInfo.getExpireDate();
            }
        }
        authRecordedDTO.setAuthCardExpirationDate(cardExpirationDate);
        authRecordedDTO.setAuthMerchantType(installEntryDTO.getMcc());
        authRecordedDTO.setAuthTransactionFee("0");
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode());
        authRecordedDTO.setAuthTransactionCurrencyCode(installEntryDTO.getInstallCcy());
        authRecordedDTO.setAuthAuthTypeCode(AuthTypeCodeEnum.REGULAR_AUTH.getCode());
        authRecordedDTO.setAuthTransactionTypeTopCode(authTransactionType);
        authRecordedDTO.setAuthTransactionTypeDetailCode(authTransactionTypeDetail);
        authRecordedDTO.setAuthTransactionSourceCode(AuthTransactionSourceCodeEnum.THE_BANK.getCode());

        if (Objects.equals(InstallEntryFunctionCodeEnum.INSTALL_CANCLE.getCode(), installEntryDTO.getFunctionCode())) {
            authRecordedDTO.setAuthReversalType(ReversalTypeEnum.REVOCATION_TRANS.getCode());
            authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.REVOCATION_TRANS.getCode());
            //原始系统跟踪号
            authRecordedDTO.setAuthOriginalSystemTraceAuditNumber(InstallmentConstant.AUTH_SYSTEM_TRACEAUDIT_NUMBER);
            //原始受理机构标识码
            authRecordedDTO.setAuthOriginalAcquiringIdentificationCode(InstallmentConstant.IDENTIFICATION_CODE);
            //原始发送机构标识码
            authRecordedDTO.setAuthOriginalForwardingIdentificationCode(InstallmentConstant.IDENTIFICATION_CODE);
        } else if (InstallEntryFunctionCodeEnum.AUTO_INSTALL_ENTRY.getCode().equals(installEntryDTO.getFunctionCode()) || InstallEntryFunctionCodeEnum.INSTLL_ENTRY.getCode().equals(installEntryDTO.getFunctionCode())) {
            authRecordedDTO.setAuthReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
            authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
            //系统跟踪号
            authRecordedDTO.setAuthSystemTraceAuditNumber(InstallmentConstant.AUTH_SYSTEM_TRACEAUDIT_NUMBER);
            //受理机构标识码
            authRecordedDTO.setAuthAcquiringIdentificationCode(InstallmentConstant.IDENTIFICATION_CODE);
            //发送机构标识码
            authRecordedDTO.setAuthForwardingIdentificationCode(InstallmentConstant.IDENTIFICATION_CODE);
        }
        authRecordedDTO.setTerm(term);
        authRecordedDTO.setInstallmentTotalFee(installEntryDTO.getInstallTotalFee());
        authRecordedDTO.setInstallmentTotalInterest(installEntryDTO.getInstallInterestAmount());

        authRecordedDTO.setInstallmentFeeRate(installEntryDTO.getInstallFeeRate());
        authRecordedDTO.setTransactionDesc(installEntryDTO.getTransactionDesc());
        authRecordedDTO.setPostingTransactionCode(installAccountTranConf.getInstallTransactionCode());
        authRecordedDTO.setPostingTransactionCodeRev(installAccountTranConf.getInstallReturnCodeRev());

       /* if (!Objects.equals(InstallmentTypeEnum.CASH_INSTALL.getCode(), installEntryDTO.getInstallType())) {
            authRecordedDTO.setPostingTransactionCode(installAccountTranConf.getInstallTransactionCode());
            authRecordedDTO.setPostingTransactionCodeRev(installAccountTranConf.getInstallReturnCodeRev());
        }*/
        if (Objects.equals(InstallmentTypeEnum.CASH_INSTALL.getCode(),
                authRecordedDTO.getInstallmentType())){
            if (!installEntryDTO.getProductCode().startsWith("IPP")){
                authRecordedDTO.setAuthTransactionMedia("1");
            }
        }

        authRecordedDTO.setAuthBillingCurrencyCode(installEntryDTO.getInstallCcy());
        authRecordedDTO.setAuthGlobalFlowNumber(IdGeneratorManager.sequenceIdGenerator().generateSeqId());

        //83版本报文中受卡方日期2位 受卡方时间12位
        authRecordedDTO.setAuthLocalTransactionDate(DateTimeFormatter.ofPattern("yy").format(installEntryDTO.getTransactionDate()));
        authRecordedDTO.setAuthLocalTransactionTime(DateTimeFormatter.ofPattern("yyMMddHHmmss").format(
                installEntryDTO.getTransactionDate().atTime(LocalTime.now())));

        authRecordedDTO.setAuthTransmissionTime(DateTimeFormatter.ofPattern("MMddHHmmss").format(
                LocalDateTime.now()));
        return authRecordedDTO;
    }

    /**
     * 校验字段
     *
     * @param installEntryDTO 页面字段
     */
    private void checkInstallRecoder(InstallEntryDTO installEntryDTO) {
        logger.info("开始校验分期字段");
        //机构号
        if (StringUtils.isEmpty(installEntryDTO.getOrganizationNumber())) {
            logger.error("分期录入 机构号必输");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.OR_ST);
        }

        if (StringUtils.isBlank(installEntryDTO.getCardNumber())) {
            logger.error("分期录入 卡号必输");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.RE_CA_IN);
        }
        //分期产品代码
        if (StringUtils.isEmpty(installEntryDTO.getProductCode())) {
            logger.error("分期录入 分期产品代码必输");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.CO_PRO);
        }
        //分期类型
        if (StringUtils.isEmpty(installEntryDTO.getInstallType())) {
            logger.error("分期录入 分期类型必输");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.PE_RE);
        }

        OrganizationInfoResDTO paramOrganizationInfo = organizationInfoService.findOrganizationInfo(installEntryDTO.getOrganizationNumber());
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(installEntryDTO.getCardNumber(),installEntryDTO.getOrganizationNumber());
        CustReconciliationControlDTO control = custReconciliationControlService.getControl(cardAuthorizationInfo.getPrimaryCustomerId(), cardAuthorizationInfo.getOrganizationNumber());
        LocalDate date = custReconciliationControlService.getBillingDate(control, paramOrganizationInfo.getAccruedThruDay(), paramOrganizationInfo.getToday(), paramOrganizationInfo.getNextProcessingDay());

        //批中不验证

        LocalDate reconciliationDate = control.getReconciliationDate();
        if(!reconciliationDate.isBefore(paramOrganizationInfo.getAccruedThruDay())){
            //检查交易日期必须是合法日期且不能大于下一处理日
            if (installEntryDTO.getTransactionDate() == null || installEntryDTO.getTransactionDate().isAfter(date)) {
                logger.error("分期录入 交易日期必输且不能大于{}",date);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.TR_DA_IN, date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }
        }
        //分期币种
        if (StringUtils.isEmpty(installEntryDTO.getInstallCcy())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.IN_CU);
        }
        //分期金额
        if (installEntryDTO.getInstallAmount() == null || installEntryDTO.getInstallAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.IN_AM);
        }

        //分期定价方式
        if (StringUtils.isEmpty(installEntryDTO.getInstallPriceFlag())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.ST_PR_ST);
        }

        if (Objects.equals(InstallEntryFunctionCodeEnum.INSTALL_CANCLE.getCode(), installEntryDTO.getFunctionCode())) {
            //转入卡卡号
            if (StringUtils.isEmpty(installEntryDTO.getTransferCard())) {
                logger.error("分期录入现金分期 转入卡卡号必输");
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.TR_CA);
            }
            //转入卡持卡人姓名
            if (StringUtils.isEmpty(installEntryDTO.getTransferCardHolderName())) {
                logger.error("分期录入现金分期 转入卡持卡人姓名必输");
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.CA_TR_TA);
            }
        }
    }

    /**
     * 构建分期流水数据
     *
     * @param installEntryDTO 页面参数
     * @return InstallRecordDTO
     */
    private InstallRecordDTO buildInstallRecordDTO(InstallEntryDTO installEntryDTO) {
        //分期创建流水表
        InstallRecordDTO installRecordDTO = new InstallRecordDTO();
        installRecordDTO.setId(IdGeneratorManager.sequenceIdGenerator().generateSeqId());
        if (Objects.equals(InstallEntryFunctionCodeEnum.INSTALL_CANCLE.getCode(), installEntryDTO.getFunctionCode())) {
            installRecordDTO.setOriginId(installEntryDTO.getId());
        }
        installRecordDTO.setFunctionCode(installEntryDTO.getFunctionCode());
        installRecordDTO.setOriginTransactionId(installEntryDTO.getOriginTransactionId());
        installRecordDTO.setOrganizationNumber(installEntryDTO.getOrganizationNumber());
        installRecordDTO.setAccountManagementId(installEntryDTO.getAccountManagementId());
        installRecordDTO.setCardNumber(installEntryDTO.getCardNumber());
        installRecordDTO.setProductCode(installEntryDTO.getProductCode());
        installRecordDTO.setInstallType(installEntryDTO.getInstallType());
        installRecordDTO.setTransactionDate(installEntryDTO.getTransactionDate());
        installRecordDTO.setAcquireReferenceNo(installEntryDTO.getAcquireReferenceNo());
        installRecordDTO.setCardExpirationDate(installEntryDTO.getCardExpirationDate());
        installRecordDTO.setInstallTerm(installEntryDTO.getInstallTerm());
        installRecordDTO.setInstallCcy(installEntryDTO.getInstallCcy());
        installRecordDTO.setInstallAmount(installEntryDTO.getInstallAmount());
        installRecordDTO.setInstallPriceFlag(installEntryDTO.getInstallPriceFlag());
        installRecordDTO.setInstallDerateMethod(installEntryDTO.getInstallDerateMethod());
        installRecordDTO.setInstallDerateValue(installEntryDTO.getInstallDerateValue());
        installRecordDTO.setInstallTotalFee(installEntryDTO.getInstallTotalFee());
        installRecordDTO.setInstallFeeRate(installEntryDTO.getInstallFeeRate());
        installRecordDTO.setLimitCode(installEntryDTO.getLimitCode());
        installRecordDTO.setMerchantId(installEntryDTO.getMerchantId());
        installRecordDTO.setMcc(installEntryDTO.getMcc());
        installRecordDTO.setTransactionDesc(installEntryDTO.getTransactionDesc());
        installRecordDTO.setTransferCard(installEntryDTO.getTransferCard());
        installRecordDTO.setTransferBank(installEntryDTO.getTransferBank());
        installRecordDTO.setTransferBranch(installEntryDTO.getTransferBranch());
        installRecordDTO.setTransferBankName(installEntryDTO.getTransferBankName());
        installRecordDTO.setTransferCardHolderName(installEntryDTO.getTransferCardHolderName());
        installRecordDTO.setCustomerTelephone(installEntryDTO.getCustomerTelephone());
        installRecordDTO.setApplyIdNo(installEntryDTO.getApplyIdNo());
        installRecordDTO.setApplyIdType(installEntryDTO.getApplyIdType());
        installRecordDTO.setCreateTime(LocalDateTime.now());
        installRecordDTO.setUpdateBy("admin");
        installRecordDTO.setVersionNumber(1L);
        installRecordDTO.setStatementDate(installEntryDTO.getStatementDate());
        installRecordDTO.setCustomerRegion(installEntryDTO.getCustomerRegion());
        return installRecordDTO;
    }

    /**
     * 错误原因
     *
     * @param authRecordedDTO 授权参数
     * @return String
     */
    private String getFailReason(AuthRecordedDTO authRecordedDTO, Exception e) {

        log.error("创建分期订单异常 ", e);

        if (authRecordedDTO != null && StringUtils.isNotBlank(authRecordedDTO.getErrorDetailCode())){
            return  authRecordedDTO.getErrorDetailCode() +
                    ":" + AuthResponseCodeEnum.getErrorDetailDesc(authRecordedDTO.getErrorDetailCode());
        }

        if (e == null){
            return "";
        }

        String message  = e.getCause() == null ? Optional.ofNullable(e.getMessage()).orElse("")
                : Optional.ofNullable(e.getCause().getMessage()).orElse("");


        if (message.length() > 251){
            message = message.substring(message.length() - 251);
        }

        return message;
    }

    private InstallTypeParmResDTO getIntallTypeByOrgNumAndType(String organizationNumber, String type) {
        InstallTypeParmResDTO result = installTypeParmService.findByOrgNumAndType(organizationNumber, type);
        if (result == null) {
            logger.error("根据机构号、类型查询分期类型失败");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_TYPE_PARAM_NOT_EXIST_FAULT);
        }
        return result;
    }
}
