package com.anytech.anytxn.installment.service;


import cn.hutool.core.collection.CollectionUtil;
import com.anytech.anytxn.common.core.enums.DebitCreditIndicatorEnum;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.account.domain.dto.AccountStatementInfoDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatementInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.installment.mapper.InstallRecordSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallRecord;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionSelfMapper;
import com.anytech.anytxn.installment.base.domain.dto.*;
import com.anytech.anytxn.installment.base.enums.*;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.service.IInstallBillEntryAppService;
import com.anytech.anytxn.installment.base.service.IInstallEntryAppService;
import com.anytech.anytxn.installment.base.service.IInstallStagingListServiceApp;
import com.anytech.anytxn.installment.service.manager.InstallManager;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallTypeParmResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallTypeSupportTxnResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallTypeParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallTypeSupportTxnService;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardProductInfoSelfMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallTypeSupportMccSelfMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallTypeSupportMerchantSelfMapper;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeSupportMcc;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeSupportMerchant;
import com.anytech.anytxn.business.dao.transaction.model.PostedTransaction;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 账单分期业务逻辑实现
 *
 * <AUTHOR>
 * @date 2024/01/05
 */
@Service
public class InstallBillEntryAppServiceImpl implements IInstallBillEntryAppService {

    private Logger logger = LoggerFactory.getLogger(InstallBillEntryAppServiceImpl.class);

    @Autowired
    private AccountStatementInfoSelfMapper accountStatementInfoSelfMapper;

    @Autowired
    private IInstallStagingListServiceApp installStagingListService;

    @Autowired
    private IInstallEntryAppService installEntryAppService;

    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;

    @Autowired
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;

    @Autowired
    private InstallRecordSelfMapper installRecordSelfMapper;

    @Autowired
    ParmCardProductInfoSelfMapper cardProductInfoSelfMapper;

    @Autowired
    private IOrganizationInfoService organizationInfoService;

    @Autowired
    private IInstallTypeParmService installTypeParmService;

    @Resource
    private InstallManager installManager;

    @Autowired
    private IInstallTypeSupportTxnService installTypeSupportTxnService;

    @Autowired
    private InstallTypeSupportMccSelfMapper installTypeSupportMccSelfMapper;

    @Autowired
    private InstallTypeSupportMerchantSelfMapper installTypeSupportMerchantSelfMapper;
    @Autowired
    private PostedTransactionSelfMapper postedTransactionSelfMapper;

    /**
     *  根据交易码+商户类别 过滤部分交易
     * @param accountManagementId
     * @param accountStatementInfoDTO
     * @return
     */
    @Override
    public BigDecimal getAvailableStatementAmount(String accountManagementId,AccountStatementInfoDTO accountStatementInfoDTO,BigDecimal availableStatementAmount){
        BigDecimal statementAmount = availableStatementAmount;
        List<String> transCodes = installTypeSupportTxnService.getByTypeAndOrgNum(InstallmentTypeEnum.APP_STATEMENT_INSTALL.getCode(),
                accountStatementInfoDTO.getOrganizationNumber())
                .stream()
                .map(InstallTypeSupportTxnResDTO::getTransactionCode).collect(Collectors.toList());

        List<String> transMccs = installTypeSupportMccSelfMapper.selectByTypeAndOrgNum(InstallmentTypeEnum.APP_STATEMENT_INSTALL.getCode(),
                            OrgNumberUtils.getOrg()).stream().map(InstallTypeSupportMcc::getMcc).collect(Collectors.toList());
        List<String> exTransMerchantIds = installTypeSupportMerchantSelfMapper.selectByTypeAndOrgNum(InstallmentTypeEnum.APP_STATEMENT_INSTALL.getCode(),
                            OrgNumberUtils.getOrg()).stream().map(InstallTypeSupportMerchant::getMerchantId).collect(Collectors.toList());
                List<PostedTransaction> postedTransactions = postedTransactionSelfMapper.selectByAccStaId(accountStatementInfoDTO.getStatementId(), accountStatementInfoDTO.getOrganizationNumber());
        logger.info("账单分期-----交易码:{}---限制商户类别mcc:{}---限制商户号:{}",transCodes,transMccs,exTransMerchantIds);
        if (CollectionUtils.isEmpty(postedTransactions)){
            return BigDecimal.ZERO;
        }
        //过滤掉借记交易
        postedTransactions = postedTransactions.stream().filter(t-> DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode().equals(
                t.getDebitCreditIndcator())).collect(Collectors.toList());
        //查询出不支持的交易码的交易记录
        List<PostedTransaction> transactionsTransactions = postedTransactions.stream().filter(t -> CollectionUtil.isNotEmpty(transCodes) &&
                !transCodes.contains(t.getPostingTransactionCode())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(transactionsTransactions)){
            BigDecimal transAmount = transactionsTransactions.stream().map(PostedTransaction::getPostingAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            statementAmount = statementAmount.subtract(transAmount);
            //过滤出支持该交易码的交易记录
            postedTransactions = postedTransactions.stream().filter(t -> CollectionUtil.isNotEmpty(transCodes) &&
                    transCodes.contains(t.getPostingTransactionCode())).collect(Collectors.toList());
        }
         logger.info("入账记录-----:{}",postedTransactions);
        if (CollectionUtil.isNotEmpty(postedTransactions)){
            List<PostedTransaction> mccTransactions =postedTransactions.stream().filter(t -> CollectionUtil.isNotEmpty(transMccs) &&
                    transMccs.contains(t.getMcc())).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(mccTransactions)){
                BigDecimal mccAmount = mccTransactions.stream().map(PostedTransaction::getPostingAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                statementAmount = statementAmount.subtract(mccAmount);
                postedTransactions =postedTransactions.stream().filter(t -> CollectionUtil.isNotEmpty(transMccs) &&
                        !transMccs.contains(t.getMcc())).collect(Collectors.toList());
            }

            List<PostedTransaction> merchantIdsTransactions =postedTransactions.stream().filter(t -> CollectionUtil.isNotEmpty(exTransMerchantIds) &&
                    exTransMerchantIds.contains(t.getMerchantId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(merchantIdsTransactions)){
                BigDecimal transAmount = merchantIdsTransactions.stream().map(PostedTransaction::getPostingAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                statementAmount = statementAmount.subtract(transAmount);
            }
        }


        statementAmount = statementAmount.max(BigDecimal.ZERO);

        InstallTypeParmResDTO installTypeParmRes = installTypeParmService.findByOrgNumAndType(
                accountStatementInfoDTO.getOrganizationNumber(), InstallmentTypeEnum.APP_STATEMENT_INSTALL.getCode());
        //账单分期可分期金额百分比
        BigDecimal statementInstallPer = installTypeParmRes.getStatementInstallPer();

        statementAmount = statementAmount.multiply(statementInstallPer.divide(BigDecimal.valueOf(100)))
                .setScale(2, RoundingMode.HALF_UP);


        logger.info("statementAmount{},statementInstallPer:{}", statementAmount, statementInstallPer);

        return statementAmount;
    }


    /**
     * 账单分期查询
     *
     * @param accountManagementId 管理账户id
     * @return {@link InstallTradingDTO}
     */
    @Override
    public List<InstallTradingDTO> findBillInstall(InstallTradingAppDTO installTradingAppDTO, String accountManagementId, String cardNumber, BigDecimal availableStatementAmount) {
        if (StringUtils.isBlank(accountManagementId)) {
            logger.warn("账单分期查询 账号不能为空");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.AC_BI);
        }
        List<InstallTradingDTO> installTradingDtos = new ArrayList<>();
        List<AccountStatementInfoDTO> lastedStatementInfo = findLastedStatementInfo(accountManagementId);
        if (!lastedStatementInfo.isEmpty()) {
            //获取账单日最大的一条记录
            AccountStatementInfoDTO accountStatementInfoDTO = lastedStatementInfo.get(0);
            OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(accountStatementInfoDTO.getOrganizationNumber());
            LocalDate nextProcessingDay = organizationInfo.getNextProcessingDay();
             availableStatementAmount = getAvailableStatementAmount(accountManagementId, accountStatementInfoDTO,availableStatementAmount);
            if (availableStatementAmount.compareTo(BigDecimal.ZERO) <= 0){
                return installTradingDtos;
            }
            InstallTradingSearchKeyDTO installTradingSearchKeyDTO = new InstallTradingSearchKeyDTO();
            installTradingSearchKeyDTO.setOrganizationNumber(organizationInfo.getOrganizationNumber());
            installTradingSearchKeyDTO.setInstallAmount(availableStatementAmount);
            installTradingSearchKeyDTO.setType(InstallmentTypeEnum.APP_STATEMENT_INSTALL.getCode());
            installTradingSearchKeyDTO.setTerm(0);
            installTradingSearchKeyDTO.setCardNumber(cardNumber);
            installTradingSearchKeyDTO.setAccountManagementId(accountManagementId);
            //交易日期
            installTradingSearchKeyDTO.setTransDate(nextProcessingDay);
            //还款日
            installTradingSearchKeyDTO.setPaymentDate(accountStatementInfoDTO.getPaymentDueDate());
            //返回账单当月还款日
            installTradingAppDTO.setPaymentDueDate(accountStatementInfoDTO.getPaymentDueDate());
            // 账单分期生成订单时，app账单分期和TXN账单分期互斥
            List<InstallRecord> installRecords = installRecordSelfMapper.selectByOrgManageIdAndInstallTypes(organizationInfo.getOrganizationNumber(), accountManagementId, Arrays.asList(InstallmentTypeEnum.APP_STATEMENT_INSTALL.getCode(),InstallmentTypeEnum.STATEMENT_INSTALL.getCode()));
            if (!installRecords.isEmpty()) {
                List<InstallRecord>  records = installRecords.stream().filter(t -> InstallTransactionIndEnum.SUCCESS.getCode().equals(t.getTransactionInd())
                        &&t.getStatementDate() !=null  && accountStatementInfoDTO.getStatementDate().isEqual(t.getStatementDate())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(records)) {
                    //当前账单已经分期,返回列表为空
                    return installTradingDtos;
                } else {
                    return installStagingListService.findInstallTradingByOptions(installTradingSearchKeyDTO);
                }
            } else {
                return installStagingListService.findInstallTradingByOptions(installTradingSearchKeyDTO);
            }
        }
        return installTradingDtos;
    }

    private List<AccountStatementInfoDTO> findLastedStatementInfo(String accountManageInfoId) {
        try {
            List<AccountStatementInfo> infos = this.accountStatementInfoSelfMapper.selectLastedStatementInfoByAccountManagementIdAndDate(accountManageInfoId);

            if (org.apache.commons.collections4.CollectionUtils.isEmpty(infos)){
                return Collections.emptyList();
            }

            return BeanMapping.copyList(infos, AccountStatementInfoDTO.class);
        } catch (Exception e) {
            logger.error("accountManageInfoId is {} select data error ", accountManageInfoId, e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_SELECT_DATABASE_FAULT);
        }
    }

    /**
     * 账单分期交易赋值及录入
     *
     * @param installEntryDTO {@link InstallEntryDTO}
     * @return Long
     */
    @Override
    public InstallEntryResDTO billInInstallment(InstallEntryDTO installEntryDTO) {
        //必输项检查
        if (StringUtils.isBlank(installEntryDTO.getAccountManagementId())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.BI_IN_TR);
        }

        if (StringUtils.isBlank(installEntryDTO.getProductCode())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.BI_IN_PR);
        }

        if (installEntryDTO.getInstallAmount() == null || installEntryDTO.getInstallAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.BI_IN_AM);
        }

        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installEntryDTO.getOrganizationNumber());
        if (organizationInfo == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORG_INFO_PARAM_NOT_EXIST_FAULT);
        }

        String cardNumber = installManager.getCardNumberByAccountManagementId(installEntryDTO);

        installEntryDTO.setFunctionCode(InstallEntryFunctionCodeEnum.INSTLL_ENTRY.getCode());
        installEntryDTO.setOrganizationNumber(organizationInfo.getOrganizationNumber());
        installEntryDTO.setCardNumber(cardNumber);
        installEntryDTO.setInstallType(InstallmentTypeEnum.APP_STATEMENT_INSTALL.getCode());
        installEntryDTO.setTransactionDate(organizationInfo.getNextProcessingDay());
        installEntryDTO.setInstallPriceFlag(InstallPriceFlagEnum.BASICPRICING_FLAG.getCode());
        installEntryDTO.setInstallTotalFee(BigDecimal.ZERO);
        installEntryDTO.setInstallFeeRate(BigDecimal.ZERO);
        installEntryDTO.setInstallDerateMethod(InstallmentDerateMethodEnum.REDUCTION_NO.getCode());
        installEntryDTO.setInstallDerateValue(BigDecimal.ZERO);

        installManager.getInstallmentProductDesc(installEntryDTO);

        //分期录入
        String orderId = installEntryAppService.entry(installEntryDTO).getOrderId();

        if (StringUtils.isBlank(orderId)){
            logger.error("account manager id {}  statement installment failure ",
                    installEntryDTO.getAccountManagementId());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_BILL_INST_ENTRY_FAIL_FAULT);
        }

        return InstallEntryResDTO.InstallEntryResDTOBuilder.anInstallEntryResDTO()
                    .withCardNumber(cardNumber)
                    .withOrderId(orderId).build();

    }


    @Override
    public List<AccountManagementRecordedDTO> findCardNumberByAccountManagementId(String accountManagementId) {
        if (accountManagementId == null || "".equals(accountManagementId)) {
            logger.warn("账单分期查询 账号不能为空");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.AC_BI);
        }
        List<AccountManagementRecordedDTO> accountManagementRecordedDtos = new ArrayList<>();
        List<AccountStatementInfoDTO> lastedStatementInfoList = findLastedStatementInfo(accountManagementId);
        if (!CollectionUtils.isEmpty(lastedStatementInfoList)) {
            //获取账单日最大的一条记录
            AccountStatementInfoDTO accountStatementInfoDTO = lastedStatementInfoList.get(0);
            //根据账号、账单账户id查询已入帐交易信息表
            if (accountStatementInfoDTO.getStatementId() == null || "".equals(accountStatementInfoDTO.getStatementId())) {
                logger.error("账户账单id不能为空,accountStatementId");
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_ACCT_STATEMENT_ID_NOT_NULL_FAULT);
            }
            AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByAccountManagementIdAndOrganizationNumber(accountManagementId, accountStatementInfoDTO.getOrganizationNumber());
            List<CardAuthorizationInfo> cardAuthorizationList = null;
            if (accountManagementInfo != null) {
                cardAuthorizationList = cardAuthorizationInfoSelfMapper.selectByPrimaryCustomerIdAndOrganizationNumber(accountStatementInfoDTO.getOrganizationNumber(), accountManagementInfo.getCustomerId());
                //primary cusID 查询不到卡片信息时，使用CorpcustomerId进行查询
                if(CollectionUtils.isEmpty(cardAuthorizationList)){
                    cardAuthorizationList = cardAuthorizationInfoSelfMapper.selectByOrgCorpCusId(accountStatementInfoDTO.getOrganizationNumber(), accountManagementInfo.getCustomerId());
                }
            }

            List<String> cardProductNumbers = cardProductInfoSelfMapper.selectByOrgAndAccountProductNum(OrgNumberUtils.getOrg(),
                    accountManagementInfo.getProductNumber()).stream().map(cardProductInfo -> cardProductInfo.getProductNumber())
                    .collect(Collectors.toList());

            if (cardAuthorizationList == null || cardAuthorizationList.isEmpty()) {
                logger.error("账单分期 查询卡片授权表是失败，accountManagementId:{}", accountManagementId);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_CARD_AUTH_NOT_EXIST_FAULT);
            }
            for (CardAuthorizationInfo cardAuthorizationInfo : cardAuthorizationList) {
                if (cardProductNumbers.contains(cardAuthorizationInfo.getProductNumber())) {
                    AccountManagementRecordedDTO accountManagementRecordedDTO = new AccountManagementRecordedDTO();
                    accountManagementRecordedDTO.setOrganizationNumber(cardAuthorizationInfo.getOrganizationNumber());
                    accountManagementRecordedDTO.setProductNumber(cardAuthorizationInfo.getProductNumber());
                    accountManagementRecordedDTO.setCardNumber(cardAuthorizationInfo.getCardNumber());
                    accountManagementRecordedDTO.setCurrency(accountManagementInfo.getCurrency());
                    accountManagementRecordedDTO.setAccountManagementId(accountManagementInfo.getAccountManagementId());
                    accountManagementRecordedDtos.add(accountManagementRecordedDTO);
                }
            }
        }
        return accountManagementRecordedDtos.stream().distinct().collect(Collectors.toList());
    }

}
