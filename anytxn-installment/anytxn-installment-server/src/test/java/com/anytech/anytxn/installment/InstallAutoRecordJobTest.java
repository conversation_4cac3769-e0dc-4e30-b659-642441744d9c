package com.anytech.anytxn.installment;

import com.anytech.anytxn.installment.base.service.IInstallBookPretreatService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 测试类：
 * 生成分期下单记录
 */
@RunWith(SpringRunner.class)
@EnableAutoConfiguration(exclude= {DataSourceAutoConfiguration.class})
@SpringBootTest(classes = AnyTxnInstallmentServerApplicationTest.class)
public class InstallAutoRecordJobTest {

	@Autowired
    IInstallBookPretreatService service;
	/**
	 * 创建分期交易订单
	 */
	@Test
	public void testInstallOrder() {
		service.installBookPretreat(null);
	}

}
