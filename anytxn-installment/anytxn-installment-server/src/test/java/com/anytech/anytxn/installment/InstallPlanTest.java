//package com.anytech.anytxn.installment;
//
//import com.anytech.anytxn.common.core.utils.JacksonUtils;
//import com.anytech.anytxn.installment.dto.InstallmentInterestDTO;
//import com.anytech.anytxn.installment.service.impl.interest.FixInterestRate;
//import com.anytech.anytxn.installment.service.impl.interest.Rule78Interest;
//import com.anytech.anytxn.installment.service.manager.InstallManager;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.logging.log4j.EventLogger;
//import org.apache.logging.log4j.message.StructuredDataMessage;
//import org.junit.Test;
//import org.slf4j.event.EventRecodingLogger;
//
//import java.math.BigDecimal;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.Optional;
//import java.util.UUID;
//
///**
// * @Author: sukang
// * @Date: 2021/10/20 10:12
// */
//@Slf4j
//public class InstallPlanTest {
//
//
//    @Test
//    public void installmentInterestTest() {
//
//
//        InstallmentInterestDTO interestDTO = InstallmentInterestDTO
//                .InstallmentInterestDTOBuilder
//                .anInstallmentInterestDTO()
//                .withAnnualInterestRate(new BigDecimal("7.88"))
//                .withCostAmount(new BigDecimal("1000"))
//                .withTerm(12)
//                .withBalanceMethod("")
//                .build();
//
//        new FixInterestRate().getInterestResult(interestDTO);
//
//        new Rule78Interest().getInterestResult(interestDTO);
//
//        System.out.println(interestDTO.getInterestAmount());
//        System.out.println(interestDTO.getMonthlyInterestAmount());
//
//        BigDecimal reduce = interestDTO.getMonthlyInterestAmount().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
//        System.out.println(reduce);
//        assert reduce.compareTo(interestDTO.getInterestAmount()) == 0;
//    }
//
//
//    @Test
//    public void tRateFee(){
//        int installTerm =  12;
//        BigDecimal installAmount = new BigDecimal("111.12");
//        BigDecimal feeRate = new BigDecimal("23");
//
//
//        BigDecimal decimal = installAmount
//                .multiply(feeRate)
//                .multiply(new BigDecimal(installTerm))
//                .divide(new BigDecimal("1200"), 2, BigDecimal.ROUND_DOWN);
//
//        log.info(decimal.toString());
//    }
//
//    @Test
//    public void installFee() {
//
//        int installTerm = 12;
//        BigDecimal installAmount = new BigDecimal("10000");
//        String balanceMethod = "0";
//        BigDecimal feeRate = new BigDecimal("5.48");
//
//
//        BigDecimal termAmount = installAmount
//                .divide(new BigDecimal(String.valueOf(installTerm)), 0, BigDecimal.ROUND_DOWN);
//
//        //尾款
//        BigDecimal balanceAmount = installAmount
//                .subtract(termAmount.multiply(new BigDecimal(installTerm).subtract(BigDecimal.ONE)));
//
//
//        Map<Integer, BigDecimal> installPlanMap = new HashMap<>();
//
//        for (int i = 1; i <= installTerm; i++) {
//            BigDecimal totalAmount = termAmount;
//            //第一期 & 尾款放第一期
//            if (i == 1 && InstallManager.isFirstTerm(balanceMethod)) {
//                totalAmount = balanceAmount;
//            }
//            //最后一期 & 尾款放最后一期
//            if (i == installTerm && InstallManager.isLastTerm(balanceMethod)) {
//                totalAmount = balanceAmount;
//            }
//            log.info("{}::{}", i, totalAmount);
//
//            BigDecimal termFee = feeRate
//                    .divide(new BigDecimal("1200"), 6, BigDecimal.ROUND_DOWN)
//                    .multiply(totalAmount)
//                    .multiply(new BigDecimal(installTerm - i + 1))
//                    .divide(BigDecimal.ONE, 4, BigDecimal.ROUND_DOWN);
//
//
//            installPlanMap.put(i, termFee);
//        }
//
//        installPlanMap.forEach((k, v) -> {
//            System.out.println(k + "::" + v);
//        });
//
//        Optional<BigDecimal> reduce = installPlanMap.values().stream().reduce(BigDecimal::add);
//
//        if (reduce.isPresent()) { //总利率/分期期数(保留4位 进1)（保留2 直接舍）*分期期数
//
//            System.out.println(reduce.get());
//            BigDecimal multiply = reduce.get().divide(new BigDecimal(installTerm), 4, BigDecimal.ROUND_UP)
//                    .multiply(new BigDecimal(installTerm))
//                    .setScale(2, BigDecimal.ROUND_DOWN);
//
//            System.out.println(multiply);
//        }
//    }
//}
