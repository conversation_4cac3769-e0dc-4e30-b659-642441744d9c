package com.anytech.anytxn.installment;


import com.anytech.anytxn.installment.server.InstallmentServerApplication;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * Installment启动类
 */

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableInstallmentApi
public class AnyTxnInstallmentServerApplicationTest {

	public static void main(String[] args) {
		SpringApplication.run(InstallmentServerApplication.class, args);
	}
}

