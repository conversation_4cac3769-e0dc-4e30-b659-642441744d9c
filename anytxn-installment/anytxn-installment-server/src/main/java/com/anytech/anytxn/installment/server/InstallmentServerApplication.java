package com.anytech.anytxn.installment.server;

import com.anytech.anytxn.installment.EnableInstallmentApi;
import io.micrometer.core.instrument.MeterRegistry;
import com.anytech.anytxn.common.core.annotation.EnableCacheListenerAnnotation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;


/**
 * Installment启动类
 * @EnableScheduling
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableInstallmentApi
@EnableFeignClients(basePackages = "com.anytech.anytxn.transaction.feign")
@ServletComponentScan
@EnableCacheListenerAnnotation
public class InstallmentServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(InstallmentServerApplication.class, args);
    }

    /**
     * 监控配置bean
     */
    @Bean
    MeterRegistryCustomizer<MeterRegistry> configurer(@Value("${spring.application.name}")String applicatonName,
                                                      @Value("${spring.cloud.client.ip-address}") String serverAddr,
                                                      @Value("${server.port}") String port) {
        return (registry) -> registry.config().commonTags("application", applicatonName.concat("-")
                .concat(serverAddr).concat(":").concat(port));
    }

}
