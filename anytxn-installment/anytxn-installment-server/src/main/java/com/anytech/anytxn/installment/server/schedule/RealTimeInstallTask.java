///**
// * Copyright (c) 2020. 北京江融信科技有限公司 版权所有
// * Created on 2020-04-01.
// */
//
//package jrx.anytxn.installment.schedule;
//
//import com.anytech.anytxn.installment.config.RealTimeInstallConfig;
//import com.anytech.anytxn.installment.task.RealTimeInstallTaskHandler;
//import com.anytech.anytxn.transaction.task.BaseDynamicJob;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.net.InetAddress;
//import java.util.Map;
//
///**
// * 分期定时任务
// * 防止引用sdk将定时器放到server
// *
// * <AUTHOR>
// * @date 2020/4/9
// */
//@Component
//public class RealTimeInstallTask extends BaseDynamicJob {
//
//    private static final Logger log = LoggerFactory.getLogger(RealTimeInstallTask.class);
//    @Autowired
//    private RealTimeInstallTaskHandler handler;
//
//    @Autowired
//    private RealTimeInstallConfig realTimeInstallConfig;
//
//    @Override
//    protected void execute() {
//        String partitionKey="";
//        try {
//            String remoteHostname = InetAddress.getLocalHost().getHostName();
//            Map<String, String> ipList = realTimeInstallConfig.getHostnames();
//            log.info("准实时入账，服务器地址为: " + remoteHostname + ",配置文件地址为:" + ipList.toString());
//            if (!ipList.containsKey(remoteHostname)) {
//                log.error("invalid_host, 准实时入账定时任务未在配置服务器上启动，不允许执行!");
//                return;
//            }
//            partitionKey = ipList.get(remoteHostname);
//        } catch (Exception e) {
//            log.error("定时任务拉起失败", e);
//        }
//        log.info("准实时入账处理开始");
//        //1,2实时入账
//        handler.accountRealTime(partitionKey);
//    }
//
//    @Override
//    protected String getCron() {
//        return realTimeInstallConfig.getCron();
//    }
//
//}
