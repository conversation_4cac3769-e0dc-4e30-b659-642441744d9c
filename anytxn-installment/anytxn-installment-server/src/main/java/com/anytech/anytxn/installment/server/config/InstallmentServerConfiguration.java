package com.anytech.anytxn.installment.server.config;

//import com.anytech.anytxn.biz.common.config.db.BusinessDbConfiguration;
//import com.anytech.anytxn.biz.common.config.db.CommonDbConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * todo 删除 CommonDbConfiguration 、 BusinessDbConfiguration
 * 引入数据库配置
 * <AUTHOR>
 */
@Configuration
//@Import({ CommonDbConfiguration.class, BusinessDbConfiguration.class})
public class InstallmentServerConfiguration {

}