# AnyTXN信用卡核心系统-父项目 (AnyTXN Parent Project)

本项目是 AnyTXN信用卡核心系统 的父项目，负责统一管理依赖版本、提供公共组件、建立项目标准化配置等基础设施。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-父项目 (AnyTXN Parent Project)](#anytxn信用卡核心系统-父项目-anytxn-parent-project)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build \& Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring \& Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的父项目是整个微服务体系的技术基础设施，在AnyTXN生态系统中负责统一依赖版本管理、提供公共通用组件、建立项目标准化配置等核心职责。该项目解决了多模块项目依赖版本冲突、公共功能重复开发、技术标准不统一、基础组件缺失等关键技术问题。

主要功能包括：统一管理Spring Boot 3.4.2、Spring Cloud 2024.0.0等核心框架版本、提供anytxn-common-core等7个通用组件模块、建立Maven多模块项目标准化配置、集成Docker容器化和Kubernetes部署支持、提供缓存、分库分表、规则引擎、序列生成等基础能力、支持多租户SaaS架构和金融级安全保障，为整个AnyTXN信用卡核心系统奠定坚实的技术基础。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于通用组件模块分析，anytxn-parent工程提供以下六大核心功能模块：

#### 1.1.1 依赖版本统一管理功能 (Unified Dependency Version Management)

**核心配置管理：**
- `pom.xml` - 父POM配置文件，统一管理所有依赖版本和构建配置
- `properties` - 版本属性配置，定义Spring Boot 3.4.2、Spring Cloud 2024.0.0等核心框架版本
- `dependencyManagement` - 依赖管理配置，统一管理Maven依赖版本避免冲突

**主要管理组件：**
- `Spring Framework` - 统一管理Spring Boot 3.4.2、Spring Cloud 2024.0.0版本
- `Database Components` - 统一管理MySQL 8.3.0、MyBatis 3.5.16、Druid 1.2.24版本
- `Middleware Components` - 统一管理Nacos、RocketMQ 2.3.1、ShardingSphere 5.5.0版本
- `Cache Components` - 统一管理Redis、Redisson 3.37.0、JetCache 2.5.16版本
- `Security Components` - 统一管理Jasypt 3.0.5、Lock4j 2.2.7等安全组件版本

**版本管理策略：**
- `anytxn.version` - 内部模块版本统一管理（1.0.2-SNAPSHOT）
- `flatten-maven-plugin` - Maven版本扁平化插件，简化版本管理
- `maven-compiler-plugin` - 编译插件配置，统一Java 17编译标准

#### 1.1.2 核心基础组件功能 (Core Infrastructure Components)

**核心API接口：**
- `GlobalExceptionHandler` - 全局异常处理器，提供统一的异常处理机制
- `BaseInfoInterceptor` - 基础信息拦截器，处理租户ID、客户ID等上下文信息
- `TenantInterceptor` - 租户拦截器，实现多租户数据隔离
- `DesensitizedHandler` - 数据脱敏处理器，提供敏感数据脱敏功能

**主要服务类：**
- `AnyTxnHttpResponse` - 统一HTTP响应封装类，标准化API响应格式
- `BaseContextHandler` - 基础上下文处理器，管理线程本地变量和上下文信息
- `TenantUtils` - 租户工具类，提供租户ID获取和管理功能
- `SpringUtils` - Spring工具类，提供Bean获取和Spring上下文操作
- `ShardingConstant` - 分片常量定义，定义租户ID、数据库索引等分片相关常量

**基础设施服务：**
- `anytxn-common-core` - 核心基础组件，提供实体类、工具类、异常处理等基础功能
- `BaseEntity` - 基础实体类，提供通用的实体属性和方法
- `CommonConstants` - 公共常量定义，统一管理系统级常量

#### 1.1.3 多级缓存管理功能 (Multi-Level Cache Management)

**核心API接口：**
- `RedisUtils` - Redis工具类，提供Redis缓存操作的统一接口
- `CacheConfig` - 缓存配置类，配置Caffeine本地缓存和Redis分布式缓存
- `PlusSpringCacheManager` - 自定义缓存管理器，整合Spring Cache框架

**主要服务类：**
- `CaffeineCacheDecorator` - Caffeine缓存装饰器，扩展一级缓存功能
- `Cache<Object, Object>` - Caffeine本地缓存处理器，提供高性能本地缓存
- `RBucket<T>` - Redisson桶操作，提供Redis对象存储功能
- `RBatch` - Redisson批量操作，提供Redis批量处理能力
- `Lock4j` - 分布式锁组件，提供Redis分布式锁功能

**缓存策略配置：**
- `Caffeine Local Cache` - 本地缓存配置（30秒过期，最大1000条记录）
- `Redis Distributed Cache` - 分布式缓存配置（支持TTL保持和批量操作）
- `Multi-Level Cache` - 多级缓存策略（Caffeine + Redis两级缓存架构）

#### 1.1.4 分库分表支持功能 (Database Sharding Support)

**核心API接口：**
- `TenantSplitDatabaseAlgorithm` - 租户分库算法，基于租户ID进行数据库分片
- `CommonDatabaseAlgorithm` - 通用分库算法，提供通用的数据库分片策略
- `TenantAndDBIndexAlgorithm` - 租户和数据库索引算法，支持多维度分片

**主要服务类：**
- `ShardingSphere Configuration` - 分库分表配置，基于YAML配置文件定义分片规则
- `ComplexKeysShardingValue` - 复合分片键值处理，支持多字段分片策略
- `BaseContextHandler` - 上下文处理器，管理租户ID和数据库索引信息
- `ShardingConstant` - 分片常量，定义TENANT_ID、DB_INDEX等分片相关常量

**分片策略配置：**
- `Tenant-based Sharding` - 基于租户的分库策略，实现多租户数据隔离
- `Database Index Sharding` - 基于数据库索引的分片策略，支持水平扩展
- `Encryption Support` - 数据加密支持，集成AES加密算法保护敏感数据

#### 1.1.5 安全加密组件功能 (Security & Encryption Components)

**核心API接口：**
- `AesCbcWithKeyAndIv` - AES加密工具类，提供CBC模式的AES加密解密功能
- `RsaUtil` - RSA加密工具类，提供RSA非对称加密和数字签名功能
- `DesensitizedHandler` - 数据脱敏处理器，提供敏感数据脱敏功能
- `DesensitizedUtils` - 脱敏工具类，提供银行卡号等敏感信息脱敏

**主要服务类：**
- `AES Encryption Service` - AES对称加密服务，支持16/24/32字节密钥
- `RSA Encryption Service` - RSA非对称加密服务，支持公钥加密私钥解密
- `Digital Signature Service` - 数字签名服务，提供私钥签名和公钥验证
- `Data Masking Service` - 数据脱敏服务，支持卡号、手机号等敏感信息脱敏

**安全策略配置：**
- `AES-256 Encryption` - AES-256对称加密，保护数据传输和存储安全
- `RSA-2048 Encryption` - RSA-2048非对称加密，保护密钥交换安全
- `Configurable Desensitization` - 可配置脱敏策略，支持开发和生产环境差异化配置

#### 1.1.6 分布式序列生成功能 (Distributed Sequence Generation)

**核心API接口：**
- `SequenceIdGen` - 核心序列号生成器，提供分布式环境下的唯一ID生成
- `Sequence Generation Service` - 序列生成服务，支持客户ID、账户ID等业务ID生成
- `ID Generation Strategy` - ID生成策略，支持多种ID生成算法

**主要服务类：**
- `Distributed ID Generator` - 分布式ID生成器，保证集群环境下ID唯一性
- `Business ID Service` - 业务ID服务，为不同业务场景生成专用ID
- `Sequence Configuration` - 序列配置服务，管理不同序列的生成规则
- `ID Pool Management` - ID池管理，提供ID预分配和缓存机制

**序列生成策略：**
- `Snowflake Algorithm` - 雪花算法，生成64位长整型唯一ID
- `Database Sequence` - 数据库序列，基于数据库自增序列生成ID
- `Redis Increment` - Redis自增，基于Redis原子操作生成连续ID

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：MySQL 8.3.0, Redis（通过Redisson 3.37.0和JetCache 2.5.16集成）

**中间件**：Nacos（服务发现与配置中心）, RocketMQ 2.3.1（消息队列）, ShardingSphere 5.5.0（分库分表）

**构建工具**：Maven 3.5+

**JDK版本要求**：Java 17

**其他核心组件**：
- MyBatis 3.5.16（数据持久化）
- MyBatis Spring Boot 3.0.4
- Undertow（Web服务器，替代Tomcat）
- Druid 1.2.24（数据库连接池）
- PageHelper 2.1.0（分页组件）
- Hutool 5.8.31（工具类库）
- Lombok 1.18.36（代码简化）
- MapStruct 1.6.3（对象映射）
- Jasypt 3.0.5（配置加解密）
- Knife4j 4.5.0（API文档生成）
- Aviator 4.2.9（规则引擎）
- Plumelog 3.1.0-SNAPSHOT（分布式日志）
- Lock4j 2.2.7（分布式锁）
- PowerMock 2.0.7 + Mockito 5.10.0（测试框架）

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**Docker**：Docker & Docker Compose（可选，用于容器化部署）

**中间件要求**：
- Nacos Server（用于服务发现和配置中心）
- MySQL 8.3.0+（数据库）
- Redis 6.2+（缓存）

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-parent.git
cd anytxn-parent
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：说明**

注意：anytxn-parent作为父项目本身不是可运行的应用，它主要用于：
- 作为其他AnyTXN微服务项目的父依赖
- 提供公共通用组件（anytxn-common模块）
- 统一管理技术栈版本

具体的微服务应用请参考各业务模块项目（如anytxn-card、anytxn-account等）。

## 4. 项目结构 (Project Structure)

**多模块结构说明**：
项目采用Maven多模块架构，作为父项目统一管理依赖版本，并包含anytxn-common通用组件聚合模块。

**各模块职责划分**：
父项目负责依赖版本管理和标准化配置，anytxn-common提供通用基础组件，各子模块按功能域划分职责。

**包结构说明**：
通用组件遵循com.anytech.anytxn.common.{module}包命名规范，为所有业务模块提供基础能力。

**关键目录介绍**：

```
.
├── anytxn-common/                       # 通用组件聚合模块
│   ├── anytxn-common-core/              # 核心基础组件
│   │   └── com.anytech.anytxn.common.core
│   │       ├── annotation/              # 注解定义
│   │       ├── base/                    # 基础类
│   │       ├── cache/                   # 缓存组件
│   │       ├── config/                  # 配置类
│   │       ├── constants/               # 常量定义
│   │       ├── enums/                   # 枚举类
│   │       ├── exception/               # 异常处理
│   │       ├── filter/                  # 过滤器
│   │       ├── handler/                 # 处理器
│   │       └── utils/                   # 工具类
│   ├── anytxn-common-loadbalancer/      # 负载均衡组件
│   ├── anytxn-common-redis/             # Redis缓存组件
│   ├── anytxn-common-sharding/          # 分库分表组件
│   ├── anytxn-common-security/          # 安全加密组件
│   ├── anytxn-common-sequence/          # 序列号生成组件
│   └── anytxn-common-rule/              # 规则引擎组件
├── code-update-record/                  # 代码迁移记录
├── doc/                                 # 项目文档
└── pom.xml                              # 父 POM
```

- `anytxn-common-core`: 提供基础实体类、工具类、全局异常处理、配置管理等核心能力。
- `anytxn-common-sharding`: 基于ShardingSphere的分库分表支持，支持多租户数据隔离。
- `anytxn-common-sequence`: 分布式ID生成服务，支持客户ID、账户ID等业务ID生成。
- `anytxn-common-rule`: 基于Aviator的规则引擎，支持复杂业务规则配置和动态加载。
- `anytxn-common-redis`: Redis缓存组件，提供多级缓存和分布式锁能力。
- `anytxn-common-loadbalancer`: 负载均衡组件，提供自定义负载均衡策略。
- `anytxn-common-security`: 安全加密组件，提供AES、RSA等加密解密能力。

## 5. 配置 (Configuration)

本项目作为父项目主要通过Maven的dependencyManagement统一管理依赖版本。各业务模块项目使用Nacos作为配置中心。

父项目的核心配置在pom.xml中进行版本管理。

**关键配置项说明:**
| 变量名/属性名 | 示例值 | 描述 |
|---------------|--------|------|
| `java.version` | `17` | Java版本要求 |
| `spring.boot.version` | `3.4.2` | Spring Boot版本 |
| `spring.cloud.version` | `2024.0.0` | Spring Cloud版本 |
| `spring.cloud.alibaba.version` | `2023.0.3.2` | Spring Cloud Alibaba版本 |
| `mysql.version` | `8.3.0` | MySQL驱动版本 |
| `shardingsphere.version` | `5.5.0` | ShardingSphere版本 |
| `anytxn.version` | `1.0.2-SNAPSHOT` | 内部模块版本 |

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建（构建所有通用组件）
mvn clean install

# 跳过测试构建
mvn clean install -DskipTests

# 单模块构建
mvn clean install -pl anytxn-common-core -am
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包通用组件
4. 推送JAR包至Nexus私有仓库
5. 业务模块项目通过Maven依赖引用

**Docker部署支持**：
```bash
# 集成fabric8 Docker插件
# 镜像仓库: k8s.jrx.com:443
# 命名空间: multi-tenant-sgb
```

**Maven仓库配置**：
- **Release仓库**: http://*********:8081/nexus/content/repositories/anytxn-product-releases/
- **Snapshot仓库**: http://*********:8083/nexus/content/repositories/anytxn-product-snapshots/

## 7. 监控与告警 (Monitoring & Alerting)

- **构建监控**: Maven构建过程集成build-info生成，支持/actuator/info接口查看构建信息。
- **版本管理**: 通过Maven版本管理机制，确保依赖版本一致性。
- **组件监控**: 各通用组件提供完整的日志记录和异常处理机制。
- **依赖分析**: 支持Maven依赖树分析，避免版本冲突。