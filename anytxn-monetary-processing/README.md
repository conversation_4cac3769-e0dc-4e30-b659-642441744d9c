# AnyTXN信用卡核心系统-最小化账务处理服务 (AnyTXN Monetary Processing Service)

本项目是 AnyTXN信用卡核心系统 的最小化账务处理服务，负责处理客户账务计算、利息处理、费用管理、逾期处理等核心业务。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-最小化账务处理服务 (AnyTXN Monetary Processing Service)](#anytxn信用卡核心系统-最小化账务处理服务-anytxn-monetary-processing-service)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build \& Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring \& Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的最小化账务处理服务是企业级微服务应用，在整个AnyTXN生态系统中负责客户账务的核心计算和处理逻辑，为卡片服务、交易服务、账户服务等其他业务模块提供账务处理基础能力。该服务解决了客户账务批量处理、利息费用计算、逾期管理、财务状态联动、账单生成等关键业务问题。

主要功能包括：单客户级账务批量处理（支持REST接口调用）、利息应计和结算处理、滞纳金和业务费用计算、逾期展期处理管理、自动分期服务处理、账户余额管理和转入转出、账单服务和财务状态联动、大批量数据的并行批处理（支持分区策略和重试机制），支持分布式事务处理和高并发访问需求。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于API接口和批量作业分析，anytxn-monetary-processing工程提供以下六大核心功能模块：

#### 1.1.1 单客户级账务批量处理功能 (Single Customer Account Batch Processing)

**核心API接口：**
- `GET /cust/account` - 单客户级批量处理接口，支持REST接口调用进行客户账务处理
- `GET /druid/sql/monitor` - Druid SQL监控接口，监控账务处理过程中的SQL执行情况

**主要服务类：**
- `MinCustAccountServiceImpl` - 最小化客户账务服务实现，处理单客户级账务批量处理核心逻辑
- `ICustReconciliationControlService` - 客户对账控制服务，管理客户对账表和批次状态
- `CustAccountController` - 客户账务控制器，提供客户级批量处理REST接口
- `CusAccountCommonServiceImpl` - 客户账务公共服务实现，提供账务处理公共功能
- `CustAccountBO` - 客户账务业务对象，封装客户账务处理的完整数据结构

**批量作业：**
- `minCustAccountJob` - 最小化客户账务批处理作业，批量处理客户账务数据
- `MinCustAccountProcessor` - 最小化账务处理器，处理单个客户的账务逻辑
- `MinCustAccountRetryProcessor` - 最小化账务重试处理器，处理失败客户的重试逻辑
- `MinCustAccountReader` - 最小化账务读取器，读取待处理的客户对账控制数据
- `MinCustAccountWriter` - 最小化账务写入器，写入处理结果和更新状态

#### 1.1.2 利息计算与结算处理功能 (Interest Calculation & Settlement Processing)

**核心API接口：**
- `POST /interest/accrual` - 利息累计处理接口，执行利息累计计算
- `POST /interest/settlement` - 利息结算处理接口，执行利息结算操作
- `GET /interest/calculation/status` - 利息计算状态查询接口

**主要服务类：**
- `InterestAccrualServiceImpl` - 利息累计服务实现，处理利息累计计算的核心逻辑
- `InterestSettledServiceImpl` - 利息结算服务实现，处理免息结息业务逻辑
- `IInterestAccureService` - 利息累积服务接口，定义利息累积处理规范
- `IInterestAccureCalcService` - 利息累积计算服务，提供利息计算算法
- `IInterestSettlementService` - 利息结算服务接口，定义利息结算处理规范

**批量作业：**
- `interestAccrualJob` - 利息累计批处理作业，批量执行客户利息累计
- `interestSettlementJob` - 利息结算批处理作业，批量执行利息结算处理
- `interestCalculationJob` - 利息计算作业，批量计算客户利息
- `clearInterestJob` - 利息清理作业，清理和调整利息数据
- `InterestProcessTasklet` - 利息处理任务单元，处理利息相关的批量任务

#### 1.1.3 滞纳金与费用管理功能 (Late Fee & Business Fee Management)

**核心API接口：**
- `POST /fee/latefee/calculate` - 滞纳金计算接口，计算客户滞纳金
- `POST /fee/business/process` - 业务费用处理接口，处理各类业务费用
- `GET /fee/waiver/status` - 费用豁免状态查询接口

**主要服务类：**
- `LateFeeServiceImpl` - 滞纳金服务实现，处理滞纳金计算和收取逻辑
- `BusinessFeeServiceImpl` - 业务费用服务实现，处理各类业务费用计算
- `ILateFeeTableService` - 滞纳金表服务，管理滞纳金参数配置
- `IBlockCodeAccountService` - 阻止码账户服务，管理费用豁免和阻止逻辑
- `WaiveFeeTakeTypeEnum` - 费用豁免类型枚举，定义费用豁免的各种类型

**批量作业：**
- `lateFeeProcessJob` - 滞纳金处理批作业，批量处理客户滞纳金
- `businessFeeCalculationJob` - 业务费用计算作业，批量计算业务费用
- `feeWaiverProcessJob` - 费用豁免处理作业，批量处理费用豁免
- `feeReconciliationJob` - 费用对账作业，执行费用数据对账
- `feeAdjustmentJob` - 费用调整作业，批量调整费用数据

#### 1.1.4 账单生成与处理功能 (Statement Generation & Processing)

**核心API接口：**
- `POST /statement/generate` - 账单生成接口，生成客户账单
- `GET /statement/query/{statementId}` - 账单查询接口，查询指定账单信息
- `POST /statement/process` - 账单处理接口，执行账单相关处理

**主要服务类：**
- `StatementServiceImpl` - 账单服务实现，处理账单生成和处理的核心逻辑
- `IStatementProcessService` - 账单处理服务接口，定义账单处理规范
- `IMinimumPaymentPercentService` - 最低还款比例服务，计算最低还款金额
- `IAutoPaymentTableService` - 自动还款表服务，管理自动还款配置
- `AccountStatementInfoDTO` - 账单信息数据传输对象，封装账单详细信息

**批量作业：**
- `statementGenerationJob` - 账单生成批作业，批量生成客户账单
- `statementProcessJob` - 账单处理批作业，批量处理账单业务
- `statementReconciliationJob` - 账单对账作业，执行账单数据对账
- `statementArchiveJob` - 账单归档作业，归档历史账单数据
- `bonusPointsCalculationJob` - 积分计算作业，计算账单期间的积分变化

#### 1.1.5 财务状态联动管理功能 (Finance Status Linkage Management)

**核心API接口：**
- `POST /finance/status/update` - 财务状态更新接口，更新客户财务状态
- `GET /finance/status/query` - 财务状态查询接口，查询客户财务状态
- `POST /finance/chargeoff/process` - 核销处理接口，处理账务核销

**主要服务类：**
- `FinanceStatusLinkageServiceImpl` - 财务状态联动服务实现，处理财务状态联动逻辑
- `IDelinquentControlDefineService` - 延滞控制定义服务，管理延滞控制规则
- `IGlAmsService` - 总账AMS服务，处理总账相关的财务状态联动
- `FinanceStatusEnum` - 财务状态枚举，定义各种财务状态类型
- `CustomerBasicInfoDTO` - 客户基础信息数据传输对象

**批量作业：**
- `financeStatusLinkageJob` - 财务状态联动批作业，批量处理财务状态联动
- `chargeOffProcessJob` - 核销处理批作业，批量处理账务核销
- `delinquentControlJob` - 延滞控制作业，批量执行延滞控制逻辑
- `financeStatusReconciliationJob` - 财务状态对账作业，对账财务状态数据
- `glCarryOverJob` - 总账结转作业，执行总账数据结转

#### 1.1.6 付款历史与数据处理功能 (Payment History & Data Processing)

**核心API接口：**
- `GET /payment/history/{accountId}` - 付款历史查询接口，查询账户付款历史
- `POST /payment/history/update` - 付款历史更新接口，更新付款历史记录
- `GET /data/monitor/status` - 数据监控状态接口，监控数据处理状态

**主要服务类：**
- `PaymentHistoryService` - 付款历史服务，管理客户付款历史记录
- `DataProcessingService` - 数据处理服务，处理各类数据处理任务
- `DruidSqlMonitorController` - Druid SQL监控控制器，监控SQL执行情况
- `PaymentHistoryDTO` - 付款历史数据传输对象，封装付款历史信息
- `CustAccountParamBO` - 客户账务参数业务对象，封装账务处理参数

**批量作业：**
- `paymentHistoryJob` - 付款历史批处理作业，批量处理付款历史数据
- `PaymentHistoryJobProcess` - 付款历史作业处理器，处理付款历史业务逻辑
- `PaymentHistoryPartitioner` - 付款历史分区器，实现付款历史数据分区处理
- `dataCleanupJob` - 数据清理作业，清理过期和无效数据
- `dataIntegrityCheckJob` - 数据完整性检查作业，检查数据完整性

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：MySQL 8.3.0, Redis（缓存支持）

**中间件**：Nacos（服务发现与配置中心）, RocketMQ（消息队列）

**构建工具**：Maven 3.5

**JDK版本要求**：Java 17

**其他核心组件**：
- MyBatis 3.5.16（数据持久化）
- MyBatis Spring Boot 3.0.4
- Undertow（Web服务器）
- PageHelper 2.1.0（分页组件）
- Spring Batch + AnyScheduler（批处理）
- Jasypt 3.0.5（配置加解密）
- Plumelog 3.1.0-SNAPSHOT（分布式日志）
- PowerMock + Mockito（测试框架）
- Docker（容器化部署）

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**Docker**：Docker & Docker Compose

**中间件要求**：
- Nacos Server
- MySQL 8.3.0+
- Redis 6.2+

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-monetary-processing.git
cd anytxn-monetary-processing
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：配置 IntelliJ IDEA 启动参数**

在 IntelliJ IDEA 中配置应用启动的程序参数：

1. **打开运行配置**
   - 在 IDEA 中找到主启动类（如 `MonetaryProcessingServerApplication.java`）
   - 右键点击该文件，选择 `Run` 或 `Debug`
   - 或者点击 IDEA 顶部的运行配置下拉菜单

2. **编辑运行配置**
   - 点击运行配置旁的编辑按钮（齿轮图标）
   - 或者选择 `Run` → `Edit Configurations...`

3. **配置程序参数**
   - 在 `Program arguments` 字段中输入以下参数：
   ```
   --NACOS_USERNAME=nacos --NACOS_PASSWORD=nacos --CLUSTER_NAME=A --NACOS_SERVER_ADDR=**********:8848,**********:8848,**********:8848 --NACOS_NAMESPACE=2cff4a1a-e0f9-42e4-8472-a7b0a17a1235
   ```
   
   > **注意**：请根据实际环境情况调整以下参数值：
   > - `NACOS_USERNAME`: Nacos 用户名
   > - `NACOS_PASSWORD`: Nacos 密码  
   > - `CLUSTER_NAME`: 集群名称
   > - `NACOS_SERVER_ADDR`: Nacos 服务器地址列表
   > - `NACOS_NAMESPACE`: Nacos 命名空间ID

4. **保存配置**
   - 点击 `Apply` 和 `OK` 保存配置

**第五步：运行应用**

我们推荐在 IntelliJ IDEA 中直接运行 `MonetaryProcessingServerApplication.java`。

或者通过命令行启动，指定 `dev` 环境配置：

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

服务启动后，你可以访问健康检查端点来验证服务状态。

## 4. 项目结构 (Project Structure)

**多模块结构说明**：
项目采用Maven多模块架构，按照业务功能和技术层次进行模块划分，实现清晰的依赖关系和职责分离。

**各模块职责划分**：
基础定义模块提供API接口和枚举定义，业务实现模块包含核心逻辑，服务启动模块负责应用启动，批量处理模块处理大批量数据。

**包结构说明**：
各模块遵循标准包命名规范，基础包名为com.anytech.anytxn.custacct，业务实现包名保持一致。

**关键目录介绍**：

```
.
├── anytxn-monetary-processing-base      # 基础定义模块 (可被其他服务依赖)
│   └── com.anytech.anytxn.custacct
│       ├── enums/                       # 枚举定义（利息指标、豁免标志等）
│       └── service/                     # 基础服务接口
├── anytxn-monetary-processing-sdk       # 业务实现模块 (核心业务逻辑)
│   └── com.anytech.anytxn.custacct
│       ├── config/                      # 配置类
│       ├── controller/                  # REST控制器
│       └── service/                     # 业务服务实现（13个核心服务）
├── anytxn-monetary-processing-server    # 服务启动模块
│   └── com.anytech.anytxn.custacct.server
│       ├── MonetaryProcessingServerApplication.java  # 主启动类
│       └── bootstrap.yml                # 配置文件
├── anytxn-monetary-processing-batch     # 批量处理模块 (定时任务)
│   └── com.anytech.anytxn.custacct
│       ├── job/                         # 批处理作业
│       │   ├── mincustaccount/          # 最小化账务作业
│       │   └── paymenthistory/          # 付款历史作业
│       └── util/                        # 批处理工具类
├── doc/                                 # 项目文档
└── pom.xml                              # 父 POM
```

- `anytxn-monetary-processing-base`: 定义了对外暴露的服务接口和枚举类型，可以被其他微服务依赖，避免了代码重复。
- `anytxn-monetary-processing-sdk`: 包含所有业务逻辑、Controller、Service等，是核心业务实现模块。
- `anytxn-monetary-processing-server`: 微服务启动入口，包含Spring Boot应用配置。
- `anytxn-monetary-processing-batch`: 批量处理模块，包含大批量数据处理作业和分区策略。

## 5. 配置 (Configuration)

本项目使用 Nacos 作为配置中心。核心配置文件在 Nacos 中，`group` 为 `DEFAULT_GROUP`，`Data ID` 为 `custAccount.yaml`。

本地开发时，会优先加载 `src/main/resources/bootstrap.yml` 中的配置。

**关键环境变量/配置项说明:**
| 变量名/属性名 | 示例值 | 描述 |
|---------------|--------|------|
| `SERVER_ADDR` | `************:8848` | Nacos 服务器地址 |
| `NAMESPACE` | `dev-namespace` | Nacos 命名空间 |
| `DATA_ID` | `custAccount-dev.yaml` | 业务数据库配置ID |
| `SPRING_CLOUD_NACOS_CONFIG_PREFIX` | `custAccount` | Nacos配置前缀 |
| `SPRING_CLOUD_NACOS_CONFIG_FILE_EXTENSION` | `yaml` | 配置文件扩展名 |

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建
mvn clean install

# 跳过测试构建
mvn clean install -DskipTests

# 单模块构建
mvn clean install -pl anytxn-monetary-processing-server -am
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包
4. Docker镜像构建
5. 推送至Harbor镜像仓库
6. Kubernetes集群部署

**Docker部署方式**：
```bash
# 构建Docker镜像
mvn clean package docker:build

# 运行Docker容器
docker run -p 8080:8080 -e SPRING_PROFILES_ACTIVE=dev anytxn-monetary-processing-server:1.0.2-SNAPSHOT
```

**环境变量配置**：
- `SPRING_PROFILES_ACTIVE`: 指定运行环境（dev/sit/prod）
- `SERVER_ADDR`: Nacos服务地址
- `NAMESPACE`: Nacos命名空间
- `DATA_ID`: 业务数据库配置ID

## 7. 监控与告警 (Monitoring & Alerting)

- **健康检查**: Spring Boot Actuator 提供了健康检查端点。
  - `http://localhost:8080/actuator/health`
- **Metrics**: 指标已接入 Prometheus。端点地址：
  - `http://localhost:8080/actuator/prometheus`
- **日志**: 应用日志格式为 JSON，统一由 Plumelog 收集并发送到 Elasticsearch。你可以在 Kibana 中查看。
  - **Kibana Dashboard**: [链接到 Kibana 仪表盘](https://kibana.jrx.com)