# AnyTXN信用卡核心系统-会计应用模块 (AnyTXN Accounting Service)

本项目是 AnyTXN信用卡核心系统 的会计应用模块，负责处理会计核算、账务记录、分录处理、批量作业等核心业务。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-会计应用模块 (AnyTXN Accounting Service)](#anytxn信用卡核心系统-会计应用模块-anytxn-accounting-service)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build \& Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring \& Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的会计应用模块是企业级微服务应用，在整个AnyTXN生态系统中负责会计核算和账务管理，为卡片服务、账户服务、交易服务等其他业务模块提供会计记账基础能力。该服务解决了会计分录处理、总账管理、账务汇总、财务报表生成、批量账务处理等关键业务问题。

主要功能包括：会计凭证生成与管理、总账科目余额维护、账务记录汇总和查询、会计科目层次管理、各类财务报表生成、大批量数据处理作业、定时任务调度管理，支持分布式事务处理和多租户数据隔离。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于API接口和批量作业分析，anytxn-accounting工程提供以下五大核心功能模块：

#### 1.1.1 会计凭证与流水管理功能 (Accounting Voucher & Transaction Management)

**核心API接口：**
- `GET /accountant/tAmsGlamsById` - 根据ID查询会计流水表数据接口
- `GET /accountant/getTAmsGlamsByPage` - 分页查询会计流水表数据接口
- `GET /accountant/getTamsGlvcherPage` - 分页查询会计传票数据接口
- `GET /accountant/findTAmsGlvcherById` - 根据ID查询会计传票数据接口

**主要服务类：**
- `IGlVoucherService` - 会计传票服务，负责根据流水生成会计传票和月末结转处理
- `IVoucherManageService` - 传票管理服务，提供传票的查询和管理功能
- `IAmsGlamsService` - 会计流水服务，处理会计流水数据的存储和查询

**批量作业：**
- `generateglvoucher` - 会计传票生成批量作业，根据交易流水自动生成会计传票
- `glvchersum` - 传票汇总批量作业，对传票数据进行汇总和统计处理

**核心业务能力：**
- 支持根据交易流水自动生成符合会计准则的会计传票
- 提供传票和流水的多维度查询和分页展示
- 支持月末结转和增值税等特殊业务的传票生成

#### 1.1.2 会计汇总与统计功能 (Accounting Summary & Statistics)

**核心API接口：**
- `GET /accountant/amssumById` - 根据ID查询会计汇总数据接口
- `GET /accountant/amssum` - 分页查询交易会计汇总流水表接口

**主要服务类：**
- `IAccountSummaryService` - 会计汇总服务，处理交易会计汇总流水表的查询和管理
- `IGlvcherSumService` - 传票汇总服务，提供传票数据的汇总统计功能
- `IAccountsCheckService` - 账务检查服务，负责账务数据的核对和验证

**批量作业：**
- `summary` - 数据汇总批量作业群，包含多个子汇总作业：
  - `amsglactbal` - 账户余额汇总
  - `amsglinsbal` - 分期余额汇总
  - `amsglvatbal` - 增值税余额汇总
  - `amsglvcher` - 传票汇总
  - `amsglvchersum` - 传票总计汇总

**核心业务能力：**
- 支持多维度的会计数据汇总和统计分析
- 提供余额类、损益类科目的汇总计算
- 支持按机构、时间等维度的数据汇总

#### 1.1.3 总账与科目管理功能 (General Ledger & Account Management)

**主要服务类：**
- `ILedgerService` - 总账服务，处理总账相关的业务逻辑
- `IGlAmsAcgdService` - 总账日记账服务，管理日记账数据
- `IGlAmsAcgmService` - 总账月度数据服务，处理月度汇总数据
- `IGlAmsAcgyService` - 总账年度数据服务，处理年度汇总数据
- `IYearEndService` - 年结服务，负责年末结账和数据归档

**核心业务能力：**
- 提供完整的总账科目体系管理
- 支持日记账、月报、年报等多层次数据管理
- 支持年末结账和数据归档处理

#### 1.1.4 账务核对与检查功能 (Account Reconciliation & Verification)

**主要服务类：**
- `IAccountsCheckService` - 账务检查服务，提供账务数据的核对验证
- `IAccountsOccurCheckService` - 账务发生额检查服务，核对账务发生额数据
- `IBalService` - 余额服务，管理各类账户余额数据

**批量作业：**
- `accountcheckoccur` - 账务发生额检查批量作业，定期核对账务发生额
- `acbalance` - 账户余额快照批量作业，生成账户余额快照数据

**核心业务能力：**
- 支持账务数据的自动核对和差异检查
- 提供余额核对和发生额核对功能
- 支持账务异常的自动识别和报告

#### 1.1.5 财务报表与对外接口功能 (Financial Reports & External Integration)

**批量作业：**
- `report` - 财务报表生成批量作业群，包含多种报表：
  - `amountdetail` - 应收账款明细报表
  - `carddetail` - 卡片明细报表
  - `cardsettlement` - 卡片结算报表
  - `cardtrans` - 卡片交易报表
  - `custdetail` - 客户明细报表
  - `installdetail` - 分期明细报表
  - `transactionsum` - 交易汇总报表

**对外接口作业：**
- `journal` - 日记账对外接口，支持Oracle Suite API集成
- `glamsfromupi` - UPI系统会计数据接入作业
- `glamsfromacquirer` - 收单系统会计数据接入作业

**核心业务能力：**
- 支持多种格式财务报表的自动生成
- 提供与外部财务系统的数据接口
- 支持监管报送和财务披露要求

#### 1.1.6 数据处理与文件管理功能 (Data Processing & File Management)

**批量作业：**
- `file` - 文件处理批量作业群：
  - `IrrgulardataFiles` - 异常数据文件处理
  - `OriRecordDetailConfig` - 原始记录明细文件处理
  - `TxnRecordSumConfig` - 交易记录汇总文件处理
  - `subjectSumFile` - 科目汇总文件处理

**技术特色：**
- **大数据处理**：采用Spring Batch框架，支持大批量数据的高效处理
- **分区处理**：支持数据分区并行处理，提高处理效率
- **容错机制**：完整的异常处理和重试机制，保证批量作业稳定运行
- **多源数据集成**：支持从UPI、收单系统等多个数据源获取会计数据
- **监管合规**：严格按照会计准则和监管要求进行数据处理和报表生成

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：MySQL 8.3.0, Redis（通过Redisson 3.41.0集成）

**中间件**：Nacos（服务发现与配置中心）, RocketMQ

**构建工具**：Maven 3.5

**JDK版本要求**：Java 17

**其他核心组件**：
- MyBatis 3.5.16（数据持久化）
- MyBatis Spring Boot 3.0.4
- Undertow（Web服务器）
- ShardingSphere 5.5.0（分库分表）
- Druid 1.2.24（数据库连接池）
- Jasypt 3.0.5（参数加解密）
- Plumelog 3.1.0-SNAPSHOT（分布式日志）
- Micrometer + Prometheus（监控）

_图 1: 会计服务交互图_

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**Docker**：Docker & Docker Compose

**中间件要求**：
- Nacos Server
- RocketMQ Server
- MySQL 8.3.0+
- Redis 6.2+

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-accounting.git
cd anytxn-accounting
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：配置 IntelliJ IDEA 启动参数**

在 IntelliJ IDEA 中配置应用启动的程序参数：

1. **打开运行配置**
   - 在 IDEA 中找到主启动类（如 `AccountantServerApplication.java`）
   - 右键点击该文件，选择 `Run` 或 `Debug`
   - 或者点击 IDEA 顶部的运行配置下拉菜单

2. **编辑运行配置**
   - 点击运行配置旁的编辑按钮（齿轮图标）
   - 或者选择 `Run` → `Edit Configurations...`

3. **配置程序参数**
   - 在 `Program arguments` 字段中输入以下参数：
   ```
   --NACOS_USERNAME=nacos --NACOS_PASSWORD=nacos --CLUSTER_NAME=A --NACOS_SERVER_ADDR=**********:8848,**********:8848,**********:8848 --NACOS_NAMESPACE=2cff4a1a-e0f9-42e4-8472-a7b0a17a1235
   ```
   
   > **注意**：请根据实际环境情况调整以下参数值：
   > - `NACOS_USERNAME`: Nacos 用户名
   > - `NACOS_PASSWORD`: Nacos 密码  
   > - `CLUSTER_NAME`: 集群名称
   > - `NACOS_SERVER_ADDR`: Nacos 服务器地址列表
   > - `NACOS_NAMESPACE`: Nacos 命名空间ID

4. **保存配置**
   - 点击 `Apply` 和 `OK` 保存配置

**第五步：运行应用**

我们推荐在 IntelliJ IDEA 中直接运行 `AccountantServerApplication.java`。

或者通过命令行启动，指定 `dev` 环境配置：

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

服务启动后，你可以访问 [http://localhost:18083](http://localhost:18083) 来检查服务状态。

## 4. 项目结构 (Project Structure)

**多模块结构说明**：
项目采用Maven多模块架构，按照业务功能和技术层次进行模块划分，实现清晰的依赖关系和职责分离。

**各模块职责划分**：
基础定义模块提供API接口和基础组件，业务实现模块包含核心逻辑，服务启动模块负责应用启动，批量处理模块处理定时任务。

**包结构说明**：
各模块遵循标准包命名规范，非sdk模块包名为com.anytech.anytxn.accounting.模块名，sdk模块包名为com.anytech.anytxn.accounting。

**关键目录介绍**：

```
.
├── anytxn-accounting-base        # 基础定义模块 (可被其他服务依赖)
│   └── com.anytech.anytxn.accounting.base
│       ├── constants/            # 常量定义
│       ├── domain/               # 领域对象 (BO、DTO、Model)
│       ├── enums/                # 枚举类定义 (19个枚举)
│       ├── exception/            # 异常类定义
│       ├── service/              # 服务接口定义 (13个接口)
│       └── utils/                # 工具类
├── anytxn-accounting-sdk         # 业务实现模块 (16个服务实现类)
│   └── com.anytech.anytxn.accounting
│       ├── controller/           # REST控制器 (4个控制器)
│       ├── mapper/               # 数据访问层
│       └── service/              # 服务实现类
├── anytxn-accounting-server      # 服务启动模块
│   └── com.anytech.anytxn.accounting.server
│       └── AccountantServerApplication.java  # 主启动类
├── anytxn-accounting-batch       # 批量处理模块 (9大类批量作业)
│   └── com.anytech.anytxn.accounting.batch
│       ├── AnytxnAccountingBatchApplication.java  # 批处理启动类
│       ├── config/               # 批处理配置
│       └── job/                  # 批处理作业
├── doc                           # 项目文档
└── pom.xml                       # 父 POM
```

- `anytxn-accounting-base`: 定义了对外暴露的API接口、DTO和业务常量，可以被其他微服务依赖，避免了代码重复。
- `anytxn-accounting-sdk`: 包含所有业务逻辑、Controller、Service等，是核心业务实现模块。
- `anytxn-accounting-server`: 微服务启动入口，包含Spring Boot应用配置。
- `anytxn-accounting-batch`: 批量处理模块，包含批处理任务实现。

## 5. 配置 (Configuration)

本项目使用 Nacos 作为配置中心。核心配置文件在 Nacos 中，`group` 为 `ACCOUNTING_GROUP`，`Data ID` 为 `anytxn-accounting-server-dev.yaml`。

本地开发时，会优先加载 `src/main/resources/application.yaml` 中的配置。

**关键环境变量/配置项说明:**
| 变量名/属性名 | 示例值 | 描述 |
|---------------|--------|------|
| `NACOS_SERVER_ADDR` | `************:8848` | Nacos 服务器地址 |
| `SPRING_DATASOURCE_URL` | `*********************************************` | 数据库连接地址 |
| `ROCKETMQ_NAME_SERVER` | `***********:9876` | RocketMQ 服务器地址 |
| `REDIS_HOST` | `localhost` | Redis 主机名 |
| `SERVER_PORT` | `18083` | 服务端口号 |

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建
mvn clean install

# 跳过测试构建
mvn clean install -DskipTests

# 单模块构建
mvn clean install -pl anytxn-accounting-server -am
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包
4. Docker镜像构建
5. 推送至Harbor镜像仓库
6. Kubernetes集群部署

**Docker部署方式**：
```bash
# 构建Docker镜像
mvn clean package docker:build

# 运行Docker容器
docker run -p 18083:18083 -e SPRING_PROFILES_ACTIVE=dev anytxn-accounting-server:1.0.2-SNAPSHOT
```

**环境变量配置**：
- `SPRING_PROFILES_ACTIVE`: 指定运行环境（dev/sit/prod）
- `NACOS_SERVER_ADDR`: Nacos服务地址
- `MYSQL_HOST`: MySQL数据库主机
- `REDIS_HOST`: Redis缓存主机
- `ROCKETMQ_NAME_SERVER`: RocketMQ服务地址

## 7. 监控与告警 (Monitoring & Alerting)

- **健康检查**: Spring Boot Actuator 提供了健康检查端点。
  - `http://localhost:18083/actuator/health`
- **Metrics**: 指标已接入 Prometheus。端点地址：
  - `http://localhost:18083/actuator/prometheus`
- **日志**: 应用日志格式为 JSON，统一由 Plumelog 收集并发送到 Elasticsearch。你可以在 Kibana 中查看。
  - **Kibana Dashboard**: [链接到 Kibana 仪表盘](https://kibana.jrx.com)