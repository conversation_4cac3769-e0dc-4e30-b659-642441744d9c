# AnyTXN信用卡核心系统-规则子系统 (AnyTXN Rule Engine Service)

本项目是 AnyTXN信用卡核心系统 的规则引擎子系统，负责处理规则匹配、规则识别、额度管理、字典管理等核心规则业务。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-规则子系统 (AnyTXN Rule Engine Service)](#anytxn信用卡核心系统-规则子系统-anytxn-rule-engine-service)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build & Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring & Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的规则引擎子系统是整个微服务体系的**业务规则中枢**，在AnyTXN生态系统中负责规则匹配、规则识别、额度管理、字典管理等核心规则业务功能。该项目解决了业务规则分散管理、规则配置复杂、决策逻辑不统一、规则执行效率低等关键技术问题。

主要功能包括：基于Aviator表达式引擎的高性能规则计算、支持优先级匹配和列表匹配的规则执行引擎、覆盖96个业务类的完整规则管理体系、包含62个类的专业额度管理子系统、提供差异化定价规则、发卡规则、授权管理规则、会计核算规则等全业务场景支持、集成JetCache分布式缓存和发布订阅机制、支持多租户和分库分表的企业级数据架构，为整个AnyTXN信用卡核心系统提供灵活、高效、可扩展的规则处理能力。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于API接口和批量作业分析，anytxn-rule工程提供以下五大核心功能模块：

#### 1.1.1 规则执行引擎功能 (Rule Execution Engine)

**核心API接口：**
- `POST /rule-api/execute` - 优先级规则匹配接口，基于规则类型执行单一最优规则匹配
- `POST /rule-api/executeList` - 优先级规则列表匹配接口，返回匹配的规则列表
- `POST /rule-api/execute/all` - 规则全匹配接口，执行所有匹配的规则并返回完整结果集

**主要服务类：**
- `TxnRuleMatcher` - 交易规则匹配器，基于Aviator表达式引擎执行规则匹配核心逻辑
- `RuleMatcherManager` - 规则匹配器管理器，负责注册、注销和获取缓存规则集
- `RuleInfoLoader` - 规则加载器，负责增量加载和按类型加载规则数据
- `RuleExecutorController` - 规则执行控制器，提供外部规则匹配调用接口
- `DataInputDTO` - 规则输入数据传输对象，封装规则因子数据和执行参数

**批量作业：**
- `ruleExecutionMonitorJob` - 规则执行监控作业，监控规则执行性能和成功率
- `rulePerformanceOptimizationJob` - 规则性能优化作业，优化规则执行效率
- `ruleExecutionLogJob` - 规则执行日志作业，记录规则执行历史和统计数据

#### 1.1.2 规则管理与配置功能 (Rule Management & Configuration)

**核心API接口：**
- `POST /rule` - 添加规则接口，创建新的业务规则配置
- `PUT /rule` - 编辑规则接口，修改现有规则的配置信息
- `PUT /rule/product` - 修改规则所属产品接口，批量调整规则归属关系
- `DELETE /rule/{id}` - 根据ID删除规则接口，删除指定的规则配置
- `GET /rule/{organizationNumber}` - 基于机构号查询规则接口，获取机构相关规则

**主要服务类：**
- `IRuleInfoOperator` - 规则信息操作接口，定义规则管理核心功能规范
- `RuleInfoServiceImpl` - 规则信息服务实现，处理规则增删改查的核心业务逻辑
- `RuleInfoController` - 规则管理控制器，提供规则管理REST API接口
- `AbstractParameterService` - 抽象参数服务，提供规则审核和数据库操作基础功能
- `RuleInfoDTO` - 规则信息数据传输对象，封装规则配置的完整信息

**批量作业：**
- `ruleDataSyncJob` - 规则数据同步作业，批量同步规则配置数据
- `ruleValidationJob` - 规则验证作业，批量验证规则配置的有效性
- `ruleArchiveJob` - 规则归档作业，归档历史规则配置数据

#### 1.1.3 规则字典管理功能 (Rule Dictionary Management)

**核心API接口：**
- `POST /rule-dict` - 添加规则字典接口，创建新的规则字典项
- `PUT /rule-dict` - 编辑规则字典接口，修改规则字典配置
- `DELETE /rule-dict/{id}` - 删除规则字典接口，删除指定的字典项
- `GET /rule-dict/list` - 规则字典列表查询接口，分页查询字典数据

**主要服务类：**
- `IRuleDictInfoOperator` - 规则字典信息操作接口，定义字典管理功能规范
- `RuleDictInfoServiceImpl` - 规则字典信息服务实现，处理字典管理核心逻辑
- `RuleDictInfoController` - 规则字典控制器，提供字典管理REST API接口
- `RuleDictInfoDTO` - 规则字典信息数据传输对象，封装字典项详细信息
- `DicInfoService` - 字典信息服务，管理交易识别等业务字典数据

**批量作业：**
- `ruleDictSyncJob` - 规则字典同步作业，批量同步字典数据
- `ruleDictValidationJob` - 规则字典验证作业，验证字典数据完整性
- `ruleDictCleanupJob` - 规则字典清理作业，清理过期和无效的字典数据

#### 1.1.4 额度规则管理功能 (Limit Rule Management)

**核心API接口：**
- `POST /m/limit/limit-type` - 新建额度类型接口，创建额度类型配置
- `POST /m/limit/open-limit-unit` - 新建额度授信单元接口，创建授信单元配置
- `POST /m/limit/limit-unit/overpayment/add` - 新增额度计算规则溢缴款接口
- `GET /m/limit/ctrl-limit-unit` - 额度管控单元查询接口，查询管控单元配置

**主要服务类：**
- `LimitTypeService` - 额度类型服务，管理额度类型的创建和配置
- `OpenLimitUnitService` - 额度授信单元服务，处理授信单元管理逻辑
- `CtrlLimitUnitService` - 额度管控单元服务，管理额度管控单元配置
- `CtrlLimitCalculateRuleService` - 管控单元额度计算规则服务，处理额度计算规则
- `CtrlLimitUnitExecutor` - 管控单元执行器，执行额度管控单元的检查规则

**批量作业：**
- `limitRuleSyncJob` - 额度规则同步作业，批量同步额度规则配置
- `limitRuleValidationJob` - 额度规则验证作业，验证额度规则有效性
- `limitRuleOptimizationJob` - 额度规则优化作业，优化额度规则执行性能

#### 1.1.5 规则缓存与发布功能 (Rule Cache & Publishing)

**核心API接口：**
- `GET /rule/cache/status` - 规则缓存状态查询接口，监控缓存运行状态
- `POST /rule/cache/refresh` - 规则缓存刷新接口，手动刷新指定规则缓存
- `DELETE /rule/cache/clear` - 规则缓存清理接口，清理指定的规则缓存

**主要服务类：**
- `RuleRefreshHandle` - 规则缓存刷新处理器，统一管理规则缓存刷新逻辑
- `RulePublishService` - 规则发布服务，处理规则变更通知和缓存刷新
- `RuleConfigListener` - 规则变更监听器，监听Nacos配置变更并同步规则
- `LimitUnitRefreshHandle` - 额度单元刷新处理器，处理额度规则缓存刷新
- `LimitRuleRefreshHandle` - 额度规则刷新处理器，管理额度规则缓存更新

**批量作业：**
- `ruleCacheWarmupJob` - 规则缓存预热作业，预加载热点规则数据到缓存
- `ruleCacheEvictionJob` - 规则缓存淘汰作业，清理过期和低频访问的缓存数据
- `ruleCacheConsistencyJob` - 规则缓存一致性作业，保证缓存与数据库数据一致性
- `rulePublishNotificationJob` - 规则发布通知作业，批量处理规则变更通知

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：Oracle (主要), MySQL 8.3.0, Redis（通过JetCache集成）

**中间件**：Nacos（服务发现与配置中心）, ShardingSphere 5.5.0（分库分表）

**构建工具**：Maven 3.5

**JDK版本要求**：Java 17

**其他核心组件**：
- MyBatis 3.5.16（数据持久化）
- MyBatis Spring Boot 3.0.4
- JetCache 2.5.16（分布式缓存）
- Aviator 4.2.9（表达式规则引擎）
- ShardingSphere 5.5.0（分库分表中间件）
- Apache Commons Collections 4（集合工具）
- Jackson（JSON处理）
- Undertow（Web服务器）
- Lombok 1.18.36（代码简化）

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**Docker**：Docker & Docker Compose（可选，用于容器化部署）

**中间件要求**：
- Nacos Server（用于服务发现和配置中心）
- Oracle 12c+（主数据库）
- MySQL 8.3.0+（辅助数据库）
- Redis 6.2+（分布式缓存）

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-rule.git
cd anytxn-rule
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：配置 IntelliJ IDEA 启动参数**

在 IntelliJ IDEA 中配置应用启动的程序参数：

1. **打开运行配置**
   - 在 IDEA 中找到主启动类（如 `RuleServiceApplication.java`）
   - 右键点击该文件，选择 `Run` 或 `Debug`
   - 或者点击 IDEA 顶部的运行配置下拉菜单

2. **编辑运行配置**
   - 点击运行配置旁的编辑按钮（齿轮图标）
   - 或者选择 `Run` → `Edit Configurations...`

3. **配置程序参数**
   - 在 `Program arguments` 字段中输入以下参数：
   ```
   --NACOS_USERNAME=nacos --NACOS_PASSWORD=nacos --CLUSTER_NAME=A --NACOS_SERVER_ADDR=**********:8848,**********:8848,**********:8848 --NACOS_NAMESPACE=2cff4a1a-e0f9-42e4-8472-a7b0a17a1235
   ```
   
   > **注意**：请根据实际环境情况调整以下参数值：
   > - `NACOS_USERNAME`: Nacos 用户名
   > - `NACOS_PASSWORD`: Nacos 密码  
   > - `CLUSTER_NAME`: 集群名称
   > - `NACOS_SERVER_ADDR`: Nacos 服务器地址列表
   > - `NACOS_NAMESPACE`: Nacos 命名空间ID

4. **保存配置**
   - 点击 `Apply` 和 `OK` 保存配置

**第五步：运行应用**

推荐在 IDE 中直接运行 `RuleServiceApplication.java`。

或者通过命令行启动，指定 `dev` 环境配置：

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

服务启动后，你可以访问 [http://localhost:8080](http://localhost:8080) 来检查服务状态。

## 4. 项目结构 (Project Structure)

**单模块结构说明**：
项目采用Maven单模块架构，所有功能集中在anytxn-rule-sdk模块中，简化了模块依赖关系和部署复杂度。

**各业务域职责划分**：
通用规则管理负责基础规则操作，额度管理子系统专门处理额度相关业务逻辑，缓存层提供高性能数据访问支持。

**包结构说明**：
业务包遵循com.anytech.anytxn.rule.{domain}命名规范，额度管理采用独立的limit子包结构。

**关键目录介绍**：

```
.
├── anytxn-rule-sdk/                 # 核心业务实现模块
│   ├── src/main/java/
│   │   └── com.anytech.anytxn.rule/
│   │       ├── cache/               # 规则缓存管理 (4个类)
│   │       ├── config/              # 配置管理 (5个类)
│   │       ├── controller/          # REST API控制器 (3个类)
│   │       ├── limit/               # 额度规则专门子系统 (62个类)
│   │       │   ├── cache/           # 额度规则缓存
│   │       │   ├── config/          # 额度规则配置
│   │       │   ├── controller/      # 额度规则API
│   │       │   ├── mapper/          # 数据访问层
│   │       │   ├── model/           # 数据模型
│   │       │   └── service/         # 业务服务层
│   │       ├── mapper/              # 通用数据访问层 (9个类)
│   │       ├── model/               # 通用数据模型 (2个类)
│   │       ├── runner/              # 启动器 (1个类)
│   │       └── service/             # 通用业务服务层 (2个类)
│   └── src/main/resources/
│       ├── aviator_functions.config # Aviator表达式函数配置
│       └── generator-config-oracle.xml # MyBatis代码生成配置
└── pom.xml                          # 项目 POM
```

- `cache/`: 提供规则信息的分布式缓存支持，基于JetCache实现多级缓存策略。
- `limit/`: 额度管理专门子系统，包含完整的额度规则管理、计算、检查功能。
- `service/`: 核心业务服务层，包含RuleInfoServiceImpl和RuleDictInfoServiceImpl等关键服务。
- `controller/`: REST API接口层，提供规则执行、管理等对外服务接口。
- `aviator_functions.config`: 配置Aviator表达式引擎支持的自定义函数，支持复杂业务规则计算。

## 5. 配置 (Configuration)

本项目使用 Nacos 作为配置中心。核心配置文件在 Nacos 中，`group` 为 `RULE_GROUP`，`Data ID` 为 `anytxn-rule-dev.yml`。

本地开发时，会优先加载 `src/main/resources/application-dev.yml` 中的配置。

**关键环境变量/配置项说明:**
| 变量名/属性名 | 示例值 | 描述 |
|---------------|--------|------|
| `NACOS_SERVER_ADDR` | `127.0.0.1:8848` | Nacos 服务器地址 |
| `ORACLE_DATASOURCE_URL` | `******************************************` | Oracle 数据库连接地址 |
| `MYSQL_DATASOURCE_URL` | `***************************************` | MySQL 数据库连接地址 |
| `REDIS_HOST` | `localhost` | Redis 主机名 |
| `AVIATOR_FUNCTIONS_CONFIG` | `aviator_functions.config` | Aviator表达式函数配置文件路径 |
| `JETCACHE_ENABLED` | `true` | 是否启用JetCache分布式缓存 |

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建
mvn clean install

# 跳过测试构建
mvn clean install -DskipTests

# 构建并生成Docker镜像
mvn clean package docker:build
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包生成JAR文件
4. Docker镜像构建和推送
5. Kubernetes集群部署服务

**Docker部署方式**：
```bash
# 构建镜像
docker build -t anytxn-rule:latest .

# 运行容器
docker run -d -p 8080:8080 \
  -e NACOS_SERVER_ADDR=nacos:8848 \
  -e SPRING_PROFILES_ACTIVE=prod \
  anytxn-rule:latest
```

**环境变量配置**：
- **镜像仓库**: k8s.jrx.com:443/multi-tenant-sgb/anytxn-rule
- **Kubernetes命名空间**: multi-tenant-sgb
- **服务端口**: 8080

## 7. 监控与告警 (Monitoring & Alerting)

- **健康检查**: Spring Boot Actuator 提供了健康检查端点。
  - `http://<host>:<port>/actuator/health`
- **Metrics**: 指标已接入 Prometheus。端点地址：
  - `http://<host>:<port>/actuator/prometheus`
- **规则执行监控**: 提供规则执行次数、成功率、响应时间等关键指标监控。
- **缓存监控**: JetCache提供缓存命中率、缓存大小、缓存刷新等监控指标。
- **日志**: 应用日志格式为 JSON，统一由 Logstash 收集并发送到 Elasticsearch。你可以在 Kibana 中查看。
  - **规则执行日志**: 记录规则匹配过程和执行结果
  - **性能日志**: 记录规则计算耗时和缓存性能数据