# AnyTXN信用卡核心系统-HSM加密服务 (AnyTXN HSM Encryption Service)

本项目是AnyTXN信用卡核心系统的HSM加密服务，负责处理PIN验证、CVV校验、ARQC验证、MAC计算等金融级加密业务。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-HSM加密服务 (AnyTXN HSM Encryption Service)](#anytxn信用卡核心系统-hsm加密服务-anytxn-hsm-encryption-service)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build & Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring & Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的HSM加密服务是整个微服务体系的**金融级安全中枢**，在AnyTXN生态系统中负责硬件安全模块（HSM）集成、PIN验证、CVV校验、ARQC验证、MAC计算等关键加密功能。该项目解决了金融级安全要求、加密算法标准化、硬件设备集成、密钥管理体系等关键技术问题。

主要功能包括：企业级HSM硬件安全模块集成能力、支持SM4/3DES双算法体系、完整的PIN处理和验证机制、CVV1/CVV2生成和校验功能、ARQC交易认证码验证、MAC消息认证码计算、密钥管理和分发体系、高性能TCP连接池管理、多卡BIN密钥映射支持、金融标准合规的加密操作、完善的异常处理和监控机制、RESTful API接口设计，为整个AnyTXN信用卡核心系统提供安全、稳定、高性能的硬件级加密能力。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于API接口和服务实现分析，anytxn-hsm工程提供以下五大核心功能模块：

#### 1.1.1 PIN处理与验证功能 (PIN Processing & Verification)

**核心API接口：**
- `POST /hsm/pin/verify` - PIN验证接口，验证持卡人个人识别码的正确性
- `POST /hsm/pin/encrypt` - PIN加密接口，使用HSM对PIN进行加密处理
- `POST /hsm/pin/block/generate` - PIN块生成接口，生成符合金融标准的PIN块
- `POST /hsm/pin/offset/calculate` - PIN偏移量计算接口，计算PIN验证偏移量

**主要服务类：**
- `EncryptionManager.encryptionPassword()` - PIN加密处理方法，实现PIN从ZPK到LMK的转换
- `EncryptionManager.pinOffsetByDe()` - PIN偏移量计算方法，使用DE命令计算PIN偏移
- `EncryptionManager.generatePinBlockZpkByJg()` - PIN块生成方法，使用JG命令生成3DES PIN块
- `EncryptionManager.generatePinBlockZpkByWx()` - PIN块生成方法，使用WX命令生成SM4 PIN块
- `EncryptionApi.encryptionByBa()` - PIN加密底层API，使用BA命令加密明文PIN

**PIN处理功能：**
- `PIN Block Format Support` - PIN块格式支持，兼容ISO 9564标准的多种PIN块格式
- `ZPK/LMK Key Conversion` - ZPK/LMK密钥转换，支持PIN在不同密钥体系间的转换
- `PIN Offset Calculation` - PIN偏移量计算，支持IBM 3624算法的PIN验证
- `Multi-Algorithm Support` - 多算法支持，同时支持SM4和3DES算法的PIN处理

#### 1.1.2 CVV生成与校验功能 (CVV Generation & Verification)

**核心API接口：**
- `POST /hsm/cvv/generate` - CVV生成接口，生成卡片验证值
- `POST /hsm/cvv/verify` - CVV校验接口，验证CVV的正确性
- `POST /hsm/cvv2/generate` - CVV2生成接口，生成动态CVV2值
- `POST /hsm/cvv2/verify` - CVV2校验接口，验证CVV2的正确性

**主要服务类：**
- `EncryptionManager.cvvByDc()` - CVV生成方法，使用DC命令生成CVV1
- `EncryptionManager.cvv2ByDw()` - CVV2生成方法，使用DW命令生成CVV2
- `EncryptionManager.cvvCheckByDc()` - CVV校验方法，验证CVV1的正确性
- `EncryptionManager.cvv2CheckByDw()` - CVV2校验方法，验证CVV2的正确性
- `AuthEncryptionProperties.getCardBinCvk1()` - CVK1密钥获取，根据卡BIN获取CVV1密钥

**CVV处理功能：**
- `CVV1 Generation & Verification` - CVV1生成和验证，支持磁条卡CVV处理
- `CVV2 Generation & Verification` - CVV2生成和验证，支持芯片卡CVV2处理
- `Card BIN Key Mapping` - 卡BIN密钥映射，支持多卡BIN的密钥管理
- `Service Code Support` - 服务代码支持，根据服务代码选择合适的CVV算法

#### 1.1.3 ARQC交易认证码验证功能 (ARQC Transaction Authentication)

**核心API接口：**
- `POST /hsm/arqc/3des` - 3DES ARQC验证接口，验证3DES算法的交易认证码
- `POST /hsm/arqc/sm4` - SM4 ARQC验证接口，验证SM4算法的交易认证码
- `POST /hsm/arpc/generate` - ARPC生成接口，生成授权响应密文

**主要服务类：**
- `EncryptionManager.checkArqc3Des()` - 3DES ARQC验证方法，验证3DES算法ARQC
- `EncryptionManager.checkArqcSm4()` - SM4 ARQC验证方法，验证SM4算法ARQC
- `EncryptionApi.checkArqc3des()` - 3DES ARQC验证底层API，使用WF命令验证
- `EncryptionApi.checkArqcsm4()` - SM4 ARQC验证底层API，使用WG命令验证
- `EncryptionManager.mcCavvGenerateHmac()` - MC CAVV HMAC生成，支持MasterCard CAVV处理

**ARQC处理功能：**
- `EMV ARQC Verification` - EMV ARQC验证，支持EMV标准的交易认证码验证
- `ARPC Generation` - ARPC生成，生成授权响应密文用于交易确认
- `ATC Counter Support` - ATC计数器支持，处理应用交易计数器
- `Dual Algorithm Support` - 双算法支持，同时支持SM4和3DES算法

#### 1.1.4 MAC消息认证码计算功能 (MAC Message Authentication Code)

**核心API接口：**
- `POST /hsm/mac/generate` - MAC生成接口，计算消息认证码
- `POST /hsm/mac/verify` - MAC验证接口，验证消息认证码的正确性
- `POST /hsm/mac/sm4/calculate` - SM4 MAC计算接口，使用SM4算法计算MAC

**主要服务类：**
- `EncryptionManager.generateMac()` - MAC生成方法，生成ISO 8583报文的MAC
- `EncryptionManager.verifyMac()` - MAC验证方法，验证ISO 8583报文的MAC
- `EncryptionManager.generateOrVerifyMacBySm4()` - SM4 MAC处理方法，使用SM4算法
- `EncryptionApi.generateOrVerifyMacBySm4()` - SM4 MAC底层API，使用W9命令
- `EncryptionApi.organizeMabHex()` - MAB组织方法，组织用于MAC计算的数据

**MAC处理功能：**
- `ISO 8583 MAC Processing` - ISO 8583 MAC处理，支持银联报文MAC计算
- `SM4/3DES MAC Algorithms` - SM4/3DES MAC算法，支持国密和国际标准
- `MAB Data Organization` - MAB数据组织，自动组织MAC计算所需的数据
- `Online/Batch MAC Methods` - 联机/批量MAC方法，支持不同的MAC计算方式

#### 1.1.5 HSM连接池管理功能 (HSM Connection Pool Management)

**核心API接口：**
- `ConnectWithHsm.sendAndRec()` - HSM通信接口，发送命令并接收响应
- `ConnectPool.getConnect()` - 连接获取接口，从连接池获取可用连接
- `ConnectPool.putConnect()` - 连接归还接口，将连接归还到连接池
- `SocketIo.connectHsm()` - HSM连接接口，建立与HSM设备的TCP连接

**主要服务类：**
- `ConnectWithHsm` - HSM连接管理器，管理与HSM设备的连接和通信
- `ConnectPool` - TCP连接池，提供高性能的连接池管理
- `SocketIo` - Socket通信类，处理与HSM设备的底层TCP通信
- `AuthEncryptionProperties` - HSM配置属性，管理HSM设备连接参数
- `EncryptionAutoConfiguration` - HSM自动配置，自动配置HSM相关组件

**连接池管理功能：**
- `High-Performance Connection Pool` - 高性能连接池，支持30个并发连接
- `Connection Health Monitoring` - 连接健康监控，自动检测和重连断开的连接
- `Load Balancing` - 负载均衡，优先使用已连接的Socket连接
- `Timeout & Retry Mechanism` - 超时和重试机制，保证通信的可靠性

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：MySQL（分片支持）, Redis（缓存支持）

**中间件**：Nacos（服务发现与配置中心）, ShardingSphere 5.5.0（分库分表）

**构建工具**：Maven 3.5+

**JDK版本要求**：Java 17

**其他核心组件**：
- MyBatis（数据持久化）
- Undertow（Web服务器，替代Tomcat）
- ShardingSphere 5.5.0（分库分表中间件）
- Jasypt 3.0.5（配置加解密）
- Plumelog 3.1.0-SNAPSHOT（分布式日志）
- Spring Boot Actuator（应用监控）
- Micrometer Prometheus（监控指标）
- Apache Commons Lang3（工具库）
- Apache Commons Codec（编解码工具）
- Spring Cloud OpenFeign（服务间调用）

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**Docker**：Docker & Docker Compose（可选，用于容器化部署）

**中间件要求**：
- Nacos Server（用于服务发现和配置中心）
- MySQL（业务数据库，支持分片）
- Redis（缓存支持）
- HSM硬件设备（硬件安全模块）

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-third-party-service.git
cd anytxn-third-party-service/anytxn-hsm
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：配置 IntelliJ IDEA 启动参数**

在 IntelliJ IDEA 中配置应用启动的程序参数：

1. **打开运行配置**
   - 在 IDEA 中找到主启动类 `HsmServerApplication.java`
   - 右键点击该文件，选择 `Run` 或 `Debug`
   - 或者点击 IDEA 顶部的运行配置下拉菜单

2. **编辑运行配置**
   - 点击运行配置旁的编辑按钮（齿轮图标）
   - 或者选择 `Run` → `Edit Configurations...`

3. **配置程序参数**
   - 在 `Program arguments` 字段中输入以下参数：
   ```
   --NACOS_USERNAME=nacos --NACOS_PASSWORD=nacos --CLUSTER_NAME=A --NACOS_SERVER_ADDR=**********:8848,**********:8848,**********:8848 --NACOS_NAMESPACE=2cff4a1a-e0f9-42e4-8472-a7b0a17a1235
   ```
   
   > **注意**：请根据实际环境情况调整以下参数值：
   > - `NACOS_USERNAME`: Nacos 用户名
   > - `NACOS_PASSWORD`: Nacos 密码  
   > - `CLUSTER_NAME`: 集群名称
   > - `NACOS_SERVER_ADDR`: Nacos 服务器地址列表
   > - `NACOS_NAMESPACE`: Nacos 命名空间ID

4. **保存配置**
   - 点击 `Apply` 和 `OK` 保存配置

**第五步：运行应用**

推荐在IDE中直接运行`HsmServerApplication.java`。

或者通过命令行启动，指定`dev`环境配置：

```bash
mvn spring-boot:run -pl anytxn-hsm-server -Dspring-boot.run.profiles=dev
```

服务启动后，你可以访问[http://localhost:18094](http://localhost:18094)来检查服务状态。

## 4. 项目结构 (Project Structure)

**多模块结构说明**：
项目采用Maven多模块架构，包含SDK和Server两个核心子模块，SDK模块负责HSM加密业务逻辑实现，Server模块负责微服务启动和运行时配置。

**各模块职责划分**：
SDK模块包含HSM连接管理、加密算法实现、密钥管理、业务接口等核心功能，Server模块负责应用启动、Nacos集成、监控配置、分片设置等运行时支持。

**包结构说明**：
业务包遵循com.anytech.anytxn.hsm命名规范，采用分层架构设计，包含控制器层、服务层、客户端层、配置层等完整的HSM集成架构。

**关键目录介绍**：

```
.
├── anytxn-hsm-sdk/                      # SDK模块 - 核心业务实现
│   ├── src/main/java/
│   │   └── com.anytech.anytxn.hsm/
│   │       ├── EnableHsmAPI.java                # 自动配置启动器
│   │       ├── config/                          # 配置类
│   │       │   ├── AuthEncryptionProperties.java    # 加密机配置属性
│   │       │   └── EncryptionAutoConfiguration.java # 自动配置类
│   │       ├── constants/                       # 常量定义
│   │       │   └── EncryptionConstant.java
│   │       ├── controller/                      # REST控制器
│   │       │   ├── CreateSecretKeyController.java
│   │       │   └── IEncryptionController.java
│   │       └── service/                         # 业务服务层
│   │           ├── ApplicationManager.java      # 应用管理器
│   │           ├── EncryptionApi.java           # 加密机底层API
│   │           ├── EncryptionManager.java       # 加密业务管理器(1258行核心代码)
│   │           └── client/                      # HSM客户端连接层
│   │               ├── Config.java              # 配置接口
│   │               ├── ConnectPool.java         # TCP连接池
│   │               ├── ConnectWithHsm.java      # HSM连接管理
│   │               ├── CreateSecretKeyDto.java  # 密钥创建DTO
│   │               └── SocketIo.java            # Socket通信
├── anytxn-hsm-server/                   # Server模块 - 服务启动器
│   ├── src/main/java/                   # 启动类
│   │   └── HsmServerApplication.java
│   └── src/main/resources/
│       ├── application.yaml             # 应用配置(端口18094)
│       ├── bootstrap.yml                # 引导配置
│       ├── banner.txt                   # 启动横幅
│       └── log4j2.xml                   # 日志配置
└── pom.xml                              # 父POM
```

- `anytxn-hsm-sdk`: 包含所有HSM加密业务逻辑、连接池管理、密钥操作、PIN/CVV/ARQC/MAC处理等完整功能。
- `anytxn-hsm-server`: 应用启动模块，配置服务端口18094，集成Nacos配置中心和分片支持，提供独立的HSM加密微服务。

## 5. 配置 (Configuration)

本项目使用Nacos作为配置中心。核心配置文件在Nacos中，`group`为`DEFAULT_GROUP`，`Data ID`为`anytxn-hsm-server-dev.yaml`。

本地开发时，会优先加载`src/main/resources/application.yaml`中的配置。

**关键环境变量/配置项说明:**
| 变量名/属性名 | 示例值 | 描述 |
|---------------|--------|------|
| `NACOS_SERVER_ADDR` | `************:8848` | Nacos 服务器地址 |
| `spring.application.name` | `anytxn-hsm-server` | HSM服务名称 |
| `server.port` | `18094` | 服务端口 |
| `anytxn.shardingsphere.enabled` | `true` | 是否启用分库分表 |
| `anytxn.number.segmentEnable` | `true` | 是否启用分段号码 |
| `anytxn.number.tenantIds` | `6001,6002` | 租户ID配置 |
| `anytxn.shardingsphere.properties.dataId` | `sharding-config-hsm.yaml` | 分片配置文件ID |
| `auth.encryption.ip` | `HSM设备IP` | 主加密机IP地址 |
| `auth.encryption.port` | `HSM设备端口` | 主加密机端口 |
| `auth.encryption.poolNum` | `30` | HSM连接池大小 |

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建
mvn clean install

# 跳过测试构建
mvn clean install -DskipTests

# 构建并生成Docker镜像
mvn clean package docker:build
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包生成JAR文件
4. Docker镜像构建和推送至Harbor仓库
5. Kubernetes集群部署服务
6. 配置HSM设备连接参数

**Docker部署方式**：
```bash
# 构建镜像
docker build -t anytxn-hsm-server:latest ./anytxn-hsm-server

# 运行容器
docker run -d -p 18094:18094 \
  -e NACOS_SERVER_ADDR=************:8848 \
  -e SPRING_PROFILES_ACTIVE=dev \
  -e AUTH_ENCRYPTION_IP=HSM_DEVICE_IP \
  -e AUTH_ENCRYPTION_PORT=HSM_DEVICE_PORT \
  anytxn-hsm-server:latest
```

**环境变量配置**：
- **镜像仓库**: k8s.jrx.com/anytxn/anytxn-hsm-server
- **Kubernetes命名空间**: anytxn-namespace
- **服务端口**: 18094
- **健康检查**: /actuator/health
- **HSM设备**: 需要配置HSM硬件设备连接参数

## 7. 监控与告警 (Monitoring & Alerting)

- **健康检查**: Spring Boot Actuator 提供了健康检查端点。
  - `http://<host>:18094/actuator/health`
- **Metrics**: 指标已接入 Prometheus。端点地址：
  - `http://<host>:18094/actuator/prometheus`
- **HSM加密监控**: 提供PIN验证成功率、CVV校验结果、ARQC验证统计、MAC计算性能等关键加密业务指标监控。
- **连接池监控**: 监控HSM设备连接状态、连接池使用率、连接超时和重连次数等连接质量指标。
- **密钥管理监控**: 监控密钥操作频次、密钥分发状态、密钥校验结果等密钥管理相关指标。
- **加密算法监控**: 监控SM4/3DES算法使用情况、加密操作响应时间、算法切换状态等性能指标。
- **日志**: 应用日志格式为JSON，统一由Plumelog分布式日志系统收集。你可以在日志平台中查看。
  - **加密操作日志**: 记录PIN/CVV/ARQC/MAC等加密操作过程和结果
  - **HSM通信日志**: 记录与HSM设备的通信状态和异常信息
  - **连接池日志**: 记录连接获取、归还、重连等连接池操作
  - **安全审计日志**: 记录密钥操作、敏感数据处理等安全相关事件