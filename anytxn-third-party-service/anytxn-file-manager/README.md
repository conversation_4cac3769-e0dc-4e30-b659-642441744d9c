# AnyTXN信用卡核心系统-文件管理服务 (AnyTXN File Manager Service)

本项目是AnyTXN信用卡核心系统的文件管理服务，负责处理文件扫描、文件处理状态跟踪、MD5防重复校验等业务。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-文件管理服务 (AnyTXN File Manager Service)](#anytxn信用卡核心系统-文件管理服务-anytxn-file-manager-service)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build & Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring & Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的文件管理服务是整个微服务体系的**文件处理中枢**，在AnyTXN生态系统中负责文件扫描、处理状态跟踪、MD5防重复校验等关键功能。该项目解决了批量文件处理、文件状态监控、重复文件检测、多租户文件隔离等关键业务问题。

主要功能包括：企业级文件扫描和处理能力、正则表达式文件名匹配、MD5防重复校验机制、文件处理状态跟踪和监控、多租户架构支持、与批量调度系统深度集成、OK文件验证机制、文件复制和移动操作、分布式文件处理任务调度、完善的异常处理和状态管理，为整个AnyTXN信用卡核心系统提供稳定、高效、可扩展的文件管理能力。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于API接口和服务实现分析，anytxn-file-manager工程提供以下五大核心功能模块：

#### 1.1.1 文件扫描配置管理功能 (File Scan Configuration Management)

**核心API接口：**
- `POST /fm/scan/add` - 文件扫描参数添加接口，创建新的文件扫描配置
- `PUT /fm/scan/update` - 文件扫描参数更新接口，修改文件扫描配置
- `GET /fm/scan/query/pageNum/{pageNum}/pageSize/{pageSize}` - 文件扫描参数分页查询接口
- `GET /fm/scan/query/id/{id}` - 根据ID查询文件扫描参数接口
- `DELETE /fm/scan/delete/id/{id}` - 根据ID删除文件扫描参数接口

**主要服务类：**
- `FileManagerScanParamService` - 文件管理扫描参数服务，管理文件扫描配置的增删改查
- `FileScanController` - 文件扫描控制器，提供文件扫描配置管理的REST API接口
- `FileManagerScanParamMapper` - 文件扫描参数数据访问层，处理扫描配置的数据库操作
- `FileManagerScanParamDTO` - 文件扫描参数数据传输对象，封装扫描配置信息
- `SequenceIdGen` - 序列号生成器，为文件扫描配置生成唯一标识

**配置管理功能：**
- `Scan Path Configuration` - 扫描路径配置，支持多目录文件扫描
- `File Name Regex Matching` - 文件名正则表达式匹配，支持复杂文件名模式
- `Cron Expression Scheduling` - Cron表达式调度，支持灵活的扫描时间配置
- `Copy Path Management` - 复制路径管理，配置文件处理后的存储位置

#### 1.1.2 智能文件扫描处理功能 (Intelligent File Scan Processing)

**核心API接口：**
- `FileScanMainOperator.doScan()` - 文件扫描主方法，执行文件扫描的核心逻辑
- `@EnableFileManagerApi` - 文件管理API启用注解，自动配置文件管理相关组件
- `@EnableFilePathConfig` - 文件路径配置启用注解，启用文件路径配置功能

**主要服务类：**
- `FileScanMainOperator` - 文件扫描主操作器，执行文件扫描的核心业务逻辑
- `FileScanParamMonitor` - 文件扫描参数监控器，管理定时扫描任务的生命周期
- `ScheduledStatusMonitor` - 调度状态监控器，监控与调度系统的集成状态
- `FileManagerScanProcessService` - 文件扫描处理服务，管理文件处理过程和状态
- `TriggerTask` - 触发任务，基于Cron表达式执行定时文件扫描

**扫描处理功能：**
- `Regex File Matching` - 正则表达式文件匹配，支持复杂文件名模式识别
- `OK File Validation` - OK文件验证机制，确保文件完整性和可处理性
- `File Status Tracking` - 文件状态跟踪，记录文件处理的完整生命周期
- `Multi-Tenant Support` - 多租户支持，实现租户级别的文件隔离和处理

#### 1.1.3 MD5防重复校验功能 (MD5 Anti-Duplication Verification)

**核心API接口：**
- `FileScanMainOperator.getSha256()` - SHA256哈希计算方法，生成文件唯一标识
- `FileManagerScanProcessService.selectByMD5()` - MD5查重方法，检查文件是否重复处理

**主要服务类：**
- `FileCopyService` - 文件复制服务，提供文件SHA256计算和防重复检查
- `DigestUtils` - 摘要工具类，使用Apache Commons Codec进行哈希计算
- `FileManagerScanProcessSelfMapper` - 文件处理自定义数据访问层，支持MD5查重查询
- `FileManagerScanProcess` - 文件扫描处理实体，记录文件MD5和处理状态

**防重复机制：**
- `SHA256 Hash Calculation` - SHA256哈希计算，为每个文件生成唯一指纹
- `Configurable Check Days` - 可配置检查天数，设定重复检查的时间范围
- `Duplicate File Detection` - 重复文件检测，自动识别和标记重复文件
- `MD5 Duplicate File Creation` - MD5重复文件创建，为重复文件生成标记文件

#### 1.1.4 文件处理器策略功能 (File Processor Strategy)

**核心API接口：**
- `IFileProcessor.process()` - 文件处理器接口方法，定义文件处理的通用规范
- `SchedulerFileProcessor.process()` - 调度文件处理器，处理与调度系统集成的文件
- `SimpleFileProcessor.process()` - 简单文件处理器，处理基础文件移动和验证

**主要服务类：**
- `IFileProcessor` - 文件处理器接口，定义文件处理的通用规范和契约
- `SchedulerFileProcessor` - 调度文件处理器，集成AnyScheduler批量调度系统
- `SimpleFileProcessor` - 简单文件处理器，提供基础的文件移动和状态更新
- `FileCopyService` - 文件复制服务，提供文件复制、移动和哈希计算功能
- `SchedulerClientConfig` - 调度客户端配置，管理与调度系统的连接和配置

**处理器策略：**
- `Strategy Pattern Implementation` - 策略模式实现，支持多种文件处理策略
- `Scheduler Integration` - 调度系统集成，与AnyScheduler批量调度系统深度集成
- `File Copy & Move Operations` - 文件复制和移动操作，支持安全的文件转移
- `Status Management` - 状态管理，跟踪文件处理的各个阶段和结果

#### 1.1.5 文件处理历史管理功能 (File Processing History Management)

**核心API接口：**
- `GET /fm/scan/history/query/pageNum/{pageNum}/pageSize/{pageSize}` - 文件处理历史分页查询接口
- `POST /fm/scan/history/done` - 文件处理状态修复接口，手动修复处理状态
- `FileManagerScanProcessService.insertOne()` - 文件处理记录插入方法

**主要服务类：**
- `FileManagerScanProcessService` - 文件扫描处理服务，管理文件处理历史记录
- `FileManagerScanProcessMapper` - 文件处理记录数据访问层，处理历史数据的数据库操作
- `FileManagerScanProcessDTO` - 文件处理记录数据传输对象，封装处理历史信息
- `ScanFileProcessStatusEnum` - 文件处理状态枚举，定义文件处理的各种状态
- `FileResponseDetailEnum` - 文件响应详情枚举，定义文件处理的详细结果

**历史管理功能：**
- `Processing History Tracking` - 处理历史跟踪，完整记录文件处理的生命周期
- `Status Query & Filter` - 状态查询和过滤，支持按状态、时间等条件查询历史
- `Manual Status Fix` - 手动状态修复，支持人工干预修复异常状态
- `Audit Trail` - 审计跟踪，提供完整的文件处理审计日志

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：MySQL, Redis（缓存支持）

**中间件**：Nacos（服务发现与配置中心）, ShardingSphere 5.5.0（分库分表）

**构建工具**：Maven 3.5+

**JDK版本要求**：Java 17

**其他核心组件**：
- MyBatis（数据持久化）
- Undertow（Web服务器，替代Tomcat）
- ShardingSphere 5.5.0（分库分表中间件）
- Plumelog（分布式日志）
- Apache Commons IO（文件操作工具）
- SnakeYAML（YAML配置解析）
- AnyScheduler Batch SDK（批量调度集成）
- AnyTXN Common Sharding（分片公共组件）
- AnyTXN Common Sequence（序列号生成组件）

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**Docker**：Docker & Docker Compose（可选，用于容器化部署）

**中间件要求**：
- Nacos Server（用于服务发现和配置中心）
- MySQL（业务数据库）
- Redis（缓存支持）

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-third-party-service.git
cd anytxn-third-party-service/anytxn-file-manager
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：配置 IntelliJ IDEA 启动参数**

在 IntelliJ IDEA 中配置应用启动的程序参数：

1. **打开运行配置**
   - 在 IDEA 中找到主启动类 `FileManagerServerApplication.java`
   - 右键点击该文件，选择 `Run` 或 `Debug`
   - 或者点击 IDEA 顶部的运行配置下拉菜单

2. **编辑运行配置**
   - 点击运行配置旁的编辑按钮（齿轮图标）
   - 或者选择 `Run` → `Edit Configurations...`

3. **配置程序参数**
   - 在 `Program arguments` 字段中输入以下参数：
   ```
   --NACOS_USERNAME=nacos --NACOS_PASSWORD=nacos --CLUSTER_NAME=A --NACOS_SERVER_ADDR=**********:8848,**********:8848,**********:8848 --NACOS_NAMESPACE=2cff4a1a-e0f9-42e4-8472-a7b0a17a1235
   ```
   
   > **注意**：请根据实际环境情况调整以下参数值：
   > - `NACOS_USERNAME`: Nacos 用户名
   > - `NACOS_PASSWORD`: Nacos 密码  
   > - `CLUSTER_NAME`: 集群名称
   > - `NACOS_SERVER_ADDR`: Nacos 服务器地址列表
   > - `NACOS_NAMESPACE`: Nacos 命名空间ID

4. **保存配置**
   - 点击 `Apply` 和 `OK` 保存配置

**第五步：运行应用**

推荐在IDE中直接运行`FileManagerServerApplication.java`。

或者通过命令行启动，指定`dev`环境配置：

```bash
mvn spring-boot:run -pl anytxn-file-manager-server -Dspring-boot.run.profiles=dev
```

服务启动后，你可以访问[http://localhost:18089](http://localhost:18089)来检查服务状态。

## 4. 项目结构 (Project Structure)

**多模块结构说明**：
项目采用Maven多模块架构，包含SDK和Server两个核心子模块，SDK模块负责核心业务逻辑实现，Server模块负责服务启动和运行时配置。

**各模块职责划分**：
SDK模块包含所有业务逻辑、控制器、服务层、数据访问层、配置类等，Server模块负责应用启动、环境配置、依赖注入等运行时支持。

**包结构说明**：
业务包遵循com.anytech.anytxn.file命名规范，采用分层架构设计，包含控制器层、服务层、数据访问层、领域模型层、工具层等完整的企业级应用架构。

**关键目录介绍**：

```
.
├── anytxn-file-manager-sdk/          # SDK模块 - 核心业务实现
│   ├── src/main/java/
│   │   └── com.anytech.anytxn.file/
│   │       ├── EnableFileManagerApi.java    # 模块启用注解
│   │       ├── EnableFilePathConfig.java    # 文件路径配置启用注解
│   │       ├── config/                      # 配置类
│   │       │   ├── AnytxnFilePathConfig.java
│   │       │   ├── FileExecutorConfig.java
│   │       │   ├── FileMainConfig.java
│   │       │   └── SchedulerClientConfig.java
│   │       ├── controller/                  # REST控制器
│   │       │   └── FileScanController.java
│   │       ├── domain/                      # 领域模型
│   │       │   ├── dto/                     # 数据传输对象
│   │       │   └── model/                   # 实体模型
│   │       ├── service/                     # 业务服务层
│   │       │   ├── FileScanMainOperator.java
│   │       │   └── processor/               # 文件处理器策略
│   │       │       ├── IFileProcessor.java
│   │       │       ├── SchedulerFileProcessor.java
│   │       │       └── SimpleFileProcessor.java
│   │       ├── mapper/                      # 数据访问层
│   │       ├── monitor/                     # 监控组件
│   │       ├── runner/                      # 多租户任务运行器
│   │       └── utils/                       # 工具类
├── anytxn-file-manager-server/              # Server模块 - 服务启动器
│   ├── src/main/java/                       # 启动类
│   └── src/main/resources/
│       └── application.yaml                 # 应用配置(端口18089)
└── pom.xml                                  # 父POM
```

- `anytxn-file-manager-sdk`: 包含所有业务逻辑、Controller、Service、Mapper等，提供文件扫描、处理、监控等完整功能。
- `anytxn-file-manager-server`: 应用启动模块，配置服务端口18089，集成Nacos配置中心和多租户分片支持。

## 5. 配置 (Configuration)

本项目使用Nacos作为配置中心。核心配置文件在Nacos中，`group`为`DEFAULT_GROUP`，`Data ID`为`anytxn-file-server-dev.yaml`。

本地开发时，会优先加载`src/main/resources/application.yaml`中的配置。

**关键环境变量/配置项说明:**
| 变量名/属性名 | 示例值 | 描述 |
|---------------|--------|------|
| `NACOS_SERVER_ADDR` | `************:8848` | Nacos 服务器地址 |
| `spring.application.name` | `anytxn-file-server` | 文件服务名称 |
| `server.port` | `18089` | 服务端口 |
| `anytxn.shardingsphere.enabled` | `true` | 是否启用分库分表 |
| `anytxn.number.segmentEnable` | `true` | 是否启用分段号码 |
| `anytxn.number.tenantIds` | `6001,6002` | 租户ID配置 |
| `anytxn.shardingsphere.properties.dataId` | `sharding-config-file.yaml` | 分片配置文件ID |

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建
mvn clean install

# 跳过测试构建
mvn clean install -DskipTests

# 构建并生成Docker镜像
mvn clean package docker:build
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包生成JAR文件
4. Docker镜像构建和推送至Harbor仓库
5. Kubernetes集群部署服务

**Docker部署方式**：
```bash
# 构建镜像
docker build -t anytxn-file-manager-server:latest ./anytxn-file-manager-server

# 运行容器
docker run -d -p 18089:18089 \
  -e NACOS_SERVER_ADDR=************:8848 \
  -e SPRING_PROFILES_ACTIVE=dev \
  anytxn-file-manager-server:latest
```

**环境变量配置**：
- **镜像仓库**: k8s.jrx.com/anytxn/anytxn-file-manager-server
- **Kubernetes命名空间**: anytxn-namespace
- **服务端口**: 18089
- **健康检查**: /actuator/health

## 7. 监控与告警 (Monitoring & Alerting)

- **健康检查**: Spring Boot Actuator 提供了健康检查端点。
  - `http://<host>:18089/actuator/health`
- **Metrics**: 指标已接入 Prometheus。端点地址：
  - `http://<host>:18089/actuator/prometheus`
- **文件处理监控**: 提供文件扫描任务状态、处理效率、MD5校验结果、重复文件检测等关键业务指标监控。
- **批量调度监控**: 监控与AnyScheduler批量调度系统的集成状态、任务执行结果、调度成功率等指标。
- **多租户监控**: 监控各租户的文件处理情况、资源使用情况、隔离效果等多租户相关指标。
- **日志**: 应用日志格式为JSON，统一由Plumelog分布式日志系统收集。你可以在日志平台中查看。
  - **文件扫描日志**: 记录文件扫描过程、匹配结果、处理状态
  - **处理器日志**: 记录不同处理器的执行情况和结果
  - **系统集成日志**: 记录与批量调度系统的交互和异常信息