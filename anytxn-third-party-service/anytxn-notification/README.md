# AnyTXN信用卡核心系统-通知服务 (AnyTXN Notification Service)

本项目是AnyTXN信用卡核心系统的通知服务，负责处理多渠道消息发送、消息模板管理、规则驱动通知路由等业务。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-通知服务 (AnyTXN Notification Service)](#anytxn信用卡核心系统-通知服务-anytxn-notification-service)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build & Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring & Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的通知服务是整个微服务体系的**多渠道消息中枢**，在AnyTXN生态系统中负责短信、邮件、微信、APP推送等多渠道消息发送、消息模板管理、规则驱动通知路由等关键功能。该项目解决了多渠道消息统一管理、消息模板标准化、智能消息路由、异步消息处理等关键业务问题。

主要功能包括：支持6种通知渠道的统一管理（SMS、MAIL、微信、APP推送、LINE BC、LINE AOA）、基于策略模式的灵活渠道扩展机制、规则引擎驱动的智能消息路由、完善的消息模板管理系统、基于RocketMQ的异步消息处理、多租户支持的机构隔离、完整的消息发送日志记录、支持交易通知、账单通知、还款提醒等业务场景、RESTful API统一接口设计、Swagger自动化API文档，为整个AnyTXN信用卡核心系统提供高可用、高并发、易扩展的多渠道通知能力。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于API接口和服务实现分析，anytxn-notification工程提供以下五大核心功能模块：

#### 1.1.1 消息统一发送管理功能 (Unified Message Sending Management)

**核心API接口：**
- `POST /message/send` - 消息统一发送接口，支持多渠道消息发送的统一入口
- `@EnableNotificationApi` - 通知API启用注解，自动配置通知服务相关组件
- `@EnableNotificationService` - 通知服务启用注解，启用通知服务核心功能

**主要服务类：**
- `MessageController` - 消息统一发送控制器，提供消息发送的REST API接口
- `IMessageHandleService` - 消息处理服务接口，定义消息发送的核心业务规范
- `MessageHandleServiceImpl` - 消息处理服务实现，处理消息发送的核心业务逻辑
- `SendMessageDTO` - 发送消息数据传输对象，封装消息发送的完整参数
- `TxnRuleMatcher` - 交易规则匹配器，基于规则引擎进行消息路由匹配

**统一发送功能：**
- `Multi-Channel Routing` - 多渠道路由，根据规则自动选择合适的发送渠道
- `Rule-Based Message Routing` - 基于规则的消息路由，使用规则引擎进行智能路由
- `Template Parameter Replacement` - 模板参数替换，支持动态参数替换和模板渲染
- `Message Sending Logging` - 消息发送日志，完整记录消息发送过程和结果

#### 1.1.2 多渠道策略模式功能 (Multi-Channel Strategy Pattern)

**核心API接口：**
- `AbstractSendMessage.sendMsg()` - 抽象发送策略方法，定义消息发送的通用规范
- `WeChatSendMessage.sendMsg()` - 微信发送策略，实现微信消息发送逻辑
- `MailSendMessage.sendMsg()` - 邮件发送策略，实现邮件消息发送逻辑
- `AppPushSendMessage.sendMsg()` - APP推送策略，实现移动应用推送逻辑

**主要服务类：**
- `AbstractSendMessage` - 抽象发送策略，定义消息发送的通用模板和基础功能
- `WeChatSendMessage` - 微信发送策略，专门处理微信公众号和小程序消息推送
- `MailSendMessage` - 邮件发送策略，处理SMTP邮件发送和邮件模板渲染
- `AppPushSendMessage` - APP推送策略，处理移动应用的Push通知
- `LineBcSendMessage` - LINE BC发送策略，实现LINE Business Connect消息发送
- `LineAoaSendMessage` - LINE AOA发送策略，实现LINE Add-on Application消息发送

**策略模式特性：**
- `Strategy Pattern Implementation` - 策略模式实现，支持灵活的渠道扩展和切换
- `Channel-Specific Logic` - 渠道特定逻辑，每个渠道实现自己的发送逻辑
- `Template Processing` - 模板处理，支持各渠道特定的模板格式和参数
- `Error Handling & Retry` - 错误处理和重试，提供渠道级别的异常处理机制

#### 1.1.3 消息模板管理功能 (Message Template Management)

**核心API接口：**
- `ParmSmsTemplateMapper.selectByChannelCodeAndTemplateCode()` - 根据渠道和模板代码查询模板
- `ParmSmsTemplateMapper.selectByTemplateCode()` - 根据模板代码查询模板配置
- `AbstractSendMessage.replaceTemplate()` - 模板参数替换方法

**主要服务类：**
- `ParmSmsTemplate` - 短信模板实体，存储模板内容和配置信息
- `ParmSmsTemplateMapper` - 短信模板数据访问层，处理模板的数据库操作
- `IParmMsgGatherService` - 参数消息集合服务，管理消息集合和渠道配置
- `MsgGatherDTO` - 消息集合数据传输对象，封装消息集合和渠道信息
- `MsgGatherChannelDTO` - 消息集合渠道数据传输对象，封装渠道配置

**模板管理功能：**
- `Template Content Management` - 模板内容管理，支持多渠道模板内容配置
- `Parameter Replacement` - 参数替换，支持动态参数替换和验证
- `Template Validation` - 模板验证，验证模板格式和参数完整性
- `Multi-Tenant Template` - 多租户模板，支持机构级别的模板隔离

#### 1.1.4 异步消息队列处理功能 (Asynchronous Message Queue Processing)

**核心API接口：**
- `RocketMQTemplate.asyncSend()` - RocketMQ异步发送接口，支持异步消息发送
- `NotificationHandleServiceImpl.sendByMQ()` - MQ方式发送消息方法
- `WeChatSendMessage.sendByMQ()` - 微信消息MQ发送方法

**主要服务类：**
- `RocketMQTemplate` - RocketMQ模板，提供消息队列操作的统一接口
- `MsgPushProperty` - 消息推送配置属性，管理消息队列的主题和分组配置
- `WechatPushProperty` - 微信推送配置属性，管理微信消息队列配置
- `MappingPushProperty` - 映射推送配置属性，管理映射消息队列配置
- `NotificationSendMqAutoConfig` - 通知发送MQ自动配置，自动配置消息队列相关组件

**异步处理功能：**
- `Asynchronous Message Sending` - 异步消息发送，提高系统响应性能
- `Message Queue Management` - 消息队列管理，支持多主题多分组的队列配置
- `Callback Handling` - 回调处理，支持发送成功和失败的回调处理
- `Message Persistence` - 消息持久化，保证消息的可靠性和一致性

#### 1.1.5 通知事件处理功能 (Notification Event Processing)

**核心API接口：**
- `INotificationEventHandler.sendSmsFinancialEvent()` - 金融交易短信事件发送接口
- `INotificationEventHandler.otpGeneration()` - OTP生成接口，生成一次性密码
- `INotificationEventHandler.otpVerify()` - OTP验证接口，验证一次性密码

**主要服务类：**
- `INotificationEventHandler` - 通知事件处理器接口，定义事件处理规范
- `NotificationEventHandlerImpl` - 通知事件处理器实现，处理各类通知事件
- `INotificationHandleService` - 通知处理服务接口，定义通知发送和批量发送规范
- `NotificationHandleServiceImpl` - 通知处理服务实现，处理通知发送的核心逻辑
- `WechatPushHandleServiceImpl` - 微信推送处理服务，专门处理微信推送业务

**事件处理功能：**
- `Financial Transaction Events` - 金融交易事件，处理交易相关的通知事件
- `OTP Management` - OTP管理，提供一次性密码的生成、发送和验证
- `Event-Driven Notification` - 事件驱动通知，基于业务事件触发通知发送
- `Batch Notification Processing` - 批量通知处理，支持批量消息发送和处理

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：MySQL（业务数据存储）, Redis（缓存支持）

**中间件**：Nacos（服务发现与配置中心）, Apache RocketMQ（异步消息队列）

**构建工具**：Maven 3.5+

**JDK版本要求**：Java 17

**其他核心组件**：
- MyBatis（数据持久化）
- Undertow（Web服务器，替代Tomcat）
- Spring Cloud OpenFeign（服务间调用）
- Feign Hystrix（服务熔断）
- Apache Commons Collections4（集合工具）
- Swagger（API文档）
- AnyTXN Common Sequence（序列号生成）
- AnyTXN Common Rule（规则引擎）
- AnyTXN Business Core SDK（业务核心SDK）
- AnyTXN Parameter SDK（参数管理SDK）

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**Docker**：Docker & Docker Compose（可选，用于容器化部署）

**中间件要求**：
- Nacos Server（用于服务发现和配置中心）
- MySQL（业务数据库）
- Redis（缓存支持）
- Apache RocketMQ（消息队列）

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-third-party-service.git
cd anytxn-third-party-service/anytxn-notification
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：配置 IntelliJ IDEA 启动参数**

在 IntelliJ IDEA 中配置应用启动的程序参数：

1. **打开运行配置**
   - 在 IDEA 中找到主启动类 `NotificationServerApplication.java`
   - 右键点击该文件，选择 `Run` 或 `Debug`
   - 或者点击 IDEA 顶部的运行配置下拉菜单

2. **编辑运行配置**
   - 点击运行配置旁的编辑按钮（齿轮图标）
   - 或者选择 `Run` → `Edit Configurations...`

3. **配置程序参数**
   - 在 `Program arguments` 字段中输入以下参数：
   ```
   --NACOS_USERNAME=nacos --NACOS_PASSWORD=nacos --CLUSTER_NAME=A --NACOS_SERVER_ADDR=**********:8848,**********:8848,**********:8848 --NACOS_NAMESPACE=2cff4a1a-e0f9-42e4-8472-a7b0a17a1235
   ```
   
   > **注意**：请根据实际环境情况调整以下参数值：
   > - `NACOS_USERNAME`: Nacos 用户名
   > - `NACOS_PASSWORD`: Nacos 密码  
   > - `CLUSTER_NAME`: 集群名称
   > - `NACOS_SERVER_ADDR`: Nacos 服务器地址列表
   > - `NACOS_NAMESPACE`: Nacos 命名空间ID

4. **保存配置**
   - 点击 `Apply` 和 `OK` 保存配置

**第五步：运行应用**

推荐在IDE中直接运行`NotificationServerApplication.java`。

或者通过命令行启动，指定`dev`环境配置：

```bash
mvn spring-boot:run -pl anytxn-notification-server -Dspring-boot.run.profiles=dev
```

服务启动后，通知服务将注册到Nacos并开始监听消息队列。

## 4. 项目结构 (Project Structure)

**多模块结构说明**：
项目采用Maven多模块架构，包含SDK和Server两个核心子模块，SDK模块负责通知业务逻辑实现，Server模块负责微服务启动和运行时配置。

**各模块职责划分**：
SDK模块包含多渠道通知策略、消息处理逻辑、规则引擎集成、模板管理、数据访问等核心功能，Server模块负责应用启动、Nacos集成、消息队列配置等运行时支持。

**包结构说明**：
业务包遵循com.anytech.anytxn.notification命名规范，采用分层架构设计，包含控制器层、服务层、策略层、数据访问层、配置层等完整的通知服务架构。

**关键目录介绍**：

```
.
├── anytxn-notification-sdk/             # SDK模块 - 核心业务实现
│   ├── src/main/java/
│   │   └── com.anytech.anytxn.notification/
│   │       ├── EnableNotificationApi.java       # API启用注解
│   │       ├── EnableNotificationService.java   # 服务启用注解
│   │       ├── client/                          # Feign客户端
│   │       │   ├── FinancialTransactionHandleFeignClient.java
│   │       │   ├── NonFinancialTranHandleFeignClient.java
│   │       │   └── TransactionHandleFeignClientProperties.java
│   │       ├── config/                          # 配置类
│   │       │   ├── ExecutorConfig.java          # 线程池配置
│   │       │   ├── NotificationConfig.java      # 通知配置
│   │       │   └── mq/                          # 消息队列配置
│   │       │       ├── NotificationSendMqAutoConfig.java
│   │       │       ├── MsgPushProperty.java
│   │       │       └── wechat/                  # 微信消息配置
│   │       ├── controller/                      # REST控制器
│   │       │   └── MessageController.java       # 消息统一发送接口
│   │       ├── domain/                          # 数据模型
│   │       │   ├── dto/                         # 数据传输对象
│   │       │   │   ├── SendMessageDTO.java      # 发送消息DTO
│   │       │   │   ├── MessageDTO.java          # 消息DTO
│   │       │   │   └── wechat/                  # 微信消息DTO
│   │       │   └── model/                       # 实体模型
│   │       │       ├── NotificationSendLog.java
│   │       │       └── NotificationSendLogHistory.java
│   │       ├── service/                         # 业务服务层
│   │       │   ├── MessageHandleServiceImpl.java    # 消息处理核心服务
│   │       │   ├── NotificationEventHandlerImpl.java
│   │       │   ├── WechatPushHandleServiceImpl.java
│   │       │   └── strategy/                    # 策略模式实现
│   │       │       ├── AbstractSendMessage.java     # 抽象发送策略
│   │       │       ├── WeChatSendMessage.java        # 微信发送策略
│   │       │       ├── MailSendMessage.java          # 邮件发送策略
│   │       │       ├── AppPushSendMessage.java       # APP推送策略
│   │       │       ├── LineBcSendMessage.java        # LINE BC策略
│   │       │       └── LineAoaSendMessage.java       # LINE AOA策略
│   │       ├── mapper/                          # 数据访问层
│   │       ├── constants/                       # 常量定义
│   │       ├── enums/                           # 枚举类
│   │       └── exception/                       # 异常处理
├── anytxn-notification-server/          # Server模块 - 服务启动器
│   ├── src/main/java/                   # 启动类
│   │   └── NotificationServerApplication.java
│   └── src/main/resources/              # 配置文件
└── pom.xml                              # 父POM
```

- `anytxn-notification-sdk`: 包含多渠道通知策略、消息处理逻辑、规则引擎集成、模板管理等完整通知功能。
- `anytxn-notification-server`: 应用启动模块，集成Nacos配置中心和RocketMQ消息队列，提供独立的通知微服务。

## 5. 配置 (Configuration)

本项目使用Nacos作为配置中心。核心配置文件在Nacos中，`group`为`DEFAULT_GROUP`，`Data ID`为`notification-dev.yaml`。

本地开发时，会优先加载`src/main/resources/bootstrap.yml`中的配置。

**关键环境变量/配置项说明:**
| 变量名/属性名 | 示例值 | 描述 |
|---------------|--------|------|
| `NACOS_SERVER_ADDR` | `************:8848` | Nacos 服务器地址 |
| `spring.application.name` | `notification` | 通知服务名称 |
| `spring.cloud.nacos.config.prefix` | `notification` | 配置前缀 |
| `anytxn.msg.sms.topic` | `SMS_TOPIC` | 短信消息队列主题 |
| `anytxn.msg.sms.group` | `SMS_GROUP` | 短信消息队列分组 |
| `anytxn.msg.wechat.topic` | `WECHAT_TOPIC` | 微信消息队列主题 |
| `anytxn.msg.wechat.group` | `WECHAT_GROUP` | 微信消息队列分组 |
| `anytxn.msg.mapping.topic` | `MAPPING_TOPIC` | 映射消息队列主题 |
| `anytxn.msg.mapping.group` | `MAPPING_GROUP` | 映射消息队列分组 |

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建
mvn clean install

# 跳过测试构建
mvn clean install -DskipTests

# 构建并生成Docker镜像
mvn clean package docker:build
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包生成JAR文件
4. Docker镜像构建和推送至Harbor仓库
5. Kubernetes集群部署服务
6. 配置RocketMQ消息队列连接

**Docker部署方式**：
```bash
# 构建镜像
docker build -t anytxn-notification-server:latest ./anytxn-notification-server

# 运行容器
docker run -d \
  -e NACOS_SERVER_ADDR=************:8848 \
  -e SPRING_PROFILES_ACTIVE=dev \
  -e ROCKETMQ_NAMESERVER=rocketmq-nameserver:9876 \
  anytxn-notification-server:latest
```

**环境变量配置**：
- **镜像仓库**: k8s.jrx.com/anytxn/anytxn-notification-server
- **Kubernetes命名空间**: anytxn-namespace
- **服务端口**: 动态分配（通过Nacos服务发现）
- **健康检查**: /actuator/health
- **消息队列**: 需要配置RocketMQ连接参数

## 7. 监控与告警 (Monitoring & Alerting)

- **健康检查**: Spring Boot Actuator 提供了健康检查端点。
  - `http://<host>:<port>/actuator/health`
- **Metrics**: 指标已接入 Prometheus。端点地址：
  - `http://<host>:<port>/actuator/prometheus`
- **多渠道通知监控**: 提供短信发送成功率、邮件投递状态、微信推送结果、APP推送到达率等关键业务指标监控。
- **消息队列监控**: 监控RocketMQ消息生产和消费状态、消息堆积情况、消费延迟等队列健康指标。
- **模板管理监控**: 监控消息模板使用频次、模板参数替换成功率、模板错误统计等模板相关指标。
- **规则引擎监控**: 监控消息路由规则执行情况、规则匹配成功率、规则引擎性能等智能路由指标。
- **日志**: 应用日志格式为JSON，统一由Plumelog分布式日志系统收集。你可以在日志平台中查看。
  - **消息发送日志**: 记录各渠道消息发送过程、发送结果、异常信息
  - **规则执行日志**: 记录消息路由规则匹配和执行过程
  - **模板处理日志**: 记录消息模板加载、参数替换、渲染结果
  - **系统集成日志**: 记录与外部系统的交互和异常信息