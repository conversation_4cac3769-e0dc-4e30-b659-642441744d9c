# AnyTXN信用卡核心系统-第三方服务集成平台 (AnyTXN Third Party Service Integration Platform)

本项目是 AnyTXN信用卡核心系统 的第三方服务集成平台，负责处理通知服务、文件管理、HSM加密服务等第三方服务集成业务。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-第三方服务集成平台 (AnyTXN Third Party Service Integration Platform)](#anytxn信用卡核心系统-第三方服务集成平台-anytxn-third-party-service-integration-platform)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build & Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring & Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的第三方服务集成平台是整个微服务体系的**第三方服务集成中枢**，在AnyTXN生态系统中负责通知服务、文件管理、HSM加密服务等关键第三方服务集成功能。该项目解决了第三方服务集成复杂度、消息通知统一管理、文件处理标准化、金融级加密安全保障等关键技术问题。

主要功能包括：覆盖143个业务类的完整第三方服务集成体系、31个核心业务服务类支持多渠道服务集成、包含通知服务、文件管理、HSM加密三大核心服务模块、支持短信、邮件、APP推送、微信等多渠道消息通知能力、企业级文件扫描处理和MD5完整性校验、金融级HSM硬件安全模块集成支持SM4/3DES双算法、完善的策略模式和处理器模式设计、集成Nacos服务治理和ShardingSphere分库分表、支持多租户架构和分布式系统部署，为整个AnyTXN信用卡核心系统提供稳定、安全、可扩展的第三方服务集成能力。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于API接口和批量作业分析，anytxn-third-party-service工程提供以下三大核心功能模块：

#### 1.1.1 多渠道通知服务功能 (Multi-Channel Notification Service)

**核心API接口：**
- `POST /message/send` - 消息统一发送接口，支持多渠道消息发送的统一入口
- `@EnableNotificationApi` - 通知API启用注解，自动配置通知服务相关组件
- `@EnableNotificationService` - 通知服务启用注解，启用通知服务核心功能

**主要服务类：**
- `IMessageHandleService` - 消息处理服务接口，定义消息发送的核心业务规范
- `MessageHandleServiceImpl` - 消息处理服务实现，处理消息发送的核心业务逻辑
- `INotificationHandleService` - 通知处理服务接口，定义通知发送和批量发送规范
- `NotificationEventHandlerImpl` - 通知事件处理器，处理通知事件的业务逻辑
- `WechatPushHandleServiceImpl` - 微信推送处理服务，专门处理微信推送业务

**策略模式实现：**
- `AbstractSendMessage` - 抽象发送策略，定义消息发送的通用模板
- `WeChatSendMessage` - 微信发送策略，实现微信消息发送逻辑
- `MailSendMessage` - 邮件发送策略，实现邮件消息发送逻辑
- `AppPushSendMessage` - APP推送策略，实现移动应用推送逻辑
- `LineBcSendMessage` - LINE BC发送策略，实现LINE Business Connect消息发送
- `LineAoaSendMessage` - LINE AOA发送策略，实现LINE Add-on Application消息发送

**批量作业：**
- `notificationBatchJob` - 通知批处理作业，批量处理待发送的通知消息
- `messageQueueProcessJob` - 消息队列处理作业，处理RocketMQ中的异步消息
- `notificationRetryJob` - 通知重试作业，重试发送失败的通知消息

#### 1.1.2 企业级文件管理功能 (Enterprise File Management)

**核心API接口：**
- `POST /fm/scan/add` - 文件扫描参数添加接口，创建新的文件扫描配置
- `PUT /fm/scan/update` - 文件扫描参数更新接口，修改文件扫描配置
- `GET /fm/scan/query/pageNum/{pageNum}/pageSize/{pageSize}` - 文件扫描参数分页查询接口
- `GET /fm/scan/query/id/{id}` - 根据ID查询文件扫描参数接口
- `DELETE /fm/scan/delete/id/{id}` - 根据ID删除文件扫描参数接口

**主要服务类：**
- `FileManagerScanParamService` - 文件管理扫描参数服务，管理文件扫描配置
- `FileManagerScanProcessService` - 文件管理扫描处理服务，处理文件扫描业务逻辑
- `FileScanMainOperator` - 文件扫描主操作器，执行文件扫描的核心逻辑
- `FileCopyService` - 文件复制服务，处理文件复制和移动操作
- `FileScanParamMonitor` - 文件扫描参数监控器，监控文件扫描任务执行

**处理器模式实现：**
- `IFileProcessor` - 文件处理器接口，定义文件处理的通用规范
- `SchedulerFileProcessor` - 调度文件处理器，处理与调度系统集成的文件
- `FileProcessorFactory` - 文件处理器工厂，根据文件类型选择合适的处理器

**批量作业：**
- `fileScanJob` - 文件扫描作业，定时扫描指定目录下的文件
- `fileProcessJob` - 文件处理作业，批量处理扫描到的文件
- `fileMd5CheckJob` - 文件MD5校验作业，验证文件完整性和防重复
- `fileCleanupJob` - 文件清理作业，清理过期和已处理的文件

#### 1.1.3 金融级HSM加密服务功能 (Financial-Grade HSM Encryption Service)

**核心API接口：**
- `POST /hsm/arqc/3des` - 3DES ARQC验证接口，验证3DES算法的交易认证码
- `POST /hsm/arqc/sm4` - SM4 ARQC验证接口，验证SM4算法的交易认证码
- `POST /hsm/cvv/check` - CVV校验接口，验证卡片验证值的正确性
- `POST /hsm/pin/verify` - PIN验证接口，验证持卡人个人识别码
- `POST /hsm/mac/calculate` - MAC计算接口，计算消息认证码

**主要服务类：**
- `EncryptionManager` - 加密管理器，统一管理所有HSM加密操作（1258行核心代码）
- `ApplicationManager` - 应用管理器，管理HSM应用程序和连接
- `IEncryptionRpcApi` - 加密RPC API接口，定义远程加密服务调用规范
- `AuthEncryptionProperties` - 加密机配置属性，管理HSM连接和算法配置
- `EncryptionAutoConfiguration` - 加密自动配置类，自动配置HSM相关组件

**加密算法支持：**
- `SM4 Encryption` - 国密SM4对称加密算法，支持国产密码标准
- `3DES Encryption` - 3DES对称加密算法，支持国际金融标准
- `PIN Block Processing` - PIN块处理，支持多种PIN块格式
- `CVV Generation & Verification` - CVV生成和验证，支持CVV1/CVV2算法
- `ARQC Verification` - 交易认证码验证，支持EMV标准

**批量作业：**
- `hsmKeyManagementJob` - HSM密钥管理作业，批量管理和更新密钥
- `hsmPerformanceMonitorJob` - HSM性能监控作业，监控加密操作性能
- `hsmConnectionPoolJob` - HSM连接池管理作业，管理TCP连接池状态
- `hsmSecurityAuditJob` - HSM安全审计作业，记录和审计加密操作

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：MySQL 8.3.0, Redis（缓存支持）

**中间件**：Nacos（服务发现与配置中心）, RocketMQ（异步消息队列）, ShardingSphere 5.5.0（分库分表）

**构建工具**：Maven 3.5+

**JDK版本要求**：Java 17

**其他核心组件**：
- MyBatis 3.5.16（数据持久化）
- MyBatis Spring Boot 3.0.4
- Undertow（Web服务器，替代Tomcat）
- ShardingSphere 5.5.0（分库分表中间件）
- Jasypt 3.0.5（配置加解密）
- Plumelog 3.1.0-SNAPSHOT（分布式日志）
- Spring Boot Actuator（应用监控）
- Apache Commons Collections（集合工具）
- Apache Commons Codec（摘要算法）
- Spring Cloud OpenFeign（服务间调用）

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**Docker**：Docker & Docker Compose（可选，用于容器化部署）

**中间件要求**：
- Nacos Server（用于服务发现和配置中心）
- MySQL 8.3.0+（业务数据库）
- Redis 6.2+（缓存支持）
- RocketMQ（消息队列）

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-third-party-service.git
cd anytxn-third-party-service
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：配置 IntelliJ IDEA 启动参数**

在 IntelliJ IDEA 中配置应用启动的程序参数：

1. **打开运行配置**
   - 在 IDEA 中找到各服务的主启动类
   - 右键点击对应文件，选择 `Run` 或 `Debug`
   - 或者点击 IDEA 顶部的运行配置下拉菜单

2. **编辑运行配置**
   - 点击运行配置旁的编辑按钮（齿轮图标）
   - 或者选择 `Run` → `Edit Configurations...`

3. **配置程序参数**
   - 在 `Program arguments` 字段中输入以下参数：
   ```
   --NACOS_USERNAME=nacos --NACOS_PASSWORD=nacos --CLUSTER_NAME=A --NACOS_SERVER_ADDR=**********:8848,**********:8848,**********:8848 --NACOS_NAMESPACE=2cff4a1a-e0f9-42e4-8472-a7b0a17a1235
   ```
   
   > **注意**：请根据实际环境情况调整以下参数值：
   > - `NACOS_USERNAME`: Nacos 用户名
   > - `NACOS_PASSWORD`: Nacos 密码  
   > - `CLUSTER_NAME`: 集群名称
   > - `NACOS_SERVER_ADDR`: Nacos 服务器地址列表
   > - `NACOS_NAMESPACE`: Nacos 命名空间ID

4. **保存配置**
   - 点击 `Apply` 和 `OK` 保存配置

**第五步：运行应用**

推荐在 IDE 中分别运行各服务的启动类：
- `NotificationServerApplication.java` (通知服务)
- `FileManagerServerApplication.java` (文件管理服务)
- `HsmServerApplication.java` (HSM加密服务)

或者通过命令行启动，指定 `dev` 环境配置：

```bash
# 启动文件管理服务
mvn spring-boot:run -pl anytxn-file-manager/anytxn-file-manager-server -Dspring-boot.run.profiles=dev

# 启动HSM加密服务
mvn spring-boot:run -pl anytxn-hsm/anytxn-hsm-server -Dspring-boot.run.profiles=dev
```

服务启动后，你可以访问对应端口来检查服务状态：
- 文件管理服务：[http://localhost:18089](http://localhost:18089)
- HSM加密服务：[http://localhost:18094](http://localhost:18094)

## 4. 项目结构 (Project Structure)

**多模块结构说明**：
项目采用Maven多模块架构，包含3个核心业务子模块：通知服务（notification）、文件管理（file-manager）、HSM加密（hsm），每个子模块均采用SDK+Server双层架构设计。

**各模块职责划分**：
通知服务负责多渠道消息发送和推送，文件管理负责企业级文件扫描和处理，HSM加密负责金融级硬件安全模块集成，各模块通过统一的配置中心和服务治理进行协调。

**包结构说明**：
业务包遵循com.anytech.anytxn.{module}命名规范，通知服务采用策略模式实现多渠道发送，文件管理采用处理器模式实现多类型处理，HSM服务采用管理器模式实现加密算法集成。

**关键目录介绍**：

```
.
├── anytxn-notification/                 # 通知服务模块
│   ├── anytxn-notification-sdk/         # 通知服务SDK
│   │   ├── src/main/java/
│   │   │   └── com.anytech.anytxn.notification/
│   │   │       ├── client/              # Feign客户端
│   │   │       ├── config/              # 配置类
│   │   │       ├── controller/          # REST控制器
│   │   │       ├── domain/              # 领域模型
│   │   │       ├── service/             # 业务服务层
│   │   │       │   └── strategy/        # 策略模式实现 (10个策略类)
│   │   │       └── mapper/              # 数据访问层
│   │   └── anytxn-notification-server/  # 通知服务启动模块
├── anytxn-file-manager/                 # 文件管理服务模块
│   ├── anytxn-file-manager-sdk/         # 文件管理SDK
│   │   ├── src/main/java/
│   │   │   └── com.anytech.anytxn.filemanager/
│   │   │       ├── config/              # 配置类
│   │   │       ├── domain/              # 领域模型
│   │   │       ├── service/             # 业务服务层 (8个服务类)
│   │   │       │   └── processor/       # 处理器模式实现
│   │   │       └── runner/              # 多租户任务运行器
│   │   └── anytxn-file-manager-server/  # 文件管理服务启动模块
│   │       └── src/main/resources/
│   │           └── application.yaml     # 端口18089配置
├── anytxn-hsm/                          # HSM加密服务模块
│   ├── anytxn-hsm-sdk/                  # HSM加密SDK
│   │   ├── src/main/java/
│   │   │   └── com.anytech.anytxn.hsm/
│   │   │       ├── config/              # HSM配置
│   │   │       ├── domain/              # 加密领域模型
│   │   │       ├── manager/             # 加密管理器 (1258行核心代码)
│   │   │       └── service/             # 加密服务层 (5个服务类)
│   │   └── anytxn-hsm-server/           # HSM服务启动模块
│   │       └── src/main/resources/
│   │           └── application.yaml     # 端口18094配置
└── pom.xml                              # 父 POM
```

- `anytxn-notification`: 提供多渠道消息通知服务，支持短信、邮件、微信、APP推送等10种发送策略。
- `anytxn-file-manager`: 提供企业级文件管理服务，端口18089，支持智能文件扫描、MD5校验、多租户处理。
- `anytxn-hsm`: 提供金融级HSM加密服务，端口18094，支持SM4/3DES双算法、PIN验证、CVV验证等完整加密能力。

## 5. 配置 (Configuration)

本项目使用 Nacos 作为配置中心。各子模块核心配置文件在 Nacos 中，`group` 为 `DEFAULT_GROUP`，`Data ID` 分别为各服务的配置文件。

本地开发时，会优先加载各服务 `src/main/resources/application.yaml` 中的配置。

**关键环境变量/配置项说明:**
| 变量名/属性名 | 示例值 | 描述 |
|---------------|--------|------|
| `NACOS_SERVER_ADDR` | `************:8848` | Nacos 服务器地址 |
| `spring.application.name` | `anytxn-file-server` | 文件管理服务名称 |
| `spring.application.name` | `anytxn-hsm-server` | HSM加密服务名称 |
| `server.port` | `18089` | 文件管理服务端口 |
| `server.port` | `18094` | HSM加密服务端口 |
| `anytxn.shardingsphere.enabled` | `true` | 是否启用分库分表 |
| `anytxn.number.segmentEnable` | `true` | 是否启用分段号码 |
| `anytxn.number.tenantIds` | `6001,6002` | 租户ID配置 |

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建
mvn clean install

# 跳过测试构建
mvn clean install -DskipTests

# 构建并生成Docker镜像
mvn clean package docker:build
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包生成各服务JAR文件
4. Docker镜像构建和推送至Harbor仓库
5. Kubernetes集群部署各个服务

**Docker部署方式**：
```bash
# 构建文件管理服务镜像
docker build -t anytxn-file-manager-server:latest ./anytxn-file-manager/anytxn-file-manager-server

# 构建HSM加密服务镜像
docker build -t anytxn-hsm-server:latest ./anytxn-hsm/anytxn-hsm-server

# 运行文件管理服务容器
docker run -d -p 18089:18089 \
  -e NACOS_SERVER_ADDR=************:8848 \
  -e SPRING_PROFILES_ACTIVE=dev \
  anytxn-file-manager-server:latest

# 运行HSM加密服务容器
docker run -d -p 18094:18094 \
  -e NACOS_SERVER_ADDR=************:8848 \
  -e SPRING_PROFILES_ACTIVE=dev \
  anytxn-hsm-server:latest
```

**环境变量配置**：
- **镜像仓库**: k8s.jrx.com/anytxn/anytxn-{service}-server
- **Kubernetes命名空间**: anytxn-namespace
- **服务端口**: 18089(file-manager), 18094(hsm), notification(Nacos配置)
- **健康检查**: /actuator/health

## 7. 监控与告警 (Monitoring & Alerting)

- **健康检查**: Spring Boot Actuator 提供了健康检查端点。
  - 文件管理服务：`http://<host>:18089/actuator/health`
  - HSM加密服务：`http://<host>:18094/actuator/health`
- **Metrics**: 指标已接入 Prometheus。端点地址：
  - 文件管理服务：`http://<host>:18089/actuator/prometheus`
  - HSM加密服务：`http://<host>:18094/actuator/prometheus`
- **通知服务监控**: 提供消息发送成功率、多渠道发送量、消息队列堆积等关键业务指标监控。
- **文件管理监控**: 监控文件扫描任务状态、处理效率、MD5校验结果、重复文件检测等指标。
- **HSM加密监控**: 监控加密操作响应时间、HSM连接状态、密钥管理操作、加密算法性能等关键指标。
- **第三方服务监控**: 监控各类第三方服务接口调用成功率、响应时间、错误率等集成服务指标。
- **日志**: 应用日志格式为 JSON，统一由 Plumelog 分布式日志系统收集。你可以在日志平台中查看。
  - **通知服务日志**: 记录消息发送过程、策略选择、推送结果
  - **文件管理日志**: 记录文件扫描状态、处理结果、异常信息
  - **HSM加密日志**: 记录加密操作、密钥管理、HSM通信状态
  - **系统集成日志**: 记录第三方服务调用和集成异常信息