# AnyTXN信用卡核心系统-卡片服务模块 (AnyTXN Card Service)

本项目是 AnyTXN信用卡核心系统 的卡片服务模块，负责处理信用卡开户、卡片管理、授权控制、批量作业等核心业务。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-卡片服务模块 (AnyTXN Card Service)](#anytxn信用卡核心系统-卡片服务模块-anytxn-card-service)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build \& Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring \& Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的卡片服务模块是企业级微服务应用，在整个AnyTXN生态系统中处于核心地位，为交易服务、客户服务、额度服务等其他业务模块提供基础的卡片服务能力。该服务解决了信用卡全生命周期管理、多租户数据隔离、高并发交易处理、复杂批量作业调度等关键业务问题。

主要功能包括：信用卡开户服务、卡片全生命周期管理（激活、挂失、解挂、换卡、销卡）、卡片授权信息管理、为下游服务提供卡片查询接口、通过批量作业处理年费扣收和制卡文件生成、支持3D安全认证和加密算法集成。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于API接口和批量作业分析，anytxn-card工程提供以下五大核心功能模块：

#### 1.1.1 卡片开户与销户功能 (Account Opening & Closing)

**核心API接口：**
- `POST /card/open` - 信用卡开户接口，支持个人卡和公司卡开户
- `POST /card/openCardCheck` - 开户预校验接口，验证客户资格和产品信息  
- `POST /card/checkCardExistByCidAndProNumber` - 检查客户在指定产品下是否已有卡片

**主要服务类：**
- `IAccountOpeningService` - 开户服务，处理开户业务逻辑和数据创建
- `IAccountClosingService` - 销户服务，处理销户条件检查和数据清理
- `IBatchCloseAccountService` - 批量销户服务，支持批量账户关闭处理

**批量作业：**
- `closeaccount` - 销户批量作业，定期处理满足条件的账户销户
- `autoclosecard` - 自动销卡作业，处理长期未激活的卡片自动销户
- `autocloseinactive` - 非活跃卡片自动关闭作业

#### 1.1.2 卡片生命周期管理功能 (Card Lifecycle Management)

**核心API接口：**
- `PUT /card/activate` - 卡片激活接口，支持多种激活方式
- `POST /card/reportLoss` - 卡片挂失接口，支持临时挂失和永久挂失
- `POST /card/reissueCard` - 补换卡接口，处理毁损、丢失等场景的补换卡
- `PUT /card/block` - 卡片止付接口，支持多种止付原因码

**主要服务类：**
- `ICardService` - 卡片服务，提供激活、挂失、补卡等核心功能
- `ICreateCardService` - 制卡服务，负责卡号生成和Luhn校验
- `IRenewalCardService` - 换卡服务，处理到期换卡和升级换卡
- `ICardCloseService` - 卡片关闭服务，处理卡片注销业务

**批量作业：**
- `renewalcard` - 到期换卡批量作业，自动处理即将到期的卡片换卡
- `cardrestriction` - 卡片限制处理作业，根据风控规则设置卡片使用限制
- `cardrelegate` - 卡片降级处理作业，根据使用情况调整卡片等级

#### 1.1.3 卡片授权与安全功能 (Card Authorization & Security)

**核心API接口：**
- `POST /card/cvv2Check` - CVV2验证接口，验证卡片安全码
- `GET /card/cvv/{cardNumber}` - CVV码查询接口，支持动态CVV生成
- `POST /card/cardTokenCheck` - 卡片Token验证接口，支持虚拟卡验证
- `POST /card/virtualCardInfo` - 虚拟卡信息查询接口

**主要服务类：**
- `ICardAuthorizationInfoService` - 卡片授权信息服务，管理授权参数
- `ICardAdjustLimitService` - 卡片额度调整服务，处理临时和永久额度调整
- `ICardBusinessTokenService` - 卡片业务Token服务，支持数字化支付
- `ICardMdesService` - MDES服务，支持Mastercard数字化钱包

**批量作业：**
- `threedsecure` - 3D安全认证批量处理作业
- `velocitymigration` - 交易速度控制迁移作业
- `blockCodeUpdate` - 止付码更新批量作业

#### 1.1.4 制卡与文件处理功能 (Card Production & File Processing)

**核心API接口：**
- `POST /card/embossing/generateFile` - 制卡文件生成接口
- `POST /card/printing/processResult` - 制卡结果处理接口
- `GET /card/embossing/status` - 制卡状态查询接口

**主要服务类：**
- `ICardEmbossingDataService` - 制卡数据服务，生成制卡所需数据
- `IGenerateCreateCardFileService` - 制卡文件生成服务
- `ICardFactoryReturnFileService` - 卡厂回盘文件处理服务
- `ICardPrintingResultInfoService` - 制卡结果信息服务

**批量作业：**
- `embossing` - 制卡数据处理作业群，包含新卡、换卡、紧急制卡等多个子作业
- `generatefile` - 制卡文件生成作业，生成发送给卡厂的制卡指令文件
- `e6makecard` - E6制卡系统集成作业，支持多种制卡场景
- `cardLetter` - 卡函生成作业，生成卡片寄送地址标签

#### 1.1.5 年费与费用管理功能 (Annual Fee & Fee Management)

**核心API接口：**
- `POST /card/annualFee/calculate` - 年费计算接口
- `POST /card/manageFee/charge` - 管理费收取接口
- `GET /card/fee/history` - 费用历史查询接口

**主要服务类：**
- `ICardManageFeeService` - 卡片管理费服务
- `ICardAnnualFeeService` - 年费服务，处理年费计算和收取
- `ISmsService` - 短信费服务，处理短信通知费用

**批量作业：**
- `annualfee` - 年费扣收批量作业，按账单日分区处理年费收取
- `annualfeetip` - 年费提醒作业，在年费到期前发送提醒短信
- `cardmanagefee` - 卡片管理费批量作业，定期收取卡片管理费用
- `smsfee` - 短信费收取作业，处理短信服务费用收取

#### 1.1.6 对外集成与数据同步功能 (External Integration & Data Sync)

**批量作业：**
- `mrs` - MRS系统数据同步作业群，向Mastercard报送卡片数据
- `loyalty` - 积分系统数据同步作业，同步卡片和客户信息到积分系统
- `mydcsportal` - 企业门户数据同步作业，同步企业客户和交易数据
- `supervision` - 监管报送作业，生成监管机构要求的报表数据
- `dayprocess` - 日终处理作业，进行账务和数据的日终汇总处理

**技术特色：**
- **高并发处理**：采用Spring Batch分区处理，支持多线程并行执行
- **容错机制**：完整的异常处理和重试机制，保证批量作业稳定运行  
- **监控告警**：集成作业监控和异常告警，及时发现和处理问题
- **数据一致性**：严格的事务控制，确保批量处理的数据一致性

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：MySQL 8.3.0, Redis（通过Redisson 3.41.0集成）

**中间件**：Nacos（服务发现与配置中心）, RocketMQ 2.3.1

**构建工具**：Maven 3.5+

**JDK版本要求**：Java 17

**其他核心组件**：
- MyBatis 3.5.16（数据持久化）
- MyBatis Spring Boot Starter 3.0.4  
- Undertow（Web服务器）
- ShardingSphere 5.5.0（分库分表）
- Spring Batch 4.2.1.RELEASE（批量处理）
- Jasypt 3.0.5（参数加解密）
- Plumelog 3.1.0-SNAPSHOT（分布式日志）
- Micrometer + Prometheus（监控）

_图 1: 卡片服务交互图_

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**Docker**：Docker & Docker Compose

**中间件要求**：
- Nacos Server
- RocketMQ Server  
- MySQL 8.3.0+
- Redis 6.2+

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-card.git
cd anytxn-card
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：配置 IntelliJ IDEA 启动参数**

在 IntelliJ IDEA 中配置应用启动的程序参数：

1. **打开运行配置**
   - 在 IDEA 中找到主启动类（如 `CardServerApplication.java`）
   - 右键点击该文件，选择 `Run` 或 `Debug`
   - 或者点击 IDEA 顶部的运行配置下拉菜单

2. **编辑运行配置**
   - 点击运行配置旁的编辑按钮（齿轮图标）
   - 或者选择 `Run` → `Edit Configurations...`

3. **配置程序参数**
   - 在 `Program arguments` 字段中输入以下参数：
   ```
   --NACOS_USERNAME=nacos --NACOS_PASSWORD=nacos --CLUSTER_NAME=A --NACOS_SERVER_ADDR=**********:8848,**********:8848,**********:8848 --NACOS_NAMESPACE=2cff4a1a-e0f9-42e4-8472-a7b0a17a1235
   ```
   
   > **注意**：请根据实际环境情况调整以下参数值：
   > - `NACOS_USERNAME`: Nacos 用户名
   > - `NACOS_PASSWORD`: Nacos 密码  
   > - `CLUSTER_NAME`: 集群名称
   > - `NACOS_SERVER_ADDR`: Nacos 服务器地址列表
   > - `NACOS_NAMESPACE`: Nacos 命名空间ID

4. **保存配置**
   - 点击 `Apply` 和 `OK` 保存配置

**第五步：运行应用**

我们推荐在 IntelliJ IDEA 中直接运行 `CardServerApplication.java`。

或者通过命令行启动，指定 `dev` 环境配置：

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

服务启动后，你可以访问 [http://localhost:18086](http://localhost:18086) 来检查服务状态。

## 4. 项目结构 (Project Structure)

**多模块结构说明**：
项目采用Maven多模块架构，按照业务功能和技术层次进行模块划分，实现清晰的依赖关系和职责分离。

**各模块职责划分**：
基础定义模块提供API接口和基础组件，业务实现模块包含核心逻辑，服务启动模块负责应用启动，批量处理模块处理定时任务，客户端模块对外暴露接口。

**包结构说明**：
各模块遵循标准包命名规范，非sdk模块包名为com.anytech.anytxn.card.模块名，sdk模块包名为com.anytech.anytxn.card。

**关键目录介绍**：

```
.
├── anytxn-card-base          # 基础定义模块 (可被其他服务依赖)
│   └── com.anytech.anytxn.card.base
│       ├── api/              # API接口定义
│       ├── constant/         # 常量类定义
│       ├── domain/           # 领域对象 (BO、DTO)
│       ├── enums/            # 枚举类定义
│       ├── exception/        # 异常类定义
│       ├── service/          # 服务接口定义
│       └── utils/            # 工具类 (Luhn算法等)
├── anytxn-card-sdk           # 业务实现模块 (87个服务实现类)
│   └── com.anytech.anytxn.card
│       ├── config/           # 配置类
│       ├── controller/       # REST控制器
│       ├── mapper/           # 数据映射器
│       └── service/          # 服务实现类
├── anytxn-card-server        # 服务启动模块
│   └── com.anytech.anytxn.card.server
│       ├── CardServerApplication.java  # 主启动类
│       ├── config/           # 服务器配置
│       └── schedule/         # 定时任务
├── anytxn-card-batch         # 批量处理模块 (32个批量作业)
│   └── com.anytech.anytxn.card.batch
│       ├── CardBatchApplication.java   # 批处理启动类
│       ├── config/           # 批处理配置
│       └── job/              # 批处理作业
├── anytxn-card-client        # Feign客户端模块
│   └── com.anytech.anytxn.card.client
│       └── fallback/         # 降级处理
├── doc                       # 项目文档
└── pom.xml                   # 父 POM
```

- `anytxn-card-base`: 定义了对外暴露的API接口、DTO和业务常量，可以被其他微服务依赖，避免了代码重复。
- `anytxn-card-sdk`: 包含所有业务逻辑、Controller、Service等，是核心业务实现模块。
- `anytxn-card-server`: 微服务启动入口，包含Spring Boot应用配置。
- `anytxn-card-batch`: 批量处理模块，包含批处理任务实现。
- `anytxn-card-client`: 为其他微服务提供Feign客户端接口。

## 5. 配置 (Configuration)

本项目使用 Nacos 作为配置中心。核心配置文件在 Nacos 中，`group` 为 `CARD_GROUP`，`Data ID` 为 `anytxn-card-server-dev.yaml`。

本地开发时，会优先加载 `src/main/resources/application.yaml` 中的配置。

**关键环境变量/配置项说明:**
| 变量名/属性名 | 示例值 | 描述 |
|---------------|--------|------|
| `NACOS_SERVER_ADDR` | `************:8848` | Nacos 服务器地址 |
| `SPRING_DATASOURCE_URL` | `***************************************` | 数据库连接地址 |
| `ROCKETMQ_NAME_SERVER` | `***********:9876` | RocketMQ 服务器地址 |
| `REDIS_HOST` | `localhost` | Redis 主机名 |
| `SERVER_PORT` | `18086` | 服务端口号 |

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建
mvn clean install

# 跳过测试构建  
mvn clean install -DskipTests

# 单模块构建
mvn clean install -pl anytxn-card-server -am
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包
4. Docker镜像构建
5. 推送至Harbor镜像仓库
6. Kubernetes集群部署

**Docker部署方式**：
```bash
# 构建Docker镜像
mvn clean package docker:build

# 运行Docker容器
docker run -p 18086:18086 -e SPRING_PROFILES_ACTIVE=dev anytxn-card-server:1.0.2-SNAPSHOT
```

**环境变量配置**：
- `SPRING_PROFILES_ACTIVE`: 指定运行环境（dev/sit/prod）
- `NACOS_SERVER_ADDR`: Nacos服务地址
- `MYSQL_HOST`: MySQL数据库主机
- `REDIS_HOST`: Redis缓存主机
- `ROCKETMQ_NAME_SERVER`: RocketMQ服务地址

## 7. 监控与告警 (Monitoring & Alerting)

- **健康检查**: Spring Boot Actuator 提供了健康检查端点。
  - `http://localhost:18086/actuator/health`
- **Metrics**: 指标已接入 Prometheus。端点地址：
  - `http://localhost:18086/actuator/prometheus`
- **日志**: 应用日志格式为 JSON，统一由 Plumelog 收集并发送到 Elasticsearch。你可以在 Kibana 中查看。
  - **Kibana Dashboard**: [链接到 Kibana 仪表盘](https://kibana.jrx.com)