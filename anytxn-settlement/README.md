# AnyTXN信用卡核心系统-清算应用 (AnyTXN Settlement Service)

本项目是 AnyTXN信用卡核心系统 的清算应用，负责处理清算数据处理、对账匹配、拒绝交易处理、批量清算任务等核心清算业务。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-清算应用 (AnyTXN Settlement Service)](#anytxn信用卡核心系统-清算应用-anytxn-settlement-service)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build & Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring & Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的清算应用是整个微服务体系的**后端清算处理核心**，在AnyTXN生态系统中负责清算数据处理、授权交易匹配、费用计算分润、拒绝交易处理等关键清算业务功能。该项目解决了多卡组织清算数据复杂性、交易匹配准确性、清算效率低下、异常处理困难等关键技术问题。

主要功能包括：支持Visa、MasterCard、JCB、Diners Club、AMEX等5大国际卡组织的清算业务处理、基于智能匹配算法的授权-清算交易自动匹配、多币种汇率转换和复杂费用计算引擎、覆盖383个业务类的完整清算管理体系、包含50+批处理任务的大规模数据处理能力、自动化对账流程和异常处理机制、集成JetCache分布式缓存和ShardingSphere分库分表、支持多租户和企业级数据架构，为整个AnyTXN信用卡核心系统提供高效、准确、可扩展的清算处理能力。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于API接口和批量作业分析，anytxn-settlement工程提供以下五大核心功能模块：

#### 1.1.1 多卡组织清算分析功能 (Multi-Card Scheme Settlement Analysis)

**核心API接口：**
- `POST /settle/settleOutTranList` - 对外入账交易表分页查询接口，支持清算交易数据查询
- `GET /settle/visaDataReject/{id}` - Visa清算拒绝数据查询接口，查询指定拒绝记录详情
- `GET /settle/visaDataHistory/{id}` - Visa清算历史数据查询接口，查询清算历史记录
- `GET /settle/mastercardFinancial/{id}` - MasterCard金融记录查询接口，查询MC清算金融数据

**主要服务类：**
- `IVisaAnalysisService` - Visa清算分析服务，处理Visa卡组织清算数据分析和入账
- `IMasterAnalysisService` - MasterCard清算分析服务，处理MC卡组织清算数据处理
- `IJcbAnalysisService` - JCB清算分析服务，处理JCB卡组织清算业务逻辑
- `IDinersAnalysisService` - Diners清算分析服务，处理Diners Club清算数据
- `IAmexAnalysisService` - AMEX清算分析服务，处理American Express清算业务

**批量作业：**
- `visaSettleJob` - Visa清算处理作业，包含文件读取、数据分析、入账处理等完整流程
- `mastercardFilePostingJob` - MasterCard文件入账作业，处理MC清算文件入账业务
- `jcbSettleJob` - JCB清算处理作业，处理JCB卡组织清算数据
- `dinersSettleJob` - Diners清算处理作业，处理Diners Club清算业务
- `amexSettleJob` - AMEX清算处理作业，处理American Express清算数据

#### 1.1.2 清算文件处理与数据入库功能 (Settlement File Processing & Data Storage)

**核心API接口：**
- `POST /settle/fileUpload` - 清算文件上传接口，支持多种格式清算文件上传
- `GET /settle/fileStatus/{fileId}` - 文件处理状态查询接口，查询文件处理进度
- `POST /settle/fileReprocess` - 文件重新处理接口，支持异常文件重新处理

**主要服务类：**
- `IVisaFileAnalysisService` - Visa文件分析服务，解析Visa清算文件格式和内容
- `IMcSettleFileInService` - MasterCard文件入库服务，处理MC清算文件入库
- `IOnUsSettleFileInService` - OnUs清算文件入库服务，处理内部清算文件
- `IVisaSettleFileInService` - Visa清算文件入库服务，处理Visa文件入库逻辑
- `IPostingEnterHandlerService` - 入账处理服务，统一处理各类清算数据入账

**批量作业：**
- `ipmFilesToLogJob` - IPM文件导入日志表作业，处理MasterCard IPM文件
- `anyAcqFileToLogTableJob` - 收单文件导入日志表作业，处理收单清算文件
- `tqr4FilesToLogJob` - TQR4文件导入作业，处理MasterCard TQR4报告文件
- `visaFileToLogJob` - Visa文件导入日志表作业，处理Visa清算文件导入
- `dinersFileToLogJob` - Diners文件导入作业，处理Diners Club清算文件

#### 1.1.3 授权匹配与费用计算功能 (Authorization Matching & Fee Calculation)

**核心API接口：**
- `POST /settle/authMatch` - 授权匹配接口，执行授权与清算交易匹配
- `GET /settle/matchResult/{transactionId}` - 匹配结果查询接口，查询交易匹配结果
- `POST /settle/feeCalculation` - 费用计算接口，计算清算相关费用

**主要服务类：**
- `ISettleAuthMatchService` - 清算授权匹配服务，处理授权与清算交易智能匹配
- `ISettleCashFeeService` - 清算取现费服务，计算和处理取现相关费用
- `IMarkUpFeeGlamsService` - 标记费用会计服务，处理标记费用的会计处理
- `IBillAmtCurrencyConvertService` - 账单金额币种转换服务，处理多币种汇率转换
- `IPricingRuleService` - 定价规则服务，执行复杂的费用定价规则

**批量作业：**
- `settleAuthMatchAndFeeJob` - 清算授权匹配和费用处理作业，批量执行匹配和费用计算
- `markupFeeCalculationJob` - 标记费用计算作业，批量计算标记费用
- `currencyConversionJob` - 币种转换作业，批量处理汇率转换
- `feeAllocationJob` - 费用分配作业，处理费用在不同账户间的分配
- `pricingRuleExecutionJob` - 定价规则执行作业，批量执行定价规则

#### 1.1.4 清算对账与报表功能 (Settlement Reconciliation & Reporting)

**核心API接口：**
- `GET /settle/reconciliationReport` - 对账报表查询接口，生成清算对账报表
- `GET /settle/unMatchedReport` - 未匹配报表接口，查询未匹配的清算交易
- `GET /settle/settlementSummary` - 清算汇总报表接口，提供清算数据汇总统计

**主要服务类：**
- `ISettleRejectLogService` - 清算拒绝日志服务，管理清算拒绝记录和查询
- `IVisaIncomingHisService` - Visa来文历史服务，管理Visa清算历史数据
- `ISettleOutTransactionService` - 对外入账交易服务，处理对外清算交易数据
- `IReconciliationService` - 对账服务，执行清算数据对账逻辑
- `IReportGenerationService` - 报表生成服务，生成各类清算报表

**批量作业：**
- `visaMatchSumReportJob` - Visa匹配汇总报表作业，生成Visa匹配统计报表
- `visaUnMatchReportJob` - Visa未匹配报表作业，生成Visa未匹配交易报表
- `bulkT112SettlementDetailReportJob` - T112清算明细报表作业，生成MasterCard T112报表
- `mcUnMatchedReportJob` - MasterCard未匹配报表作业，生成MC未匹配报表
- `settlementReconciliationJob` - 清算对账作业，执行日终清算对账流程

#### 1.1.5 异常处理与数据修复功能 (Exception Handling & Data Recovery)

**核心API接口：**
- `POST /settle/exceptionHandle` - 异常处理接口，处理清算异常数据
- `POST /settle/dataRepair` - 数据修复接口，修复异常的清算数据
- `GET /settle/exceptionLog` - 异常日志查询接口，查询清算异常处理日志

**主要服务类：**
- `ISettleIdentifyRuleService` - 清算识别规则服务，识别和处理异常清算数据
- `IExceptionHandlerService` - 异常处理服务，统一处理各类清算异常
- `IDataRepairService` - 数据修复服务，修复损坏或异常的清算数据
- `ISettleRollbackService` - 清算回滚服务，处理清算数据回滚操作
- `IErrorRecoveryService` - 错误恢复服务，从清算错误中恢复数据

**批量作业：**
- `abNormalDataProcessTaskletJob` - 异常数据处理作业，批量处理异常清算数据
- `settleRollbackJob` - 清算回滚作业，批量执行清算数据回滚
- `dataIntegrityCheckJob` - 数据完整性检查作业，检查清算数据完整性
- `errorRecoveryJob` - 错误恢复作业，从系统错误中恢复清算数据
- `duplicateHandlerJob` - 重复数据处理作业，识别和处理重复的清算记录

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：MySQL 8.3.0, Redis（通过JetCache集成）

**中间件**：Nacos（服务发现与配置中心）, ShardingSphere 5.5.0（分库分表）

**构建工具**：Maven 3.5

**JDK版本要求**：Java 17

**其他核心组件**：
- MyBatis 3.5.16（数据持久化）
- MyBatis Spring Boot 3.0.4
- Undertow（Web服务器，替代Tomcat）
- JetCache 2.5.16（分布式缓存）
- ShardingSphere 5.5.0（分库分表中间件）
- Jasypt 3.0.5（配置加解密）
- Plumelog 3.1.0-SNAPSHOT（分布式日志）
- Spring Boot Actuator（应用监控）
- Micrometer Prometheus（指标监控）
- Caffeine（本地缓存）
- Lock4j 2.2.7（分布式锁）

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**Docker**：Docker & Docker Compose（可选，用于容器化部署）

**中间件要求**：
- Nacos Server（用于服务发现和配置中心）
- MySQL 8.3.0+（清算业务数据库）
- Redis 6.2+（分布式缓存）

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-settlement.git
cd anytxn-settlement
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：配置 IntelliJ IDEA 启动参数**

在 IntelliJ IDEA 中配置应用启动的程序参数：

1. **打开运行配置**
   - 在 IDEA 中找到主启动类（如 `SettlementServerApplication.java`）
   - 右键点击该文件，选择 `Run` 或 `Debug`
   - 或者点击 IDEA 顶部的运行配置下拉菜单

2. **编辑运行配置**
   - 点击运行配置旁的编辑按钮（齿轮图标）
   - 或者选择 `Run` → `Edit Configurations...`

3. **配置程序参数**
   - 在 `Program arguments` 字段中输入以下参数：
   ```
   --NACOS_USERNAME=nacos --NACOS_PASSWORD=nacos --CLUSTER_NAME=A --NACOS_SERVER_ADDR=**********:8848,**********:8848,**********:8848 --NACOS_NAMESPACE=2cff4a1a-e0f9-42e4-8472-a7b0a17a1235
   ```
   
   > **注意**：请根据实际环境情况调整以下参数值：
   > - `NACOS_USERNAME`: Nacos 用户名
   > - `NACOS_PASSWORD`: Nacos 密码  
   > - `CLUSTER_NAME`: 集群名称
   > - `NACOS_SERVER_ADDR`: Nacos 服务器地址列表
   > - `NACOS_NAMESPACE`: Nacos 命名空间ID

4. **保存配置**
   - 点击 `Apply` 和 `OK` 保存配置

**第五步：运行应用**

推荐在 IDE 中直接运行 `SettlementServerApplication.java`。

或者通过命令行启动，指定 `dev` 环境配置：

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

服务启动后，你可以访问 [http://localhost:18095](http://localhost:18095) 来检查服务状态。

## 4. 项目结构 (Project Structure)

**多模块结构说明**：
项目采用Maven多模块架构，包含4个核心子模块：base基础模块提供公共定义，sdk模块实现核心业务逻辑，server模块提供联机服务，batch模块处理批量任务。

**各模块职责划分**：
base模块负责公共基础组件和业务模型定义，sdk模块实现29个核心清算服务，server模块提供RESTful API接口，batch模块执行大批量清算任务处理。

**包结构说明**：
业务包遵循com.anytech.anytxn.settlement.{domain}命名规范，清算分析服务按卡组织划分，文件处理服务按业务流程组织。

**关键目录介绍**：

```
.
├── anytxn-settlement-base/          # 基础模块 - 公共定义
│   ├── src/main/java/
│   │   └── com.anytech.anytxn.settlement.base/
│   │       ├── constants/           # 常量定义 (18个类)
│   │       ├── domain/              # 领域模型 (55个类)
│   │       ├── enums/               # 枚举定义 (20个类)
│   │       ├── exception/           # 异常定义 (2个类)
│   │       ├── service/             # 服务接口 (20个接口)
│   │       └── utils/               # 工具类 (6个类)
├── anytxn-settlement-sdk/           # 业务实现模块 - 核心业务逻辑
│   ├── src/main/java/
│   │   └── com.anytech.anytxn.settlement/
│   │       ├── mapper/              # 数据访问层 (30个类)
│   │       └── service/             # 业务实现 (29个类)
├── anytxn-settlement-server/        # 服务启动模块 - Web服务入口
│   ├── src/main/java/
│   │   └── com.anytech.anytxn.settlement/
│   │       └── controller/          # 控制器 (2个类)
│   └── src/main/resources/
│       ├── application.yaml         # 应用配置
│       └── bootstrap.yml            # 启动配置
├── anytxn-settlement-batch/         # 批处理模块 - 批量任务处理
│   └── src/main/java/
│       └── com.anytech.anytxn.settlement.batch/
│           └── 批处理任务实现       # 50+个批处理作业
└── pom.xml                          # 父 POM
```

- `anytxn-settlement-base`: 提供清算业务的基础定义，包含18个常量类、55个领域模型、20个枚举类和20个服务接口。
- `anytxn-settlement-sdk`: 实现29个核心清算服务，包含清算分析服务、文件处理服务、匹配费用服务等。
- `anytxn-settlement-server`: 提供RESTful API接口，端口18095，支持清算数据查询和状态监控。
- `anytxn-settlement-batch`: 执行大批量清算任务，覆盖5大卡组织的批量处理流程。

## 5. 配置 (Configuration)

本项目使用 Nacos 作为配置中心。核心配置文件在 Nacos 中，`group` 为 `DEFAULT_GROUP`，`Data ID` 为 `anytxn-settlement-server.yaml`。

本地开发时，会优先加载 `src/main/resources/application.yaml` 中的配置。

**关键环境变量/配置项说明:**
| 变量名/属性名 | 示例值 | 描述 |
|---------------|--------|------|
| `server.port` | `18095` | 清算服务端口 |
| `NACOS_SERVER_ADDR` | `************:8848` | Nacos 服务器地址 |
| `spring.application.name` | `anytxn-settlement-server` | 服务名称 |
| `jetcache.statIntervalMinutes` | `15` | JetCache统计间隔 |
| `anytxn.shardingsphere.enabled` | `true` | 是否启用分库分表 |
| `anytxn.number.segmentEnable` | `true` | 是否启用分段号码 |
| `anytxn.number.tenantIds` | `6001,6002` | 租户ID配置 |

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建
mvn clean install

# 跳过测试构建
mvn clean install -DskipTests

# 构建并生成Docker镜像
mvn clean package docker:build
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包生成JAR文件
4. 使用Maven Assembly插件生成部署包
5. Docker镜像构建和推送至Harbor仓库
6. Kubernetes集群部署服务

**Docker部署方式**：
```bash
# 构建镜像
docker build -t anytxn-settlement-server:latest .

# 运行容器
docker run -d -p 18095:18095 \
  -e NACOS_SERVER_ADDR=************:8848 \
  -e SPRING_PROFILES_ACTIVE=dev \
  anytxn-settlement-server:latest
```

**环境变量配置**：
- **镜像仓库**: k8s.jrx.com/anytxn/anytxn-settlement-server
- **Kubernetes命名空间**: anytxn-namespace
- **服务端口**: 18095
- **健康检查**: /actuator/health

## 7. 监控与告警 (Monitoring & Alerting)

- **健康检查**: Spring Boot Actuator 提供了健康检查端点。
  - `http://<host>:18095/actuator/health`
- **Metrics**: 指标已接入 Prometheus。端点地址：
  - `http://<host>:18095/actuator/prometheus`
- **清算业务监控**: 提供清算交易处理量、匹配成功率、异常交易数量等关键业务指标监控。
- **批处理任务监控**: 监控批处理任务执行状态、处理效率、错误率等关键指标。
- **缓存监控**: JetCache提供缓存命中率、缓存大小、缓存刷新等性能监控指标。
- **日志**: 应用日志格式为 JSON，统一由 Plumelog 分布式日志系统收集。你可以在日志平台中查看。
  - **清算处理日志**: 记录清算数据处理过程和结果
  - **匹配日志**: 记录授权-清算交易匹配详情
  - **异常日志**: 记录拒绝交易和处理异常信息