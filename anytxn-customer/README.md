# AnyTXN信用卡核心系统-客户服务 (AnyTXN Customer Service)

本项目是 AnyTXN信用卡核心系统 的客户服务，负责处理客户授权、客户基础信息管理、企业客户信息管理等核心业务。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-客户服务 (AnyTXN Customer Service)](#anytxn信用卡核心系统-客户服务-anytxn-customer-service)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build \& Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring \& Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的客户服务是企业级微服务应用，在整个AnyTXN生态系统中负责客户信息管理和授权处理，为卡片服务、账户服务、授权服务、交易服务等其他业务模块提供客户数据基础支撑。该服务解决了客户基础信息统一管理、企业客户信息维护、客户授权信息查询、持卡人身份验证、客户经理关系管理等关键业务问题。

主要功能包括：客户基础信息管理和查询、企业客户注册信息维护、客户授权信息处理、持卡人身份验证服务、企业担保人和抵押人信息管理、客户经理关系维护、开卡附属客户服务、维护日志记录和查询、封锁码维护日志管理，支持多租户数据隔离和分布式事务处理。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于API接口和服务实现分析，anytxn-customer工程提供以下六大核心功能模块：

#### 1.1.1 客户授权信息管理功能 (Customer Authorization Management)

**核心API接口：**
- `GET /cardholder/authorinfos/{idType}/{idNumber}` - 根据证件类型和证件号查询客户授权信息
- `GET /cardholder/epcc/authorinfos/{idType}/{idNumber}` - EPCC专用客户授权信息查询
- `GET /cardholder/authorinfos/old/{idType}/{oldIdNumber}` - 根据旧证件号查询客户授权信息
- `GET /cardholder/authorinfosById/{organizationNumber}/{customerId}` - 根据机构号和客户ID查询授权信息
- `GET /cardholder/authorinfos/{customerId}` - 根据客户ID查询授权信息
- `POST /cardholder/authorinfos` - 添加客户授权信息
- `PUT /cardholder/authorinfos` - 更新客户授权信息  
- `DELETE /cardholder/authorinfos/{id}` - 删除客户授权信息

**客户搜索查询API：**
- `GET /cardholder/search` - 根据姓名和生日分页搜索客户信息
- `GET /cardholder/selEmail` - 根据邮件地址查询客户信息
- `GET /cardholder/queryCustomerInfo/orgNumber/{orgNumber}/mobile/{mobile}` - 根据手机号查询客户信息
- `GET /cardholder/getCustomerCollectInfo/{customerId}` - 查询客户汇总信息
- `GET /cardholder/checkECM` - NAS在线申请客户存在性检查

**核心业务能力：**
- 提供完整的客户授权信息生命周期管理（增删改查）
- 支持多种证件类型的客户身份验证和查询
- 实现客户信息的多维度搜索（姓名、生日、邮箱、手机号）
- 支持新旧证件号的关联查询，满足证件变更场景
- 提供客户汇总信息查询，整合基本信息、授权信息和地址信息
- 支持EPCC等特殊渠道的客户信息查询接口

#### 1.1.2 客户基础信息管理功能 (Customer Basic Information Management)

**客户基本信息API：**
- `GET /cardholder/basicinfos/{organizationNumber}/{customerId}` - 查询客户基本信息
- `POST /cardholder/basicinfos` - 添加客户基本信息
- `PUT /cardholder/basicinfos` - 更新客户基本信息

**客户地址信息API：**
- `GET /cardholder/addressinfos/{organizationNumber}/{customerId}` - 查询客户地址信息列表
- `GET /cardholder/addressinfos/{id}` - 根据ID查询客户地址信息
- `GET /cardholder/addr/{organizationNumber}/{customerId}/{type}` - 根据地址类型查询客户地址
- `POST /cardholder/addressinfos` - 添加客户地址信息
- `PUT /cardholder/addressinfos` - 更新客户地址信息
- `DELETE /cardholder/addressinfos/{id}` - 删除客户地址信息

**客户联系人信息API：**
- `GET /cardholder/relationshipinfos/{organizationNumber}/{customerId}` - 查询客户联系人信息列表
- `GET /cardholder/relationshipinfos/{id}` - 根据ID查询客户联系人信息
- `POST /cardholder/relationinfos` - 添加客户联系人信息
- `PUT /cardholder/relationinfos` - 更新客户联系人信息
- `DELETE /cardholder/relationshipinfos/{id}` - 删除客户联系人信息

**客户附加信息API：**
- `GET /cardholder/additional/{organizationNumber}/{customerId}` - 查询客户附加信息
- `PUT /cardholder/additional` - 更新客户附加信息
- `GET /cardholder/foreigner/{organizationNumber}/{customerId}` - 查询客户非居民信息
- `PUT /cardholder/foreigner` - 更新客户非居民信息

**账单周期管理API：**
- `GET /cardholder/trialCycleDay/{cycleDay}/{customerId}` - 试算下次账单日期

**核心业务能力：**
- 提供完整的客户基础信息管理体系（个人信息、地址、联系人）
- 支持多地址类型管理（居住地址、工作地址、邮寄地址等）
- 实现客户联系人关系管理，支持紧急联系人、家庭成员等
- 提供客户附加信息和非居民信息的专项管理
- 支持账单周期的灵活配置和试算功能
- 实现客户信息的全生命周期维护和历史记录

#### 1.1.3 企业客户信息管理功能 (Corporate Customer Information Management)

**企业客户基础管理API：**
- `POST /cardholder/corporateCustomerInfo` - 添加企业客户基本信息
- `PUT /cardholder/corporateCustomerInfo` - 更新企业客户信息
- `DELETE /cardholder/corporateCustomerInfo/{id}` - 删除企业客户节点
- `GET /cardholder/corpCusInfosById/{corporateCustomerId}` - 根据ID查询企业客户信息

**企业客户结构管理API：**
- `POST /cardholder/corporateCustomerInfoList` - 企业客户结构管理首页分页查询
- `GET /cardholder/corporateCustomerInfoList/{corporateCustomerId}` - 查询企业客户下级结构
- `GET /cardholder/corporateCustomerInfoTree/{corporateCustomerId}` - 企业客户结构树查询

**企业客户转移管理API：**
- `GET /cardholder/corporateCustomerInfo/{sourceCorporateCustomerId}/{targetCorporateCustomerId}` - 部门转移
- `GET /cardholder/corporateCustomerInfoPerson/{accountId}/{targetCorporateCustomerId}` - 个人部门转移

**IF申请信息管理API：**
- `GET /cardholder/ifApplicationInfo/{customerId}` - 查询IF申请信息
- `PUT /cardholder/ifApplicationInfo/{customerId}` - 更新IF申请信息

**企业担保和抵押信息API：**
- `GET /cardholder/guarantorInfos/{cardNumber}` - 根据卡号查询担保人信息
- `GET /cardholder/mortgagorInfos/{cardNumber}` - 根据卡号查询抵押人信息

**核心业务能力：**
- 提供完整的企业客户层级结构管理
- 支持企业客户的树形组织架构维护和查询
- 实现部门转移和人员调动的灵活管理
- 提供企业担保人和抵押人信息的专项管理
- 支持IF（Industrial Finance）申请信息的处理
- 实现企业客户的分级授权和权限控制

#### 1.1.4 企业注册信息管理功能 (Corporate Registration Information Management)

**企业注册信息API：**
- `POST /cardholder/corporateRegistrationInfo` - 添加企业注册信息
- `PUT /cardholder/corporateRegistrationInfo` - 更新企业注册信息
- `DELETE /cardholder/corporateRegistrationInfo/{corporateRegistrationId}` - 删除企业注册信息
- `GET /cardholder/corporateRegistrationInfo/{organizationNumber}/{socialCreditCode}` - 根据统一社会信用代码查询注册信息
- `GET /cardholder/corpRegInfosById/{corporateRegistrationId}` - 根据注册ID查询企业注册信息

**核心业务能力：**
- 提供企业工商注册信息的完整管理
- 支持统一社会信用代码的查询和验证
- 实现企业法人信息、注册资本等关键数据维护
- 提供企业注册信息的增删改查全功能支持
- 支持企业合规性检查和信息核验

#### 1.1.5 客户经理关系管理功能 (Customer Manager Relationship Management)

**VIP客户经理管理API：**
- `POST /cardholder/addCustomerManagerInfo` - 添加VIP客户经理信息
- `PUT /cardholder/modifyCustomerManagerInfo` - 修改VIP客户经理信息
- `DELETE /cardholder/removeCustomerManagerInfo/customerManagerIdNo/{customerManagerIdNo}` - 删除VIP客户经理信息
- `GET /cardholder/queryCustomerManagerInfo/orgNumber/{orgNumber}/customerId/{customerId}` - 查询VIP客户经理信息
- `GET /cardholder/queryCustomerManagerInfo/orgNumber/{orgNumber}/mobile/{mobile}` - 根据手机号查询客户信息

**核心业务能力：**
- 提供VIP客户和客户经理的关系管理
- 支持客户经理信息的维护和查询
- 实现基于手机号的客户经理快速查找
- 提供客户经理绩效和服务质量跟踪基础
- 支持多机构的客户经理关系管理

#### 1.1.6 维护日志与系统管理功能 (Maintenance Log & System Management)

**维护日志管理API：**
- `POST /cardholder/addMaintenanceLog` - 添加维护日志信息
- `POST /cardholder/queryMaintenanceLog` - 分页查询维护日志
- `POST /cardholder/addSysCode` - 添加系统码信息

**封锁码维护日志API：**
- `GET /cardholder/blockcodemaintenancelog/{key}/{type}/{blockCodeDateAfter}/{pageNum}/{pageSize}` - 根据关键字和类型查询封锁码维护日志

**开卡客户服务API：**
- `cardOpenSubsidiaryCustomer()` - 开卡相关客户信息同步
- `getOrganization()` - 获取机构信息

**核心业务能力：**
- 提供完整的系统维护日志记录和查询功能
- 支持封锁码维护操作的审计跟踪
- 实现开卡流程中的客户信息同步服务
- 提供系统代码和参数的维护管理
- 支持操作日志的分页查询和历史追溯
- 实现跨系统的客户信息一致性保障

**技术特色：**
- **微服务架构**：标准的Spring Boot微服务架构，支持独立部署和扩展
- **多维度查询**：支持基于证件号、手机号、邮箱、姓名等多种方式的客户查询
- **层级管理**：完善的企业客户层级结构管理，支持树形组织架构
- **审计追踪**：全面的维护日志和操作记录，确保业务操作的可追溯性
- **数据一致性**：跨系统的客户信息同步机制，保障数据一致性
- **灵活扩展**：基于接口的设计模式，支持业务功能的灵活扩展和定制

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：MySQL 8.3.0, Redis（通过Redisson 3.41.0集成）

**中间件**：Nacos（服务发现与配置中心）

**构建工具**：Maven 3.5

**JDK版本要求**：Java 17

**其他核心组件**：
- MyBatis 3.5.16（数据持久化）
- MyBatis Spring Boot 3.0.4
- Undertow（Web服务器）
- ShardingSphere 5.5.0（分库分表）
- Druid 1.2.24（数据库连接池）
- Jasypt 3.0.5（参数加解密）
- OpenFeign（服务间调用）
- Plumelog 3.1.0-SNAPSHOT（分布式日志）
- Micrometer + Prometheus（监控）

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**Docker**：Docker & Docker Compose

**中间件要求**：
- Nacos Server
- MySQL 8.3.0+
- Redis 6.2+

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-customer.git
cd anytxn-customer
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：配置 IntelliJ IDEA 启动参数**

在 IntelliJ IDEA 中配置应用启动的程序参数：

1. **打开运行配置**
   - 在 IDEA 中找到主启动类（如 `CustomerServerApplication.java`）
   - 右键点击该文件，选择 `Run` 或 `Debug`
   - 或者点击 IDEA 顶部的运行配置下拉菜单

2. **编辑运行配置**
   - 点击运行配置旁的编辑按钮（齿轮图标）
   - 或者选择 `Run` → `Edit Configurations...`

3. **配置程序参数**
   - 在 `Program arguments` 字段中输入以下参数：
   ```
   --NACOS_USERNAME=nacos --NACOS_PASSWORD=nacos --CLUSTER_NAME=A --NACOS_SERVER_ADDR=**********:8848,**********:8848,**********:8848 --NACOS_NAMESPACE=2cff4a1a-e0f9-42e4-8472-a7b0a17a1235
   ```
   
   > **注意**：请根据实际环境情况调整以下参数值：
   > - `NACOS_USERNAME`: Nacos 用户名
   > - `NACOS_PASSWORD`: Nacos 密码  
   > - `CLUSTER_NAME`: 集群名称
   > - `NACOS_SERVER_ADDR`: Nacos 服务器地址列表
   > - `NACOS_NAMESPACE`: Nacos 命名空间ID

4. **保存配置**
   - 点击 `Apply` 和 `OK` 保存配置

**第五步：运行应用**

我们推荐在 IntelliJ IDEA 中直接运行 `AnyTxnCardholderServerApplication.java`。

或者通过命令行启动，指定 `dev` 环境配置：

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

服务启动后，你可以访问 [http://localhost:18085](http://localhost:18085) 来检查服务状态。

## 4. 项目结构 (Project Structure)

**多模块结构说明**：
项目采用Maven多模块架构，按照业务功能和技术层次进行模块划分，实现清晰的依赖关系和职责分离。

**各模块职责划分**：
基础定义模块提供API接口和基础组件，业务实现模块包含核心逻辑，服务启动模块负责应用启动，客户端模块提供Feign接口。

**包结构说明**：
各模块遵循标准包命名规范，基础包名为com.anytech.anytxn.customer.base，业务实现包名为com.anytech.anytxn.customer，客户端包名为com.anytech.anytxn.customer.client。

**关键目录介绍**：

```
.
├── anytxn-customer-base             # 基础定义模块 (可被其他服务依赖)
│   └── com.anytech.anytxn.customer.base
│       ├── api/                     # API接口定义
│       ├── domain/dto/              # 数据传输对象 (10个DTO)
│       ├── enums/                   # 枚举类定义 (6个枚举)
│       ├── exception/               # 异常定义
│       ├── service/                 # 服务接口定义 (12个服务接口)
│       └── util/                    # 工具类 (4个工具类)
├── anytxn-customer-sdk              # 业务实现模块 (核心业务逻辑)
│   └── com.anytech.anytxn.customer
│       ├── EnableCardHolderApi.java # 服务启用注解
│       ├── controller/              # REST控制器 (8个Controller)
│       ├── feign/                   # Feign客户端
│       ├── impl/                    # 核心服务实现 (10个服务实现)
│       └── service/                 # 其他服务实现
├── anytxn-customer-server           # 服务启动模块
│   └── com.anytech.anytxn.customer.server
│       ├── AnyTxnCardholderServerApplication.java   # 主启动类
│       ├── config/                  # 服务器配置
│       └── application.yaml         # 配置文件
├── anytxn-customer-client           # 客户端模块 (Feign接口定义)
│   └── com.anytech.anytxn.customer.client
├── doc/                             # 项目文档
└── pom.xml                          # 父 POM
```

- `anytxn-customer-base`: 定义了对外暴露的API接口、DTO和业务常量，可以被其他微服务依赖，避免了代码重复。
- `anytxn-customer-sdk`: 包含所有业务逻辑、Controller、Service等，是核心业务实现模块。
- `anytxn-customer-server`: 微服务启动入口，包含Spring Boot应用配置。
- `anytxn-customer-client`: 定义了Feign客户端接口，供其他服务调用客户功能。

## 5. 配置 (Configuration)

本项目使用 Nacos 作为配置中心。核心配置文件在 Nacos 中，`group` 为 `DEFAULT_GROUP`，`Data ID` 为 `anytxn-customer-server.yaml`。

本地开发时，会优先加载 `src/main/resources/application.yaml` 中的配置。

**关键环境变量/配置项说明:**
| 变量名/属性名 | 示例值 | 描述 |
|---------------|--------|------|
| `NACOS_SERVER_ADDR` | `************:8848` | Nacos 服务器地址 |
| `SPRING_DATASOURCE_URL` | `*******************************************` | 数据库连接地址 |
| `REDIS_HOST` | `localhost` | Redis 主机名 |
| `SERVER_PORT` | `18085` | 服务端口号 |
| `SHARDING_CONFIG_DATAID` | `sharding-config-wjc-accouuntate.yaml` | 分库分表配置ID |

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建
mvn clean install

# 跳过测试构建
mvn clean install -DskipTests

# 单模块构建
mvn clean install -pl anytxn-customer-server -am
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包
4. Docker镜像构建
5. 推送至Harbor镜像仓库
6. Kubernetes集群部署

**Docker部署方式**：
```bash
# 构建Docker镜像
mvn clean package docker:build

# 运行Docker容器
docker run -p 18085:18085 -e SPRING_PROFILES_ACTIVE=dev anytxn-customer-server:1.0.2-SNAPSHOT
```

**环境变量配置**：
- `SPRING_PROFILES_ACTIVE`: 指定运行环境（dev/sit/prod）
- `NACOS_SERVER_ADDR`: Nacos服务地址
- `MYSQL_HOST`: MySQL数据库主机
- `REDIS_HOST`: Redis缓存主机

## 7. 监控与告警 (Monitoring & Alerting)

- **健康检查**: Spring Boot Actuator 提供了健康检查端点。
  - `http://localhost:18085/actuator/health`
- **Metrics**: 指标已接入 Prometheus。端点地址：
  - `http://localhost:18085/actuator/prometheus`
- **日志**: 应用日志格式为 JSON，统一由 Plumelog 收集并发送到 Elasticsearch。你可以在 Kibana 中查看。
  - **Kibana Dashboard**: [链接到 Kibana 仪表盘](https://kibana.jrx.com)