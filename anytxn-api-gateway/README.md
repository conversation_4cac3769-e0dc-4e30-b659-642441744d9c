# AnyTXN信用卡核心系统-API网关服务 (AnyTXN API Gateway Service)

本项目是 AnyTXN信用卡核心系统 的API网关服务，负责统一的路由管理、认证授权、请求转发和数据分片路由等核心业务。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-API网关服务 (AnyTXN API Gateway Service)](#anytxn信用卡核心系统-api网关服务-anytxn-api-gateway-service)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build \& Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring \& Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的API网关服务是企业级微服务网关应用，在整个AnyTXN生态系统中负责流量入口控制和路由分发，为卡片服务、账户服务、交易服务等其他业务模块提供统一的API网关基础能力。该服务解决了统一路由管理、身份认证授权、智能负载均衡、多租户数据分片路由、灰度发布管理等关键业务问题。

主要功能包括：动态路由配置管理、Sa-Token身份认证授权、智能路由映射和数据分片、请求加解密处理、灰度负载均衡、安全白名单管理、Feign客户端服务调用，支持响应式编程模式和多环境配置隔离。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于API接口和网关过滤器分析，anytxn-api-gateway工程提供以下六大核心功能模块：

#### 1.1.1 动态路由管理功能 (Dynamic Route Management)

**核心配置类：**
- `DynamicRouteConfig` - 启动时从Nacos配置中心加载路由配置并监听变化
- `DynamicRouteServiceImpl` - 动态路由服务实现，提供路由的增删改查功能
- `IDynamicRouteService` - 动态路由服务接口定义

**核心实现机制：**
- 通过Nacos配置中心存储路由配置（`group=DEFAULT_GROUP`, `dataId=gateway-router.json`）
- 支持路由配置的实时热更新，无需重启网关服务
- 基于Spring Cloud Gateway的`RouteDefinition`进行路由定义和管理
- 实现路由配置的监听器模式，自动响应配置变更

**核心业务能力：**
- 支持基于路径、请求头、请求参数等多种条件的路由匹配
- 提供负载均衡、重试、熔断等路由增强功能
- 支持多环境路由配置隔离和版本管理

#### 1.1.2 身份认证授权功能 (Authentication & Authorization)

**核心过滤器：**
- `AuthFilter` - Sa-Token全局认证过滤器，拦截`/app/api/**`路径
- `ForwardAuthFilter` - 请求转发认证过滤器
- `IgnoreWhiteProperties` - 认证白名单配置

**主要功能特性：**
- 基于Sa-Token 1.41.0框架实现统一身份认证
- 支持Token验证、登录状态检查、权限校验
- 提供认证白名单机制，支持特定路径跳过认证
- 集成响应式编程模式，适配Spring WebFlux

**核心业务能力：**
- 统一的用户身份验证和会话管理
- 细粒度的权限控制和访问控制
- 支持移动端APP和Web端的多端认证

#### 1.1.3 多租户数据分片路由功能 (Multi-Tenant Data Sharding & Routing)

**核心API接口：**
- `GET /mapping/getDbIndex` - 根据客户号查询数据库分片索引
- `GET /mapping/getCidByRouteMap` - 根据路由键查询对应的客户号
- `GET /mapping/getCidAndShardIndex` - 获取客户号和分片索引信息
- `GET /mapping/getDbIndexByOrgNum` - 根据机构号查询分片索引列表

**核心过滤器：**
- `MappingFilter` - 数据分片路由过滤器，实现智能路由分发
- `CustIdMappingFeign` - 映射服务Feign客户端，提供分片查询功能

**分片路由策略：**
- **申卡类请求**：通过`/anytxn/v2/api/mapping/getDefaultAppIndex`获取默认应用群索引
- **标准API请求**：基于机构号进行分片路由
- **授权交易请求**：基于客户号、卡号、管理账户ID等多种路由键进行分片

**核心业务能力：**
- 支持按客户、机构、业务类型的多维度数据分片
- 实现数据库分片索引(dbIndex)和应用集群索引(appIndex)的智能路由
- 支持多租户数据隔离和跨分片数据访问
- 提供路由失败的容错和降级机制

#### 1.1.4 灰度发布与负载均衡功能 (Gray Release & Load Balancing)

**核心负载均衡器：**
- `GrayLoadBalancer` - 自定义灰度负载均衡器，支持版本路由和集群路由
- `GrayReactiveLoadBalancerClientFilter` - 响应式负载均衡客户端过滤器

**灰度路由策略：**
- **应用集群路由**：基于请求头`SHARD_APP_INDEX`和服务实例的`nacos.cluster`元数据进行匹配
- **版本路由**：支持基于请求头`version`进行版本化路由（可扩展）
- **标签路由**：支持基于环境标签进行服务实例筛选

**负载均衡算法：**
- 集成Nacos权重随机算法`NacosBalancer.getHostByRandomWeight3()`
- 支持服务实例的权重配置和动态调整
- 提供服务实例健康检查和故障转移

**核心业务能力：**
- 支持蓝绿部署、金丝雀发布等多种发布策略
- 实现基于业务特征的智能路由和流量分配
- 提供服务实例的动态发现和负载均衡

#### 1.1.5 请求加解密处理功能 (Request Encryption & Decryption)

**核心加解密组件：**
- `DecryptFilter` - 请求解密过滤器，处理敏感数据的解密
- `ResponseFilter` - 响应过滤器，处理返回数据的加密
- `DecryptionConstant` - 加解密相关常量定义
- `ModifyBodyResponseDecorator` - 响应体修改装饰器

**加解密策略：**
- 支持请求参数和请求体的自动解密处理
- 支持响应数据的选择性加密返回
- 集成多种加密算法（如SM4、AES等）
- 支持加解密规则的配置化管理

**核心业务能力：**
- 保障敏感数据在传输过程中的安全性
- 支持字段级别的精细化加解密控制
- 提供加解密性能优化和缓存机制

#### 1.1.6 对外服务调用功能 (External Service Integration)

**Feign客户端接口：**
- `CustIdMappingFeign` - 客户ID映射服务Feign客户端，连接`anytxn-mapping-server`服务

**服务调用能力：**
- 提供标准化的Feign客户端接口供其他微服务依赖使用
- 支持服务发现、负载均衡、熔断、重试等微服务治理功能
- 集成Spring Cloud OpenFeign的完整功能特性

**集成特性：**
- **多租户支持**：通过`TENANT_ID`请求头传递租户信息
- **分片路由**：基于映射服务实现数据分片的智能路由
- **配置管理**：支持通过配置文件灵活配置服务名称和路径
- **容错机制**：提供服务调用失败的降级和容错处理

**技术特色：**
- **响应式架构**：基于Spring WebFlux实现非阻塞响应式处理
- **云原生设计**：完全基于Spring Cloud Gateway构建，支持容器化部署
- **高可用保障**：支持多实例部署、健康检查、故障转移等高可用机制
- **监控告警**：集成Actuator、Prometheus等监控组件，提供完整的可观测性
- **配置中心化**：所有配置通过Nacos统一管理，支持动态配置更新
- **多环境支持**：通过环境变量和配置文件支持dev/sit/prod等多环境部署

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：Redis（通过spring-boot-starter-data-redis集成）

**中间件**：Nacos（服务发现与配置中心）

**构建工具**：Maven 3.5

**JDK版本要求**：Java 17

**其他核心组件**：
- Spring Cloud Gateway（网关核心）
- Spring Cloud LoadBalancer（负载均衡）
- OpenFeign（服务间调用）
- Sa-Token 1.41.0（认证授权框架）
- Hutool 5.8.31（工具库）
- Commons Pool2（连接池）
- Docker Maven Plugin（容器化部署）

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**Docker**：Docker & Docker Compose

**中间件要求**：
- Nacos Server
- Redis 6.2+

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-api-gateway.git
cd anytxn-api-gateway
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：配置 IntelliJ IDEA 启动参数**

在 IntelliJ IDEA 中配置应用启动的程序参数：

1. **打开运行配置**
   - 在 IDEA 中找到主启动类（如 `GatewayServerApplication.java`）
   - 右键点击该文件，选择 `Run` 或 `Debug`
   - 或者点击 IDEA 顶部的运行配置下拉菜单

2. **编辑运行配置**
   - 点击运行配置旁的编辑按钮（齿轮图标）
   - 或者选择 `Run` → `Edit Configurations...`

3. **配置程序参数**
   - 在 `Program arguments` 字段中输入以下参数：
   ```
   --NACOS_USERNAME=nacos --NACOS_PASSWORD=nacos --CLUSTER_NAME=A --NACOS_SERVER_ADDR=**********:8848,**********:8848,**********:8848 --NACOS_NAMESPACE=2cff4a1a-e0f9-42e4-8472-a7b0a17a1235
   ```
   
   > **注意**：请根据实际环境情况调整以下参数值：
   > - `NACOS_USERNAME`: Nacos 用户名
   > - `NACOS_PASSWORD`: Nacos 密码  
   > - `CLUSTER_NAME`: 集群名称
   > - `NACOS_SERVER_ADDR`: Nacos 服务器地址列表
   > - `NACOS_NAMESPACE`: Nacos 命名空间ID

4. **保存配置**
   - 点击 `Apply` 和 `OK` 保存配置

**第五步：运行应用**

我们推荐在 IntelliJ IDEA 中直接运行 `GatewayServerApplication.java`。

或者通过命令行启动，指定 `dev` 环境配置：

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

服务启动后，你可以访问 [http://localhost:18084](http://localhost:18084) 来检查服务状态。

## 4. 项目结构 (Project Structure)

**多模块结构说明**：
项目采用Maven多模块架构，按照业务功能和技术层次进行模块划分，实现清晰的依赖关系和职责分离。

**各模块职责划分**：
客户端模块提供Feign接口定义，服务端模块包含核心网关逻辑和路由配置。

**包结构说明**：
各模块遵循标准包命名规范，client模块包名为com.anytech.anytxn.gateway.client，server模块包名为com.anytech.anytxn.gateway.server。

**关键目录介绍**：

```
.
├── anytxn-api-gateway-client    # 客户端模块 (可被其他服务依赖)
│   └── com.anytech.anytxn.gateway.client
│       └── CustIdMappingFeign.java      # Feign客户端接口
├── anytxn-api-gateway-server    # 服务端模块
│   └── com.anytech.anytxn.gateway.server
│       ├── GatewayServerApplication.java    # 主启动类
│       ├── config/                          # 配置类目录
│       ├── constants/                       # 常量定义目录
│       ├── filter/                          # 过滤器目录
│       ├── service/                         # 服务接口目录
│       └── utils/                           # 工具类目录
├── doc                          # 项目文档
└── pom.xml                      # 父 POM
```

- `anytxn-api-gateway-client`: 定义了对外暴露的Feign客户端接口，可以被其他微服务依赖，避免了代码重复。
- `anytxn-api-gateway-server`: 包含所有网关业务逻辑、过滤器、配置等，是核心网关实现模块。

## 5. 配置 (Configuration)

本项目使用 Nacos 作为配置中心。核心配置文件在 Nacos 中，`group` 为 `DEFAULT_GROUP`，`Data ID` 为 `gateway-router.json`。

本地开发时，会优先加载 `src/main/resources/application.yaml` 中的配置。

**关键环境变量/配置项说明:**
| 变量名/属性名 | 示例值 | 描述 |
|---------------|--------|------|
| `NACOS_SERVER_ADDR` | `************:8848` | Nacos 服务器地址 |
| `SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE` | `4a3c117e-3efb-4acc-b05b-0c0bf8e6acd6` | Nacos 命名空间ID |
| `REDIS_HOST` | `localhost` | Redis 主机名 |
| `SERVER_PORT` | `18084` | 服务端口号 |

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建
mvn clean install

# 跳过测试构建
mvn clean install -DskipTests

# 单模块构建
mvn clean install -pl anytxn-api-gateway-server -am
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包
4. Docker镜像构建
5. 推送至Harbor镜像仓库
6. Kubernetes集群部署

**Docker部署方式**：
```bash
# 构建Docker镜像
mvn clean package docker:build

# 运行Docker容器
docker run -p 18084:18084 -e SPRING_PROFILES_ACTIVE=dev k8s.jrx.com/multi-tenant-sgb/anytxn-api-gateway-server:1.0.2-SNAPSHOT
```

**环境变量配置**：
- `SPRING_PROFILES_ACTIVE`: 指定运行环境（dev/sit/prod）
- `NACOS_SERVER_ADDR`: Nacos服务地址
- `REDIS_HOST`: Redis缓存主机

## 7. 监控与告警 (Monitoring & Alerting)

- **健康检查**: Spring Boot Actuator 提供了健康检查端点。
  - `http://localhost:18084/actuator/health`
- **Metrics**: 指标已接入 Prometheus。端点地址：
  - `http://localhost:18084/actuator/prometheus`
- **日志**: 应用日志格式为 JSON，统一由 Plumelog 收集并发送到 Elasticsearch。你可以在 Kibana 中查看。
  - **Kibana Dashboard**: [链接到 Kibana 仪表盘](https://kibana.jrx.com)