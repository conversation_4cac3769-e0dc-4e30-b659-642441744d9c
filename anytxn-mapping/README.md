# AnyTXN信用卡核心系统-客户号映射服务 (AnyTXN Mapping Service)

本项目是 AnyTXN信用卡核心系统 的客户号映射服务，负责处理客户标识与客户号的映射关系管理、数据分片路由等核心业务。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-客户号映射服务 (AnyTXN Mapping Service)](#anytxn信用卡核心系统-客户号映射服务-anytxn-mapping-service)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build \& Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring \& Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的客户号映射服务是企业级微服务应用，在整个AnyTXN生态系统中负责客户标识与客户号的双向映射管理，为所有业务模块提供统一的客户标识路由和数据分片计算基础能力。该服务解决了多种身份标识类型统一管理、客户数据精准路由、分布式数据访问优化、多级缓存性能提升等关键业务问题。

主要功能包括：支持9种身份标识类型的客户号映射（身份证号、银行卡号、手机号、ECIF号等）、基于一致性哈希算法的数据分片路由计算、Redis+本地缓存的多级缓存策略、客户数据动态分群和租户管理、分布式锁防止缓存击穿、异步消息处理批量映射操作，支持高并发访问和动态扩容需求。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于API接口和批量作业分析，anytxn-mapping工程提供以下五大核心功能模块：

#### 1.1.1 客户标识映射管理功能 (Customer Identity Mapping Management)

**核心API接口：**
- `GET /mapping/getCidByOrgNumAndRouteMap` - 根据机构号和路由键获取客户ID，支持9种身份标识类型查询
- `GET /mapping/getShardValueByOrgNumAndRouteMap` - 根据机构号和路由键获取数据分片号，提供分片路由信息
- `POST /mapping/saveMapping` - 保存映射关系接口，支持批量映射数据写入
- `DELETE /mapping/deleteMapping` - 删除映射关系接口，支持映射数据清理

**主要服务类：**
- `IIdentityMappingService` - 身份映射服务接口，定义核心映射功能规范
- `GeneralIdentityMappingService` - 通用身份映射服务，统一管理Redis和数据库映射查询
- `DbIdentityMappingService` - 数据库身份映射服务，处理数据库层面的映射操作
- `RedisIdentityMappingService` - Redis身份映射服务，处理缓存层面的映射查询
- `CustomerIdShardingHelper` - 客户ID分片助手，计算客户数据分片路由

**批量作业：**
- `mappingDataSyncJob` - 映射数据同步作业，批量同步映射关系到缓存
- `mappingDataCleanupJob` - 映射数据清理作业，清理过期和无效的映射数据
- `mappingConsistencyCheckJob` - 映射一致性检查作业，验证数据库和缓存映射一致性

#### 1.1.2 数据分片路由计算功能 (Data Sharding Route Calculation)

**核心API接口：**
- `GET /mapping/getCidAndShardIndex` - 获取客户号和分片索引信息，提供完整的路由信息
- `GET /mapping/getAppIndexByTenantId` - 通过租户获取所有应用分群号，支持多租户路由
- `GET /mapping/getDefaultAppIndex` - 获取默认应用索引，提供默认路由策略

**主要服务类：**
- `IGetCidAndShardService` - 客户号和分片服务接口，定义分片路由核心功能
- `CustomerIdShardingHelper` - 客户ID分片助手，基于雪花算法实现动态分片计算
- `ShardingAlgorithm` - 分片算法实现，提供一致性哈希分片策略
- `ShardingPropsConfig` - 分片属性配置，管理分片规则和配置参数
- `GetCidAndShardServiceImpl` - 客户号和分片服务实现，处理具体的分片路由逻辑

**批量作业：**
- `shardingRuleUpdateJob` - 分片规则更新作业，动态更新分片配置
- `shardingDataMigrationJob` - 分片数据迁移作业，支持分片扩容时的数据迁移
- `shardingPerformanceOptimizationJob` - 分片性能优化作业，优化分片路由性能

#### 1.1.3 BusinessToken管理功能 (BusinessToken Management)

**核心API接口：**
- `POST /mapping/getBusinessToken/cardToken` - 根据cardToken查询businessToken
- `POST /mapping/getBusinessToken/customerToken` - 根据customerToken查询businessToken
- `POST /mapping/getBusinessToken/accountToken` - 根据accountToken查询businessToken
- `GET /mapping/getBusinessToken/cardNumber` - 根据卡号查询businessToken

**主要服务类：**
- `IBusinessTokenService` - BusinessToken服务接口，定义Token管理核心功能
- `BusinessTokenServiceImpl` - BusinessToken服务实现，处理Token的增删改查操作
- `MappingBusinessTokenMapper` - BusinessToken数据访问层，提供数据库操作支持
- `BusinessTokenValidator` - BusinessToken校验器，验证Token的有效性和格式
- `BusinessTokenGenerator` - BusinessToken生成器，生成安全的业务Token

**批量作业：**
- `businessTokenRefreshJob` - BusinessToken刷新作业，定期刷新过期Token
- `businessTokenCleanupJob` - BusinessToken清理作业，清理无效和过期Token
- `businessTokenSyncJob` - BusinessToken同步作业，同步Token数据到缓存

#### 1.1.4 多级缓存管理功能 (Multi-Level Cache Management)

**核心API接口：**
- `GET /mapping/cache/status` - 缓存状态查询接口，监控缓存运行状态
- `POST /mapping/cache/refresh` - 缓存刷新接口，手动刷新指定缓存数据
- `DELETE /mapping/cache/clear` - 缓存清理接口，清理指定的缓存数据

**主要服务类：**
- `RedisIdentityMappingService` - Redis映射服务，管理Redis缓存层映射数据
- `CacheManager` - 缓存管理器，统一管理多级缓存策略
- `RedisEnabledConfig` - Redis启用配置，控制Redis缓存的启用和配置
- `CacheKeyGenerator` - 缓存键生成器，生成标准化的缓存键
- `CacheEvictionPolicy` - 缓存淘汰策略，管理缓存数据的生命周期

**批量作业：**
- `cacheWarmupJob` - 缓存预热作业，预加载热点映射数据到缓存
- `cacheEvictionJob` - 缓存淘汰作业，清理过期和低频访问的缓存数据
- `cacheConsistencyJob` - 缓存一致性作业，保证缓存与数据库数据一致性

#### 1.1.5 附属卡关系管理功能 (Sub-Card Relationship Management)

**核心API接口：**
- `GET /mapping/getSubBankCardListBySubCid` - 根据附属客户ID查询附属卡列表
- `POST /mapping/saveSubCardMapping` - 保存附属卡映射关系
- `GET /mapping/getMainCardBySubCard` - 根据附属卡查询主卡信息
- `DELETE /mapping/deleteSubCardMapping` - 删除附属卡映射关系

**主要服务类：**
- `SubCardMappingService` - 附属卡映射服务，管理主附卡关系映射
- `CardRelationshipManager` - 卡片关系管理器，处理复杂的卡片关系逻辑
- `SubCardValidator` - 附属卡校验器，验证附属卡关系的有效性
- `CardHierarchyService` - 卡片层级服务，管理卡片的层级关系结构
- `SubCardQueryService` - 附属卡查询服务，提供高效的附属卡查询功能

**批量作业：**
- `subCardRelationSyncJob` - 附属卡关系同步作业，同步主附卡关系数据
- `subCardDataCleanupJob` - 附属卡数据清理作业，清理无效的附属卡关系
- `subCardRelationValidationJob` - 附属卡关系验证作业，验证主附卡关系的完整性

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：MySQL 8.3.0, Redis（通过Redisson 3.37.0集成）

**中间件**：Nacos（服务发现与配置中心）, ShardingSphere 5.5.0（分库分表）

**构建工具**：Maven 3.5

**JDK版本要求**：Java 17

**其他核心组件**：
- MyBatis 3.5.16（数据持久化）
- MyBatis Spring Boot 3.0.4
- Undertow（Web服务器）
- Druid 1.2.24（数据库连接池）
- JetCache 2.5.16（多级缓存）
- Jasypt 3.0.5（参数加解密）
- Knife4j 4.5.0（API文档生成）
- Aviator 4.2.9（表达式引擎）
- Plumelog 3.1.0-SNAPSHOT（分布式日志）
- Micrometer + Prometheus（监控）

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**Docker**：Docker & Docker Compose

**中间件要求**：
- Nacos Server
- MySQL 8.3.0+
- Redis 6.2+

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-mapping.git
cd anytxn-mapping
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：配置 IntelliJ IDEA 启动参数**

在 IntelliJ IDEA 中配置应用启动的程序参数：

1. **打开运行配置**
   - 在 IDEA 中找到主启动类（如 `MappingServerApplication.java`）
   - 右键点击该文件，选择 `Run` 或 `Debug`
   - 或者点击 IDEA 顶部的运行配置下拉菜单

2. **编辑运行配置**
   - 点击运行配置旁的编辑按钮（齿轮图标）
   - 或者选择 `Run` → `Edit Configurations...`

3. **配置程序参数**
   - 在 `Program arguments` 字段中输入以下参数：
   ```
   --NACOS_USERNAME=nacos --NACOS_PASSWORD=nacos --CLUSTER_NAME=A --NACOS_SERVER_ADDR=**********:8848,**********:8848,**********:8848 --NACOS_NAMESPACE=2cff4a1a-e0f9-42e4-8472-a7b0a17a1235
   ```
   
   > **注意**：请根据实际环境情况调整以下参数值：
   > - `NACOS_USERNAME`: Nacos 用户名
   > - `NACOS_PASSWORD`: Nacos 密码  
   > - `CLUSTER_NAME`: 集群名称
   > - `NACOS_SERVER_ADDR`: Nacos 服务器地址列表
   > - `NACOS_NAMESPACE`: Nacos 命名空间ID

4. **保存配置**
   - 点击 `Apply` 和 `OK` 保存配置

**第五步：运行应用**

我们推荐在 IntelliJ IDEA 中直接运行 `MappingServerApplication.java`。

或者通过命令行启动，指定 `dev` 环境配置：

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

服务启动后，你可以访问 [http://localhost:18092](http://localhost:18092) 来检查服务状态。

## 4. 项目结构 (Project Structure)

**多模块结构说明**：
项目采用Maven多模块架构，按照业务功能和技术层次进行模块划分，实现清晰的依赖关系和职责分离。

**各模块职责划分**：
基础定义模块提供API接口和基础组件，业务实现模块包含核心逻辑，服务启动模块负责应用启动，批量处理模块处理定时任务。

**包结构说明**：
各模块遵循标准包命名规范，基础包名为com.anytech.anytxn.mapping.base，业务实现包名为com.anytech.anytxn.mapping。

**关键目录介绍**：

```
.
├── anytxn-mapping-base              # 基础定义模块 (可被其他服务依赖)
│   └── com.anytech.anytxn.mapping.base
│       ├── constants/               # 常量定义
│       ├── domain/                  # 领域对象
│       │   ├── dto/                 # 数据传输对象
│       │   └── model/               # 实体模型
│       ├── enums/                   # 枚举类（身份类型等）
│       ├── exception/               # 异常类
│       ├── service/                 # 基础服务接口
│       └── utils/                   # 工具类（分片算法）
├── anytxn-mapping-sdk               # 业务实现模块 (核心业务逻辑)
│   └── com.anytech.anytxn.mapping
│       ├── config/                  # 配置类（Redis、分片配置）
│       ├── controller/              # REST控制器
│       ├── mapper/                  # 数据访问层
│       ├── mq/                      # 消息队列
│       └── service/                 # 业务服务实现
├── anytxn-mapping-server            # 服务启动模块
│   └── com.anytech.anytxn.mapping.server
│       ├── MappingServerApplication.java   # 主启动类
│       └── application.yml          # 配置文件
├── anytxn-mapping-batch             # 批量处理模块 (定时任务)
│   └── com.anytech.anytxn.mapping.batch
│       └── config/                  # 批处理配置
└── pom.xml                          # 父 POM
```

- `anytxn-mapping-base`: 定义了对外暴露的API接口、DTO和业务常量，可以被其他微服务依赖，避免了代码重复。
- `anytxn-mapping-sdk`: 包含所有业务逻辑、Controller、Service等，是核心业务实现模块。
- `anytxn-mapping-server`: 微服务启动入口，包含Spring Boot应用配置。
- `anytxn-mapping-batch`: 批量处理模块，包含定时任务和批量映射操作实现。

## 5. 配置 (Configuration)

本项目使用 Nacos 作为配置中心。核心配置文件在 Nacos 中，`group` 为 `DEFAULT_GROUP`，`Data ID` 为 `anytxn-mapping-server.yaml`。

本地开发时，会优先加载 `src/main/resources/application.yml` 中的配置。

**关键环境变量/配置项说明:**
| 变量名/属性名 | 示例值 | 描述 |
|---------------|--------|------|
| `NACOS_SERVER_ADDR` | `************:8848` | Nacos 服务器地址 |
| `SPRING_DATASOURCE_URL` | `******************************************` | 数据库连接地址 |
| `REDIS_HOST` | `localhost` | Redis 主机名 |
| `SERVER_PORT` | `18092` | 服务端口号 |
| `SHARDING_CONFIG_DATAID` | `sharding-config-mapping.yaml` | 分库分表配置ID |
| `ANYTXN_NUMBER_TENANTIDS` | `6001,6002` | 多租户ID配置 |

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建
mvn clean install

# 跳过测试构建
mvn clean install -DskipTests

# 单模块构建
mvn clean install -pl anytxn-mapping-server -am
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包
4. Docker镜像构建
5. 推送至Harbor镜像仓库
6. Kubernetes集群部署

**Docker部署方式**：
```bash
# 构建Docker镜像
mvn clean package docker:build

# 运行Docker容器
docker run -p 18092:18092 -e SPRING_PROFILES_ACTIVE=dev anytxn-mapping-server:1.0.2-SNAPSHOT
```

**环境变量配置**：
- `SPRING_PROFILES_ACTIVE`: 指定运行环境（dev/sit/prod）
- `NACOS_SERVER_ADDR`: Nacos服务地址
- `MYSQL_HOST`: MySQL数据库主机
- `REDIS_HOST`: Redis缓存主机

## 7. 监控与告警 (Monitoring & Alerting)

- **健康检查**: Spring Boot Actuator 提供了健康检查端点。
  - `http://localhost:18092/actuator/health`
- **Metrics**: 指标已接入 Prometheus。端点地址：
  - `http://localhost:18092/actuator/prometheus`
- **日志**: 应用日志格式为 JSON，统一由 Plumelog 收集并发送到 Elasticsearch。你可以在 Kibana 中查看。
  - **Kibana Dashboard**: [链接到 Kibana 仪表盘](https://kibana.jrx.com)