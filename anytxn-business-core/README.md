# AnyTXN信用卡核心系统-业务核心组件 (AnyTXN Business Core)

本项目是 AnyTXN信用卡核心系统 的业务核心组件，负责提供统一的业务实体定义、数据访问层、业务服务等核心功能。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-业务核心组件 (AnyTXN Business Core)](#anytxn信用卡核心系统-业务核心组件-anytxn-business-core)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build \& Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring \& Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的业务核心组件是企业级微服务基础组件库，在整个AnyTXN生态系统中负责提供统一的业务实体定义、数据访问层接口、业务服务实现等基础能力，为卡片服务、账户服务、授权服务、交易服务等其他业务模块提供标准化的业务组件基础支撑。该组件解决了业务实体标准化定义、数据访问层统一封装、业务常量和枚举管理、服务接口统一规范等关键技术问题。

主要功能包括：完整的业务实体DTO/BO定义（362个基础文件）、标准化的数据访问Mapper接口（602个Mapper文件）、核心业务服务实现（14个ServiceImpl类）、丰富的业务枚举和常量定义（48个授权相关枚举）、Feign客户端接口定义、自动配置和服务启用注解，支持微服务架构的模块化集成和标准化开发。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于服务接口、数据访问层和业务实现分析，anytxn-business-core工程提供以下六大核心功能模块：

#### 1.1.1 业务实体标准化定义功能 (Business Entity Standardization)

**核心业务领域：**
- `account` - 账户相关实体定义，包含24个DTO、3个Model、8个枚举、6个服务接口
- `card` - 卡片相关实体定义，包含40+个DTO、7个枚举、5个服务接口
- `authorization` - 授权相关实体定义，包含18个DTO、48个枚举、2个服务接口
- `accounting` - 会计相关实体定义，包含6个DTO、5个枚举
- `customer` - 客户相关实体定义，包含7个枚举、1个服务接口
- `monetary` - 货币金额相关实体定义，包含6个注解、4个枚举、异常类和工具类

**主要DTO定义：**
- `AccountManagementInfoDTO` - 账户管理信息DTO
- `CardBasicInfoDTO` - 卡片基本信息DTO
- `AuthorizationLogDTO` - 授权日志DTO
- `AccountBalanceInfoDTO` - 账户余额信息DTO
- `CardAcctCustReleationDTO` - 卡账户客户关系DTO

**核心业务能力：**
- 提供完整的信用卡核心业务实体定义标准
- 支持多业务领域的DTO/BO/Model统一规范
- 实现业务枚举和常量的标准化管理
- 提供统一的业务异常和响应码定义

#### 1.1.2 数据访问层统一封装功能 (Data Access Layer Standardization)

**数据访问覆盖范围：**
- `account/mapper` - 账户相关数据访问，包含30+个Mapper接口
- `card/mapper` - 卡片相关数据访问，包含120+个Mapper接口  
- `accounting/mapper` - 会计相关数据访问，包含20+个Mapper接口
- `authorization/mapper` - 授权相关数据访问，包含多个核心Mapper
- `customer/mapper` - 客户相关数据访问
- `transaction/mapper` - 交易相关数据访问

**核心Mapper接口：**
- `AccountManagementInfoMapper/SelfMapper` - 账户管理信息数据访问
- `CardBasicInfoMapper/SelfMapper` - 卡片基本信息数据访问
- `CardAcctCustReleationMapper/SelfMapper` - 卡账户客户关系数据访问
- `AccountBalanceInfoMapper/SelfMapper` - 账户余额信息数据访问
- `AuthorizationLogMapper/SelfMapper` - 授权日志数据访问

**技术特色：**
- 支持标准MyBatis Mapper和自定义SelfMapper双重数据访问模式
- 提供统一的SQL Provider动态SQL生成机制
- 支持批量操作和复杂查询条件封装
- 实现分库分表和多租户数据隔离支持

#### 1.1.3 核心业务服务实现功能 (Core Business Service Implementation)

**账户业务服务：**
- `AccountCommonServiceImpl` - 账户通用服务实现，提供账户和统计信息创建功能
- `CommonAccountService` - 公共账户服务，提供账户相关的通用业务逻辑

**卡片业务服务：**
- `CardAcctCustReleationServiceImpl` - 卡账户客户关系服务实现，提供卡片、账户、客户三者关系管理
- `CardCustSpecialInfoServiceImpl` - 卡片客户特殊信息服务实现
- `CardManageFeeRecordServiceImpl` - 卡片管理费记录服务实现
- `CardMcAbuLogServiceImpl` - MasterCard滥用日志服务实现
- `CardMdesNotificationServiceImpl` - MDES通知服务实现

**其他业务服务：**
- `ExceptionRecordServiceImpl` - 异常记录服务实现
- `AccountAbsStautsServiceImpl` - 账户摘要状态服务实现
- `MaintenanceLogBisServiceImpl` - 维护日志业务服务实现

**核心业务能力：**
- 提供完整的卡片账户客户关系管理功能
- 支持账户创建、查询、更新等全生命周期管理
- 实现卡片相关的特殊业务处理和费用管理
- 提供异常处理和日志记录等基础支撑服务

#### 1.1.4 微服务间通信接口功能 (Microservice Communication Interface)

**Feign客户端接口：**
- `MappingFeign` - 映射服务Feign客户端，提供数据分片路由和客户ID映射功能
- `MappingFeignFallBack` - 映射服务降级处理
- `CardHolderFeignFallBack` - 持卡人服务降级处理

**映射服务API：**
- `GET /mapping/getCidByOrgNumAndRouteMap` - 根据机构号和路由键获取客户ID
- `POST /mapping/saveMapping` - 保存映射关系
- `GET /mapping/getShardValueByOrgNumAndRouteMap` - 获取数据分片号
- `DELETE /mapping/deleteMapping` - 删除映射关系
- `GET /mapping/getSubBankCardListBySubCid` - 查询附属卡列表
- `GET /mapping/getBusinessToken/cardNumber` - 根据卡号查询BusinessToken

**核心业务能力：**
- 支持微服务间的标准化调用和降级处理
- 提供数据分片路由和客户ID映射服务
- 实现BusinessToken管理和卡号映射功能
- 支持附属卡和主附卡关系管理

#### 1.1.5 高性能数据处理功能 (High-Performance Data Processing)

**JDBC批量处理服务：**
- `ICustAccountJdbcService` - 客户账户JDBC服务接口，提供批量插入、更新和通用批量操作
- `CusAccountCommonServiceImpl` - 客户账户通用服务实现
- `CustAccountWriterService` - 客户账户写入服务
- `JdbcServiceProxy` - JDBC服务代理

**批量处理注解：**
- `@BatchSharedAnnotation` - 批量共享注解
- `@InsertCacheAnnotation` - 插入缓存注解
- `@UpdateCacheAnnotation` - 更新缓存注解
- `@DeleteCacheAnnotation` - 删除缓存注解

**核心业务能力：**
- 支持大批量数据的高效插入和更新操作
- 提供缓存管理和数据同步机制
- 实现JDBC层面的性能优化和批量处理
- 支持分布式数据处理和事务管理

#### 1.1.6 自动配置与集成功能 (Auto Configuration & Integration)

**自动配置注解：**
- `@EnableBisCoreService` - 启用业务核心服务层
- `@EnableBisCoreDao` - 启用业务核心数据访问层
- `@EnableCmCoreDao` - 启用客户管理数据访问层

**集成支持服务：**
- `PartitionKeyInitService` - 分区键初始化服务
- `SharedInfoFindServiceImpl` - 共享信息查找服务实现
- `CustReconciliationControlServiceImpl` - 客户对账控制服务实现

**核心业务能力：**
- 支持Spring Boot自动配置和组件扫描
- 提供微服务架构下的标准化集成方式
- 实现分区键管理和数据分片支持
- 支持多数据源和事务管理配置

**技术特色：**
- **标准化基础组件**：提供完整的信用卡核心业务基础组件库
- **高度可复用性**：所有DTO、服务接口和实现都可被多个微服务复用
- **统一数据访问**：602个Mapper接口提供统一的数据访问标准
- **微服务友好**：完整支持Spring Cloud微服务架构和自动配置
- **高性能处理**：提供JDBC批量处理和缓存管理机制
- **业务领域完整**：覆盖账户、卡片、授权、会计、客户等所有核心业务领域

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：MySQL 8.3.0（通过MyBatis 3.5.16集成）

**中间件**：Nacos（服务发现与配置中心）

**构建工具**：Maven 3.5

**JDK版本要求**：Java 17

**其他核心组件**：
- MyBatis 3.5.16（数据持久化）
- MyBatis Spring Boot 3.0.4
- Spring Cloud OpenFeign（服务间调用）
- Apache Commons Collections4（集合工具）
- Knife4j（API文档生成）
- Spring Boot Starter Web（Web支持）
- Spring Boot Starter Test（测试支持）

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**数据库要求**：
- MySQL 8.3.0+

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-business-core.git
cd anytxn-business-core
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：在其他项目中使用**

本项目为业务核心组件库，需要在其他微服务项目中引入依赖使用：

```xml
<!-- 引入业务核心SDK -->
<dependency>
    <groupId>com.anytech</groupId>
    <artifactId>anytxn-business-core-sdk</artifactId>
    <version>1.0.2-SNAPSHOT</version>
</dependency>

<!-- 或单独引入数据访问层 -->
<dependency>
    <groupId>com.anytech</groupId>
    <artifactId>anytxn-business-core-dao</artifactId>
    <version>1.0.2-SNAPSHOT</version>
</dependency>
```

在主应用类上添加启用注解：

```java
@EnableBisCoreService  // 启用业务核心服务
@EnableBisCoreDao      // 启用业务核心数据访问
```

## 4. 项目结构 (Project Structure)

**多模块结构说明**：
项目采用Maven多模块架构，按照业务功能和技术层次进行模块划分，实现清晰的依赖关系和职责分离。

**各模块职责划分**：
基础定义模块提供完整的业务实体和接口定义，数据访问层模块包含所有Mapper接口，服务实现模块提供核心业务逻辑，客户端模块提供Feign接口定义。

**包结构说明**：
各模块遵循标准包命名规范，基础包名为com.anytech.anytxn.business.base，数据访问包名为com.anytech.anytxn.business.dao，服务实现包名为com.anytech.anytxn.business。

**关键目录介绍**：

```
.
├── anytxn-business-core-base        # 基础定义模块 (362个Java文件)
│   └── com.anytech.anytxn.business.base
│       ├── account/                 # 账户相关 (常量、DTO、枚举、服务接口)
│       ├── accounting/              # 会计核算相关
│       ├── authorization/           # 授权相关 (48个枚举类)
│       ├── card/                    # 卡片相关
│       ├── customer/                # 客户相关
│       ├── installment/             # 分期相关
│       ├── limit/                   # 额度相关
│       ├── mapping/                 # 映射相关
│       ├── monetary/                # 货币金额相关
│       ├── settlement/              # 清算相关
│       └── transaction/             # 交易相关
├── anytxn-business-core-dao         # 数据访问层模块 (602个Java文件)
│   └── com.anytech.anytxn.business.dao
│       ├── account/mapper/          # 账户相关Mapper (30+个)
│       ├── accounting/mapper/       # 会计Mapper (20+个)
│       ├── authorization/mapper/    # 授权Mapper
│       ├── card/mapper/             # 卡片Mapper (120+个)
│       ├── customer/mapper/         # 客户Mapper
│       ├── installment/mapper/      # 分期Mapper
│       ├── limit/mapper/            # 额度Mapper
│       ├── monetary/mapper/         # 货币Mapper
│       ├── settlement/mapper/       # 清算Mapper
│       └── transaction/mapper/      # 交易Mapper
├── anytxn-business-core-sdk         # 业务服务实现模块 (55个Java文件)
│   └── com.anytech.anytxn.business
│       ├── account/service/         # 账户服务实现
│       ├── accounting/service/      # 会计服务实现
│       ├── authorization/service/   # 授权服务实现
│       ├── card/service/            # 卡片服务实现 (5个ServiceImpl)
│       ├── customer/service/        # 客户服务实现
│       ├── installment/service/     # 分期服务实现
│       ├── monetary/service/        # 货币服务实现
│       └── EnableBisCoreService.java # 服务启用配置
├── anytxn-business-core-client      # 客户端模块 (3个Java文件)
│   └── com.anytech.anytxn.business.client
│       └── Feign客户端接口和降级处理
├── doc                              # 项目文档
└── pom.xml                          # 父 POM
```

- `anytxn-business-core-base`: 定义了完整的业务实体、DTO、枚举、常量和服务接口，可以被其他微服务依赖，提供标准化的业务定义。
- `anytxn-business-core-dao`: 包含所有数据访问层Mapper接口，提供统一的数据库操作能力。
- `anytxn-business-core-sdk`: 包含核心业务逻辑Service实现，提供可复用的业务服务。
- `anytxn-business-core-client`: 定义了Feign客户端接口，供微服务间调用使用。

## 5. 配置 (Configuration)

本项目为业务核心组件库，主要通过自动配置注解进行集成。在使用该组件的项目中需要配置相关数据源和包扫描。

**关键配置项说明:**
| 配置项 | 示例值 | 描述 |
|--------|--------|------|
| `@EnableBisCoreService` | 注解 | 启用业务核心服务层 |
| `@EnableBisCoreDao` | 注解 | 启用业务核心数据访问层 |
| `@EnableCmCoreDao` | 注解 | 启用客户管理数据访问 |
| 包扫描路径 | `com.anytech.anytxn.business` | 业务组件包路径 |

**数据源配置示例:**
```yaml
spring:
  datasource:
    business:
      url: *****************************************
      username: sit_biz2_rd
      password: jrx1234
    common:
      url: *******************************************
      username: sit_common_rd
      password: jrx1234
```

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建
mvn clean install

# 跳过测试构建
mvn clean install -DskipTests

# 单模块构建
mvn clean install -pl anytxn-business-core-sdk -am
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包
4. 发布到Maven私有仓库
5. 其他项目通过依赖引入使用

**Maven仓库发布**：
```bash
# 发布到私有仓库
mvn clean deploy

# 安装到本地仓库
mvn clean install
```

**依赖使用**：
其他项目在pom.xml中添加依赖：
```xml
<dependency>
    <groupId>com.anytech</groupId>
    <artifactId>anytxn-business-core-sdk</artifactId>
    <version>1.0.2-SNAPSHOT</version>
</dependency>
```

## 7. 监控与告警 (Monitoring & Alerting)

- **构建状态**: Maven构建状态通过Jenkins Pipeline监控。
- **依赖检查**: 定期检查依赖项的安全漏洞和版本更新。
- **代码质量**: 通过SonarQube进行代码质量分析。
- **使用统计**: 监控组件在各微服务中的使用情况和性能表现。