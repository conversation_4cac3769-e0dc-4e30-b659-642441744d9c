# AnyTXN信用卡核心系统-额度管理服务 (AnyTXN Limit Service)

本项目是 AnyTXN信用卡核心系统 的额度管理服务，负责处理客户额度管理、额度使用控制、额度变更历史、额度试算等核心业务。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-额度管理服务 (AnyTXN Limit Service)](#anytxn信用卡核心系统-额度管理服务-anytxn-limit-service)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build \& Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring \& Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的额度管理服务是企业级微服务应用，在整个AnyTXN生态系统中负责客户额度计算、校验、管理等核心业务处理，为卡片服务、账户服务、授权服务、交易服务等其他业务模块提供额度管理基础能力。该服务解决了客户额度信息统一管理、额度使用实时控制、额度变更历史追踪、额度试算模拟、合作方保证金管理等关键业务问题。

主要功能包括：客户额度信息查询和更新（单个/批量）、额度占用释放校验等交易时额度控制、额度变更记录查询和历史追踪、额度计算模拟和试算服务、合作伙伴保证金同步和查询、客户额度批处理作业、额度调整批处理作业、额度授信批处理作业，支持分布式事务处理和多租户数据隔离。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于API接口和批量作业分析，anytxn-limit工程提供以下六大核心功能模块：

#### 1.1.1 客户额度管理功能 (Customer Limit Management)

**核心API接口：**
- `GET /limit/customer-limit/{customerId}` - 客户指定额度信息查询接口，支持按客户号查询额度详情
- `PUT /limit/customer-limit` - 客户额度更新接口，支持单个客户额度信息修改
- `PUT /limit/customer-limit/batch` - 客户额度批量更新接口，支持批量修改多个客户额度
- `POST /limit/new-customer-limit` - 新建客户额度接口，创建新客户的授信额度信息

**主要服务类：**
- `CustomerLimitService` - 客户额度服务，处理客户额度查询、创建、修改等核心业务逻辑
- `CustomerLimitUpdateService` - 客户额度更新服务，处理额度计算和更新操作
- `CustomerLimitInfoService` - 客户额度信息服务，管理客户额度基础信息维护
- `LimitCustUsedInfoService` - 客户用信信息服务，管理客户额度使用情况
- `LimitCustCreditInfoService` - 客户授信信息服务，处理客户授信额度管理

**批量作业：**
- `limitCustBatchJob` - 客户额度批处理作业，批量处理客户额度数据同步和更新
- `limitCustStep` - 客户额度处理步骤，执行客户额度数据的读取、处理和写入
- `limitCustProcessor` - 客户额度处理器，处理客户额度业务逻辑计算

#### 1.1.2 额度使用控制功能 (Limit Usage Control)

**核心API接口：**
- `POST /limit/customer-limitUsed/{customerId}` - 客户用信查询接口，查询客户额度使用情况
- `POST /limit/limit-match/test` - 额度匹配测试接口，测试额度匹配规则和控制逻辑
- `POST /limit/limit-trial-cal/test` - 额度试算测试接口，测试额度计算和控制规则

**主要服务类：**
- `LimitMatchCheckService` - 额度匹配检查服务，处理额度匹配和检查核心逻辑
- `LimitPreOccupiedService` - 额度预占服务，处理额度预占用和释放操作
- `LimitExchangeManager` - 额度交换管理器，管理额度使用的交换和控制
- `CtrlLimitUnitExecutor` - 管控单元执行器，执行额度管控单元的检查规则
- `CtrlLimitUnitMatcher` - 管控单元匹配器，匹配适用的额度管控单元

**批量作业：**
- `limitUsageControlJob` - 额度使用控制作业，批量检查和控制额度使用情况
- `limitValidationJob` - 额度校验作业，批量执行额度有效性检查
- `limitControlRuleJob` - 额度控制规则作业，批量执行额度控制规则处理

#### 1.1.3 额度试算与计算功能 (Limit Trial Calculation)

**核心API接口：**
- `POST /limit/limit-trial-cal` - 额度试算接口，提供额度计算模拟和试算服务
- `GET /limit/available-limit/{customerId}` - 可用额度查询接口，查询客户实时可用额度
- `POST /limit/limit-calculation` - 额度计算接口，执行复杂的额度计算逻辑

**主要服务类：**
- `CustomerLimitService.tryToCalculateLimit()` - 额度试算方法，提供额度计算试算功能
- `CustomerLimitUpdateService.calculateLimit()` - 额度计算方法，执行额度更新计算逻辑
- `LimitCalculationEngine` - 额度计算引擎，处理复杂的额度计算规则
- `LimitTrialService` - 额度试算服务，提供各种额度试算场景支持
- `AvailableLimitCalculator` - 可用额度计算器，计算客户实时可用额度

**批量作业：**
- `limitCalculationJob` - 额度计算作业，批量执行额度重新计算
- `limitTrialBatchJob` - 额度试算批处理作业，批量执行额度试算任务
- `availableLimitUpdateJob` - 可用额度更新作业，批量更新客户可用额度

#### 1.1.4 额度变更历史管理功能 (Limit Change History Management)

**核心API接口：**
- `GET /limit/limit-history-summary/list` - 分页查询授信历史概要信息接口
- `GET /limit/limit-history-detail/{orgNum}/{cusId}/{bizTraceNum}` - 查询授信历史明细信息接口
- `GET /limit/limit-change-log/{customerId}` - 额度变更日志查询接口

**主要服务类：**
- `LimitChangeHistorySummaryService` - 额度变更历史概要服务，管理额度变更历史概要信息
- `LimitChangeHistoryDetailService` - 额度变更历史明细服务，管理额度变更历史详细记录
- `LimitChangeHistorySummaryServiceImpl` - 额度变更历史概要服务实现，提供分页查询功能
- `LimitChangeHistoryDetailServiceImpl` - 额度变更历史明细服务实现，提供明细查询功能
- `LimitSynRequestLogService` - 额度同步请求日志服务，记录额度同步操作历史

**批量作业：**
- `limitHistoryArchiveJob` - 额度历史归档作业，批量归档历史额度变更记录
- `limitChangeLogJob` - 额度变更日志作业，批量处理额度变更日志记录
- `limitHistoryCleanupJob` - 额度历史清理作业，清理过期的额度变更历史数据

#### 1.1.5 合作方保证金管理功能 (Partner Margin Management)

**核心API接口：**
- `GET /limit/margin/balance/{partnerId}/{currency}` - 合作商保证金查询接口，查询合作方保证金余额
- `POST /limit/margin/update` - 合作商保证金更新接口，更新合作方保证金信息
- `GET /limit/margin/history/{partnerId}` - 合作商保证金历史查询接口

**主要服务类：**
- `IPartnerMarginService` - 合作方保证金服务接口，定义保证金管理核心功能
- `PartnerMarginService` - 合作方保证金服务实现，处理保证金业务逻辑
- `PartnerMarginSyncService` - 合作方保证金同步服务，处理保证金数据同步
- `PartnerMarginCalculator` - 合作方保证金计算器，计算保证金相关金额
- `PartnerMarginValidator` - 合作方保证金校验器，校验保证金操作有效性

**批量作业：**
- `partnerMarginSyncJob` - 合作方保证金同步作业，批量同步合作方保证金数据
- `partnerMarginReconciliationJob` - 合作方保证金对账作业，执行保证金对账处理
- `partnerMarginReportJob` - 合作方保证金报表作业，生成保证金统计报表

#### 1.1.6 额度批处理管理功能 (Limit Batch Processing Management)

**核心API接口：**
- `POST /limit/batch/credit` - 额度授信批处理接口，批量处理客户授信业务
- `POST /limit/batch/adjust` - 额度调整批处理接口，批量处理额度调整业务
- `GET /limit/batch/status/{batchId}` - 批处理状态查询接口，查询批处理任务执行状态

**主要服务类：**
- `LimitBatchService` - 额度批处理服务，统一管理各类额度批处理任务
- `LimitCreditBatchService` - 额度授信批处理服务，处理批量授信业务
- `LimitAdjustBatchService` - 额度调整批处理服务，处理批量调整业务
- `BatchJobExecutor` - 批处理任务执行器，执行各类批处理任务
- `BatchStatusMonitor` - 批处理状态监控器，监控批处理任务执行状态

**批量作业：**
- `limitCreditJob` - 额度授信作业，批量处理客户额度授信业务
- `limitAdjustJob` - 额度调整作业，批量处理客户额度调整业务
- `limitCustBatchJob` - 客户额度批处理作业，批量处理客户额度相关业务
- `limitReconciliationJob` - 额度对账作业，执行额度数据对账和校验
- `limitDataSyncJob` - 额度数据同步作业，同步额度数据到其他系统

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：MySQL 8.3.0, Redis（通过Redisson 3.41.0集成）

**中间件**：Nacos（服务发现与配置中心）

**构建工具**：Maven 3.5+

**JDK版本要求**：Java 17

**其他核心组件**：
- MyBatis 3.5.16（数据持久化）
- MyBatis Spring Boot 3.0.4
- Undertow（Web服务器）
- ShardingSphere 5.5.0（分库分表）
- Druid 1.2.24（数据库连接池）
- Spring Batch（批量处理）
- Jasypt 3.0.5（参数加解密）
- Knife4j（API文档生成）
- Aviator（表达式引擎）
- Plumelog 3.1.0-SNAPSHOT（分布式日志）
- Micrometer + Prometheus（监控）

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**Docker**：Docker & Docker Compose

**中间件要求**：
- Nacos Server
- MySQL 8.3.0+
- Redis 6.2+

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-limit.git
cd anytxn-limit
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：配置 IntelliJ IDEA 启动参数**

在 IntelliJ IDEA 中配置应用启动的程序参数：

1. **打开运行配置**
   - 在 IDEA 中找到主启动类（如 `LimitServerApplication.java`）
   - 右键点击该文件，选择 `Run` 或 `Debug`
   - 或者点击 IDEA 顶部的运行配置下拉菜单

2. **编辑运行配置**
   - 点击运行配置旁的编辑按钮（齿轮图标）
   - 或者选择 `Run` → `Edit Configurations...`

3. **配置程序参数**
   - 在 `Program arguments` 字段中输入以下参数：
   ```
   --NACOS_USERNAME=nacos --NACOS_PASSWORD=nacos --CLUSTER_NAME=A --NACOS_SERVER_ADDR=**********:8848,**********:8848,**********:8848 --NACOS_NAMESPACE=2cff4a1a-e0f9-42e4-8472-a7b0a17a1235
   ```
   
   > **注意**：请根据实际环境情况调整以下参数值：
   > - `NACOS_USERNAME`: Nacos 用户名
   > - `NACOS_PASSWORD`: Nacos 密码  
   > - `CLUSTER_NAME`: 集群名称
   > - `NACOS_SERVER_ADDR`: Nacos 服务器地址列表
   > - `NACOS_NAMESPACE`: Nacos 命名空间ID

4. **保存配置**
   - 点击 `Apply` 和 `OK` 保存配置

**第五步：运行应用**

我们推荐在 IntelliJ IDEA 中直接运行 `LimitServerApplication.java`。

或者通过命令行启动，指定 `dev` 环境配置：

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

服务启动后，你可以访问 [http://localhost:18082](http://localhost:18082) 来检查服务状态。

## 4. 项目结构 (Project Structure)

**多模块结构说明**：
项目采用Maven多模块架构，按照业务功能和技术层次进行模块划分，实现清晰的依赖关系和职责分离。

**各模块职责划分**：
基础定义模块提供API接口和基础组件，业务实现模块包含核心逻辑，服务启动模块负责应用启动，批量处理模块处理定时任务。

**包结构说明**：
各模块遵循标准包命名规范，基础包名为com.anytech.anytxn.limit.base，业务实现包名为com.anytech.anytxn.limit。

**关键目录介绍**：

```
.
├── anytxn-limit-base                # 基础定义模块 (可被其他服务依赖)
│   └── com.anytech.anytxn.limit.base
│       ├── config/                  # 配置类
│       ├── constant/                # 常量定义
│       ├── domain/                  # 领域对象
│       │   ├── bo/                  # 业务对象 (9个)
│       │   ├── dto/                 # 数据传输对象 (26个)
│       │   └── model/               # 实体模型 (3个)
│       ├── enums/                   # 枚举类 (10个)
│       ├── exception/               # 异常类
│       └── service/                 # 基础服务接口 (4个)
├── anytxn-limit-sdk                 # 业务实现模块 (核心业务逻辑)
│   └── com.anytech.anytxn.limit
│       ├── config/                  # 配置类 (6个)
│       ├── controller/              # REST控制器 (8个)
│       ├── mapper/                  # 数据访问层 (8个Mapper)
│       ├── service/                 # 业务服务实现 (20个)
│       └── utlis/                   # 工具类
├── anytxn-limit-server              # 服务启动模块
│   └── com.anytech.anytxn.limit.server
│       ├── LimitServerApplication.java      # 主启动类
│       └── application.yaml         # 配置文件
├── anytxn-limit-batch               # 批量处理模块 (定时任务)
│   └── com.anytech.anytxn.limit.batch
│       ├── job/                     # 批处理任务
│       │   ├── limitCust/           # 客户额度批处理
│       │   ├── limitadjust/         # 额度调整批处理
│       │   └── limitcredit/         # 额度授信批处理
│       └── config/                  # 批处理配置
├── doc/                             # 项目文档
└── pom.xml                          # 父 POM
```

- `anytxn-limit-base`: 定义了对外暴露的API接口、DTO和业务常量，可以被其他微服务依赖，避免了代码重复。
- `anytxn-limit-sdk`: 包含所有业务逻辑、Controller、Service等，是核心业务实现模块。
- `anytxn-limit-server`: 微服务启动入口，包含Spring Boot应用配置。
- `anytxn-limit-batch`: 批量处理模块，包含定时任务和批量作业实现。

## 5. 配置 (Configuration)

本项目使用 Nacos 作为配置中心。核心配置文件在 Nacos 中，`group` 为 `DEFAULT_GROUP`，`Data ID` 为 `anytxn-limit-server.yaml`。

本地开发时，会优先加载 `src/main/resources/application.yaml` 中的配置。

**关键环境变量/配置项说明:**
| 变量名/属性名 | 示例值 | 描述 |
|---------------|--------|------|
| `NACOS_SERVER_ADDR` | `************:8848` | Nacos 服务器地址 |
| `SPRING_DATASOURCE_URL` | `****************************************` | 数据库连接地址 |
| `REDIS_HOST` | `localhost` | Redis 主机名 |
| `SERVER_PORT` | `18082` | 服务端口号 |
| `SHARDING_CONFIG_DATAID` | `sharding-config-wjc-accouuntate.yaml` | 分库分表配置ID |

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建
mvn clean install

# 跳过测试构建
mvn clean install -DskipTests

# 单模块构建
mvn clean install -pl anytxn-limit-server -am
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包
4. Docker镜像构建
5. 推送至Harbor镜像仓库
6. Kubernetes集群部署

**Docker部署方式**：
```bash
# 构建Docker镜像
mvn clean package docker:build

# 运行Docker容器
docker run -p 18082:18082 -e SPRING_PROFILES_ACTIVE=dev anytxn-limit-server:1.0.2-SNAPSHOT
```

**环境变量配置**：
- `SPRING_PROFILES_ACTIVE`: 指定运行环境（dev/sit/prod）
- `NACOS_SERVER_ADDR`: Nacos服务地址
- `MYSQL_HOST`: MySQL数据库主机
- `REDIS_HOST`: Redis缓存主机

## 7. 监控与告警 (Monitoring & Alerting)

- **健康检查**: Spring Boot Actuator 提供了健康检查端点。
  - `http://localhost:18082/actuator/health`
- **Metrics**: 指标已接入 Prometheus。端点地址：
  - `http://localhost:18082/actuator/prometheus`
- **日志**: 应用日志格式为 JSON，统一由 Plumelog 收集并发送到 Elasticsearch。你可以在 Kibana 中查看。
  - **Kibana Dashboard**: [链接到 Kibana 仪表盘](https://kibana.jrx.com)