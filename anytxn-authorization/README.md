# AnyTXN信用卡核心系统-授权服务 (AnyTXN Authorization Service)

本项目是 AnyTXN信用卡核心系统 的授权服务，负责处理信用卡授权、风险控制、交易验证等核心业务。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-授权服务 (AnyTXN Authorization Service)](#anytxn信用卡核心系统-授权服务-anytxn-authorization-service)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build \& Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring \& Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的授权服务是企业级微服务应用，在整个AnyTXN生态系统中负责信用卡交易授权和风险控制，为卡片服务、账户服务、交易服务等其他业务模块提供授权验证基础能力。该服务解决了多卡组织授权处理、实时风险控制、交易安全验证、欺诈检测、速度控制等关键业务问题。

主要功能包括：多卡组织授权支持（Visa、MasterCard、银联、JCB等）、实时风险控制和欺诈检测、交易速度控制和黑名单管理、CVV/PIN/3D Secure验证、预授权和正式授权处理、人工授权和未决交易管理、交易费用计算和批量处理作业，支持分布式事务处理和多租户数据隔离。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于API接口和批量作业分析，anytxn-authorization工程提供以下八大核心功能模块：

#### 1.1.1 多卡组织授权处理功能 (Multi-Card Organization Authorization)

**核心API接口：**
- `POST /auth/iso8583` - 银联ISO8583报文授权处理接口
- `POST /auth/dcipos` - DCI POS授权报文处理接口
- `POST /auth/dcidcs` - DCI DCS授权报文处理接口
- `POST /auth/epcc` - 网联非标ISO8583报文处理接口
- `POST /auth/jcb` - JCB卡授权报文处理接口
- `POST /auth/express` - Express卡授权报文处理接口

**主要服务接口：**
- `IAuthProcessService` - 核心授权处理服务，处理ISO8583报文
- `IVisaAuthProcessService` - Visa卡授权处理服务
- `IMastercAuthProcessService` - MasterCard授权处理服务
- `IUpiAuthProcessService` - 银联国际授权处理服务
- `IOnusAuthProcessService` - 本行卡授权处理服务
- `IJcbAuthProcessService` - JCB卡授权处理服务
- `IDciAuthProcessService` - DCI卡授权处理服务
- `IEpccAuthProcessService` - EPCC授权处理服务
- `IExpressAuthProcessService` - Express卡授权处理服务

**核心业务能力：**
- 支持主流国际卡组织的授权报文格式和业务规则
- 实现ISO8583报文的解析、验证和响应处理
- 支持不同卡组织的交易分类和路由策略
- 提供统一的授权检查和响应码映射机制

#### 1.1.2 授权检查与验证功能 (Authorization Check & Validation)

**核心API接口：**
- `POST /auth/authAssist` - 授权辅助功能（CVV/CVV2/PIN计算）
- `POST /auth/cardSafetyInfo` - 卡片安全信息获取接口
- `POST /auth/dcsCheckArqc` - DCS检验ARQC接口

**主要服务接口：**
- `IAuthCheckProcessService` - 授权检查处理服务，包含数据准备、授权检查、数据更新三个阶段
- `IAuthCommonHandlerService` - 授权通用处理服务，提供CVV、PIN验证等功能
- `IAuthCheckWayDetailService` - 授权检查方式详细服务
- `IFieldInAndOutProcessService` - 报文域进出处理服务
- `IHandlerAuthService` - 授权处理器服务

**核心业务能力：**
- 支持CVV、CVV2、PIN、3D Secure等多种验证方式
- 实现ARQC/ARPC密码验证和EMV交易授权
- 提供卡片有效性、账户状态、余额等基础检查
- 支持多层次的授权决策和风险评估

#### 1.1.3 风险控制与欺诈检测功能 (Risk Control & Fraud Detection)

**核心API接口：**
- `GET /auth/fraudcardinfos` - 分页查询欺诈卡信息
- `GET /auth/fraudcardinfo/{fraudCardNumber}` - 根据卡号查询欺诈卡信息
- `POST /auth/fraudcardinfo` - 新增欺诈卡信息
- `PUT /auth/fraudcardinfo` - 修改欺诈卡信息
- `DELETE /auth/fraudcardinfo/{id}` - 删除欺诈卡信息

**主要服务接口：**
- `IFraudCardInfoService` - 欺诈卡信息管理服务
- `ICustomerRiskCtrlService` - 客户风险控制服务
- `IMerchantBlackService` - 商户黑名单服务
- `ICardSafetyLockService` - 卡片安全锁服务

**核心业务能力：**
- 提供欺诈卡库的维护和实时查询功能
- 支持客户级别的风险控制规则设置
- 实现商户黑名单管理和风险商户识别
- 支持卡片安全锁功能，防止异常交易

#### 1.1.4 交易速度控制功能 (Transaction Velocity Control)

**核心API接口：**
- `POST /auth/cardVelocitys` - 查询个性流量信息
- `PUT /auth/cardVelocity` - 修改个性流量信息
- `GET /auth/cardVelocity/{cardNumber}` - 根据卡号查询个性流量信息

**主要服务接口：**
- `ICardVelocityService` - 卡片速度控制服务
- `ICardSpecialVelocityControlService` - 卡片特殊速度控制服务
- `ITransVelocityLogService` - 交易速度日志服务
- `ITransVelocityStatisticsService` - 交易速度统计服务

**核心业务能力：**
- 支持基于时间窗口的交易频次控制
- 实现个性化的速度控制规则设置
- 提供交易速度统计和监控功能
- 支持特殊场景的速度控制策略

#### 1.1.5 预授权管理功能 (Pre-Authorization Management)

**主要服务接口：**
- `IPreAuthorizationLogService` - 预授权日志服务
- `IOutstandingTransService` - 未决交易服务
- `ICancelReversalTransactionService` - 撤销冲正交易服务
- `IOriginTransMatchProcessService` - 原交易匹配处理服务

**核心业务能力：**
- 支持预授权交易的完整生命周期管理
- 实现预授权完成、撤销、冲正等操作
- 提供未决交易的查询和处理功能
- 支持原交易匹配和关联处理

#### 1.1.6 人工授权管理功能 (Manual Authorization Management)

**主要服务接口：**
- `IManualAuthorizationService` - 人工授权服务
- `IAuthCancleService` - 授权取消服务
- `IFallbackTradeInfoService` - 后备交易信息服务

**核心业务能力：**
- 支持复杂交易的人工审核和授权
- 提供授权决策的人工干预机制
- 实现后备交易处理和应急授权
- 支持授权取消和交易回退功能

#### 1.1.7 交易费用计算功能 (Transaction Fee Calculation)

**主要服务接口：**
- `IAuthTransactionFeeService` - 授权交易费用服务

**核心业务能力：**
- 支持多种费用计算规则和费率配置
- 实现交易费用的实时计算和扣收
- 提供费用明细查询和统计功能
- 支持不同卡组织的费用结算规则

#### 1.1.8 授权老化批量处理功能 (Authorization Aging Batch Processing)

**批量作业：**
- `authorizationAgingJob` - 授权老化处理批量作业，包含两个主要步骤：
  - `masterReleaseStep` - 未决交易释放处理步骤
  - `masterPreAuthStep` - 预授权过期处理步骤

**核心处理组件：**
- `ReleaseReader/Processor/Writer` - 未决交易释放处理链
- `PreAuthOverDueReader/Processor/Writer` - 预授权过期处理链
- `ReleasePartitioner` - 释放处理分区器
- `OverDuePartitioner` - 过期处理分区器

**核心业务能力：**
- 支持大批量未决交易的自动释放和清理
- 实现预授权交易的过期检查和处理
- 提供分区并行处理能力，提高批量处理效率
- 支持事务恢复和错误处理机制

**技术特色：**
- **多卡组织支持**：完整支持Visa、MasterCard、银联、JCB、DCI等主流卡组织
- **实时授权处理**：基于ISO8583标准的高性能实时授权处理能力
- **全面风险控制**：集成欺诈检测、速度控制、黑名单等多层次风险防控
- **EMV标准兼容**：完整支持EMV规范的ARQC/ARPC验证和密码学处理
- **分布式架构**：支持分区并行处理和分布式事务管理
- **监管合规**：严格按照各卡组织规范和金融监管要求实现授权流程

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：MySQL 8.3.0, Redis（通过Redisson 3.41.0集成）

**中间件**：Nacos（服务发现与配置中心）, RocketMQ

**构建工具**：Maven 3.5

**JDK版本要求**：Java 17

**其他核心组件**：
- MyBatis 3.5.16（数据持久化）
- MyBatis Spring Boot 3.0.4
- Spring Batch 5.x（批量处理）
- Undertow（Web服务器）
- ShardingSphere 5.5.0（分库分表）
- Druid 1.2.24（数据库连接池）
- Jasypt 3.0.5（参数加解密）
- JPOS 2.1.3（金融交易处理）
- Plumelog 3.1.0-SNAPSHOT（分布式日志）
- Micrometer + Prometheus（监控）

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**Docker**：Docker & Docker Compose

**中间件要求**：
- Nacos Server
- RocketMQ Server
- MySQL 8.3.0+
- Redis 6.2+

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-authorization.git
cd anytxn-authorization
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：配置 IntelliJ IDEA 启动参数**

在 IntelliJ IDEA 中配置应用启动的程序参数：

1. **打开运行配置**
   - 在 IDEA 中找到主启动类（如 `AuthorizationServerApplication.java`）
   - 右键点击该文件，选择 `Run` 或 `Debug`
   - 或者点击 IDEA 顶部的运行配置下拉菜单

2. **编辑运行配置**
   - 点击运行配置旁的编辑按钮（齿轮图标）
   - 或者选择 `Run` → `Edit Configurations...`

3. **配置程序参数**
   - 在 `Program arguments` 字段中输入以下参数：
   ```
   --NACOS_USERNAME=nacos --NACOS_PASSWORD=nacos --CLUSTER_NAME=A --NACOS_SERVER_ADDR=**********:8848,**********:8848,**********:8848 --NACOS_NAMESPACE=2cff4a1a-e0f9-42e4-8472-a7b0a17a1235
   ```
   
   > **注意**：请根据实际环境情况调整以下参数值：
   > - `NACOS_USERNAME`: Nacos 用户名
   > - `NACOS_PASSWORD`: Nacos 密码  
   > - `CLUSTER_NAME`: 集群名称
   > - `NACOS_SERVER_ADDR`: Nacos 服务器地址列表
   > - `NACOS_NAMESPACE`: Nacos 命名空间ID

4. **保存配置**
   - 点击 `Apply` 和 `OK` 保存配置

**第五步：运行应用**

我们推荐在 IntelliJ IDEA 中直接运行 `AuthorizationServerApplication.java`。

或者通过命令行启动，指定 `dev` 环境配置：

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

服务启动后，你可以访问 [http://localhost:18087](http://localhost:18087) 来检查服务状态。

## 4. 项目结构 (Project Structure)

**多模块结构说明**：
项目采用Maven多模块架构，按照业务功能和技术层次进行模块划分，实现清晰的依赖关系和职责分离。

**各模块职责划分**：
基础定义模块提供API接口和基础组件，客户端模块提供Feign接口，业务实现模块包含核心逻辑，服务启动模块负责应用启动，批量处理模块处理定时任务。

**包结构说明**：
各模块遵循标准包命名规范，非sdk模块包名为com.anytech.anytxn.authorization.模块名，sdk模块包名为com.anytech.anytxn.authorization。

**关键目录介绍**：

```
.
├── anytxn-authorization-base        # 基础定义模块 (可被其他服务依赖)
│   └── com.anytech.anytxn.authorization.base
│       ├── constants/               # 常量定义
│       ├── domain/                  # 领域对象 (BO、DTO、Model)
│       ├── enums/                   # 枚举类定义
│       ├── exception/               # 异常类定义
│       ├── service/                 # 服务接口定义
│       └── utils/                   # 工具类
├── anytxn-authorization-client      # 客户端模块 (Feign接口定义)
│   └── com.anytech.anytxn.authorization.client
├── anytxn-authorization-sdk         # 业务实现模块 (核心业务逻辑)
│   └── com.anytech.anytxn.authorization
│       ├── controller/              # REST控制器 (22个授权控制器)
│       ├── mapper/                  # 数据访问层
│       └── service/                 # 服务实现类
├── anytxn-authorization-server      # 服务启动模块
│   └── com.anytech.anytxn.authorization.server
│       └── AuthorizationServerApplication.java  # 主启动类
├── anytxn-authorization-batch       # 批量处理模块 (定时任务)
│   └── com.anytech.anytxn.authorization.batch
├── code-update-record/              # 代码更新记录
└── pom.xml                          # 父 POM
```

- `anytxn-authorization-base`: 定义了对外暴露的API接口、DTO和业务常量，可以被其他微服务依赖，避免了代码重复。
- `anytxn-authorization-client`: 定义了Feign客户端接口，供其他服务调用授权功能。
- `anytxn-authorization-sdk`: 包含所有业务逻辑、Controller、Service等，是核心业务实现模块。
- `anytxn-authorization-server`: 微服务启动入口，包含Spring Boot应用配置。
- `anytxn-authorization-batch`: 批量处理模块，包含定时任务和批量作业实现。

## 5. 配置 (Configuration)

本项目使用 Nacos 作为配置中心。核心配置文件在 Nacos 中，`group` 为 `DEFAULT_GROUP`，`Data ID` 为 `anytxn-authorization-server-dev.yaml`。

本地开发时，会优先加载 `src/main/resources/application.yaml` 中的配置。

**关键环境变量/配置项说明:**
| 变量名/属性名 | 示例值 | 描述 |
|---------------|--------|------|
| `NACOS_SERVER_ADDR` | `************:8848` | Nacos 服务器地址 |
| `SPRING_DATASOURCE_URL` | `************************************************` | 数据库连接地址 |
| `ROCKETMQ_NAME_SERVER` | `***********:9876` | RocketMQ 服务器地址 |
| `REDIS_HOST` | `localhost` | Redis 主机名 |
| `SERVER_PORT` | `18087` | 服务端口号 |

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建
mvn clean install

# 跳过测试构建
mvn clean install -DskipTests

# 单模块构建
mvn clean install -pl anytxn-authorization-server -am
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包
4. Docker镜像构建
5. 推送至Harbor镜像仓库
6. Kubernetes集群部署

**Docker部署方式**：
```bash
# 构建Docker镜像
mvn clean package docker:build

# 运行Docker容器
docker run -p 18087:18087 -e SPRING_PROFILES_ACTIVE=dev anytxn-authorization-server:1.0.2-SNAPSHOT
```

**环境变量配置**：
- `SPRING_PROFILES_ACTIVE`: 指定运行环境（dev/sit/prod）
- `NACOS_SERVER_ADDR`: Nacos服务地址
- `MYSQL_HOST`: MySQL数据库主机
- `REDIS_HOST`: Redis缓存主机
- `ROCKETMQ_NAME_SERVER`: RocketMQ服务地址

## 7. 监控与告警 (Monitoring & Alerting)

- **健康检查**: Spring Boot Actuator 提供了健康检查端点。
  - `http://localhost:18087/actuator/health`
- **Metrics**: 指标已接入 Prometheus。端点地址：
  - `http://localhost:18087/actuator/prometheus`
- **日志**: 应用日志格式为 JSON，统一由 Plumelog 收集并发送到 Elasticsearch。你可以在 Kibana 中查看。
  - **Kibana Dashboard**: [链接到 Kibana 仪表盘](https://kibana.jrx.com)