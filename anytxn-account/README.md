# AnyTXN信用卡核心系统-账户服务模块 (AnyTXN Account Service)

本项目是 AnyTXN信用卡核心系统 的账户服务模块，负责处理账户管理、自动扣款、债务管理、批量处理等核心业务。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-账户服务模块 (AnyTXN Account Service)](#anytxn信用卡核心系统-账户服务模块-anytxn-account-service)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build \& Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring \& Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的账户服务模块是企业级微服务应用，在整个AnyTXN生态系统中负责账户生命周期管理，为卡片服务、交易服务、客户服务等其他业务模块提供账户基础服务能力。该服务解决了账户信息管理、自动扣款处理、债务管理、账单统计、批量作业调度等关键业务问题。

主要功能包括：账户忠诚度管理、账户可选信息管理、账单信息处理、账户统计分析、自动扣款签约和历史管理、客户债务处理、超额分配管理、完善的批量作业体系，支持多租户数据隔离和高并发处理。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于API接口和批量作业分析，anytxn-account工程提供以下五大核心功能模块：

#### 1.1.1 账户管理功能 (Account Management)

**核心API接口：**
- `PUT /account/management/batch` - 批量更新管理账户信息接口，支持批处理业务更新
- `PUT /account/management` - 联机更新管理账户信息接口，CMS前端使用
- `GET /account/managements/{customerId}` - 根据客户号查询管理账户接口
- `GET /account/management/findByExternalReferenceNumber` - 根据扩展参考号查询管理账户接口
- `GET /account/managements` - 根据查询类型（卡号C/证件号I/管理账号M）查询管理账户接口

**主要服务类：**
- `IAccountManageInfoService` - 账户管理服务，提供账户查询、更新、创建等核心功能
- `IAccountStatisticsInfoService` - 账户统计服务，处理账户统计分析和报表生成
- `IAccountStatementInfoService` - 账单信息服务，管理账户账单信息

**核心业务能力：**
- 支持根据客户号、卡号、证件号、管理账号等多维度查询账户信息
- 提供批量和联机两种账户信息更新模式
- 支持账户状态管理（新户、静止、活跃）和账户产品关联

#### 1.1.2 自动扣款功能 (Auto Payment)

**核心API接口：**
- `POST /account/autoPayment` - 根据搜索条件分页查询约定扣款流水接口
- `GET /account/autoPayment/autoPaymentId/{autoPaymentId}` - 根据约定扣款ID查询流水明细接口

**主要服务类：**
- `IAutoPaymentLogService` - 约定扣款服务，处理自动扣款流水记录和查询
- `IAutoPaymentLogHistoryService` - 约定扣款历史服务，管理历史扣款记录
- `AutoEbitSignUpInforService` - 自动扣款签约服务，处理扣款签约信息

**批量作业：**
- `autopayment` - 约定扣款批量处理作业，执行定期自动扣款
- `newautopay` - 新自动扣款作业，支持新的扣款业务模式
- `paymentfile` - 扣款文件处理作业，生成和处理扣款相关文件

**核心业务能力：**
- 支持约定扣款签约管理和自动执行
- 提供扣款流水查询和历史记录管理
- 支持扣款文件生成和银行对接

#### 1.1.3 账户忠诚度管理功能 (Account Loyalty Management)

**核心API接口：**
- 账户忠诚度相关API（通过AccountLoyaltyInfoController提供）

**主要服务类：**
- `IAccountLoyaltyInfoService` - 账户忠诚度服务，管理客户忠诚度积分和等级
- `IAccountOptinalInfoService` - 账户可选信息服务，处理账户扩展属性信息

**核心业务能力：**
- 管理客户忠诚度积分累积和消费
- 支持客户等级评定和权益管理
- 提供账户可选信息的灵活配置

#### 1.1.4 债务管理功能 (Customer Debt Management)

**核心API接口：**
- 客户债务查询API（通过CustomerDebtController提供）

**主要服务类：**
- `ICustomerDebtService` - 客户债务服务，支持多维度债务信息查询
- `IOverpayAllocationService` - 超额分配服务，处理客户超额还款的分配逻辑

**批量作业：**
- `incollection` - 债务催收批量作业，处理逾期账户的催收业务
- `overpayallocation` - 超额分配批量作业，自动分配客户超额还款

**核心业务能力：**
- 支持根据客户号、证件号、EcIf号等查询客户债务信息
- 提供超额还款的智能分配机制
- 支持债务催收和逾期管理

#### 1.1.5 购汇与外币处理功能 (Foreign Exchange Management)

**主要服务类：**
- 账户管理服务中的购汇相关方法（`batchProcess4ExchangePayment`等）

**批量作业：**
- `exchange` - 购汇批量处理作业，执行外币购买和汇率处理
- `stamptaxsumlimit` - 印花税限额管理作业，处理税务相关计算

**核心业务能力：**
- 支持外币账户的购汇业务处理
- 提供汇率转换和外币结算功能
- 支持印花税等税务处理

#### 1.1.6 文件处理与对账功能 (File Processing & Reconciliation)

**批量作业：**
- `paymentfile` - 支付文件处理作业群，包含自动扣款和还款文件处理
- `errorfile` - 错误文件处理作业，处理业务异常和数据修正
- `brandservicefee` - 品牌服务费处理作业，计算和收取品牌相关费用
- `egprepayschedule` - EGP预付计划作业，处理预付费业务

**技术特色：**
- **高性能批处理**：采用Spring Batch框架，支持分区处理和并行执行
- **文件处理能力**：支持多种格式文件的生成、解析和处理
- **对账机制**：完整的文件对账和异常处理机制
- **分布式处理**：支持集群环境下的分布式批量处理
- **监控告警**：集成批处理监控和异常告警机制

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：MySQL 8.3.0, Redis（通过Redisson 3.41.0集成）

**中间件**：Nacos（服务发现与配置中心）, RocketMQ

**构建工具**：Maven 3.5+

**JDK版本要求**：Java 17

**其他核心组件**：
- MyBatis 3.5.16（数据持久化）
- MyBatis Spring Boot 3.0.4
- Undertow（Web服务器）
- ShardingSphere 5.5.0（分库分表）
- Spring Batch（批量处理）
- Jasypt 3.0.5（参数加解密）
- Plumelog 3.1.0-SNAPSHOT（分布式日志）
- Micrometer + Prometheus（监控）

_图 1: 账户服务交互图_

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**Docker**：Docker & Docker Compose

**中间件要求**：
- Nacos Server
- RocketMQ Server
- MySQL 8.3.0+
- Redis 6.2+

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-account.git
cd anytxn-account
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：配置 IntelliJ IDEA 启动参数**

在 IntelliJ IDEA 中配置应用启动的程序参数：

1. **打开运行配置**
   - 在 IDEA 中找到主启动类（如 `AnytxnAccountingServerApplication.java`）
   - 右键点击该文件，选择 `Run` 或 `Debug`
   - 或者点击 IDEA 顶部的运行配置下拉菜单

2. **编辑运行配置**
   - 点击运行配置旁的编辑按钮（齿轮图标）
   - 或者选择 `Run` → `Edit Configurations...`

3. **配置程序参数**
   - 在 `Program arguments` 字段中输入以下参数：
   ```
   --NACOS_USERNAME=nacos --NACOS_PASSWORD=nacos --CLUSTER_NAME=A --NACOS_SERVER_ADDR=**********:8848,**********:8848,**********:8848 --NACOS_NAMESPACE=2cff4a1a-e0f9-42e4-8472-a7b0a17a1235
   ```
   
   > **注意**：请根据实际环境情况调整以下参数值：
   > - `NACOS_USERNAME`: Nacos 用户名
   > - `NACOS_PASSWORD`: Nacos 密码  
   > - `CLUSTER_NAME`: 集群名称
   > - `NACOS_SERVER_ADDR`: Nacos 服务器地址列表
   > - `NACOS_NAMESPACE`: Nacos 命名空间ID

4. **保存配置**
   - 点击 `Apply` 和 `OK` 保存配置

**第五步：运行应用**

我们推荐在 IntelliJ IDEA 中直接运行 `AnytxnAccountingServerApplication.java`。

或者通过命令行启动，指定 `dev` 环境配置：

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

服务启动后，你可以访问 [http://localhost:18084](http://localhost:18084) 来检查服务状态。

## 4. 项目结构 (Project Structure)

**多模块结构说明**：
项目采用Maven多模块架构，按照业务功能和技术层次进行模块划分，实现清晰的依赖关系和职责分离。

**各模块职责划分**：
基础定义模块提供API接口和基础组件，业务实现模块包含核心逻辑，服务启动模块负责应用启动，批量处理模块处理定时任务，客户端模块对外暴露接口。

**包结构说明**：
各模块遵循标准包命名规范，非sdk模块包名为com.anytech.anytxn.account.模块名，sdk模块包名为com.anytech.anytxn.account。

**关键目录介绍**：

```
.
├── anytxn-account-base       # 基础定义模块 (可被其他服务依赖)
│   └── com.anytech.anytxn.account.base
│       ├── constants/        # 常量类定义 (10个常量类)
│       ├── domain/           # 领域对象 (BO、DTO)
│       ├── enums/            # 枚举类定义 (7个枚举)
│       ├── exception/        # 异常类定义
│       ├── service/          # 服务接口定义 (10个接口)
│       └── utils/            # 工具类 (5个工具类)
├── anytxn-account-sdk        # 业务实现模块 (12个服务实现类)
│   └── com.anytech.anytxn.account
│       ├── config/           # 配置类
│       ├── controller/       # REST控制器 (9个控制器)
│       └── service/          # 服务实现类
├── anytxn-account-server     # 服务启动模块
│   └── com.anytech.anytxn.account.server
│       ├── AnytxnAccountingServerApplication.java  # 主启动类
│       └── config/           # 服务器配置
├── anytxn-account-batch      # 批量处理模块 (18个批量作业)
│   └── com.anytech.anytxn.account
│       ├── config/           # 批处理配置
│       └── job/              # 批处理作业
├── anytxn-account-client     # Feign客户端模块
│   └── com.anytech.anytxn.account.client
│       └── fallback/         # 降级处理
├── doc                       # 项目文档
└── pom.xml                   # 父 POM
```

- `anytxn-account-base`: 定义了对外暴露的API接口、DTO和业务常量，可以被其他微服务依赖，避免了代码重复。
- `anytxn-account-sdk`: 包含所有业务逻辑、Controller、Service等，是核心业务实现模块。
- `anytxn-account-server`: 微服务启动入口，包含Spring Boot应用配置。
- `anytxn-account-batch`: 批量处理模块，包含批处理任务实现。
- `anytxn-account-client`: 为其他微服务提供Feign客户端接口。

## 5. 配置 (Configuration)

本项目使用 Nacos 作为配置中心。核心配置文件在 Nacos 中，`group` 为 `ACCOUNT_GROUP`，`Data ID` 为 `anytxn-account-server-dev.yaml`。

本地开发时，会优先加载 `src/main/resources/application.yaml` 中的配置。

**关键环境变量/配置项说明:**
| 变量名/属性名 | 示例值 | 描述 |
|---------------|--------|------|
| `NACOS_SERVER_ADDR` | `************:8848` | Nacos 服务器地址 |
| `SPRING_DATASOURCE_URL` | `******************************************` | 数据库连接地址 |
| `ROCKETMQ_NAME_SERVER` | `***********:9876` | RocketMQ 服务器地址 |
| `REDIS_HOST` | `localhost` | Redis 主机名 |
| `SERVER_PORT` | `18084` | 服务端口号 |

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建
mvn clean install

# 跳过测试构建
mvn clean install -DskipTests

# 单模块构建
mvn clean install -pl anytxn-account-server -am
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包
4. Docker镜像构建
5. 推送至Harbor镜像仓库
6. Kubernetes集群部署

**Docker部署方式**：
```bash
# 构建Docker镜像
mvn clean package docker:build

# 运行Docker容器
docker run -p 18084:18084 -e SPRING_PROFILES_ACTIVE=dev anytxn-account-server:1.0.2-SNAPSHOT
```

**环境变量配置**：
- `SPRING_PROFILES_ACTIVE`: 指定运行环境（dev/sit/prod）
- `NACOS_SERVER_ADDR`: Nacos服务地址
- `MYSQL_HOST`: MySQL数据库主机
- `REDIS_HOST`: Redis缓存主机
- `ROCKETMQ_NAME_SERVER`: RocketMQ服务地址

## 7. 监控与告警 (Monitoring & Alerting)

- **健康检查**: Spring Boot Actuator 提供了健康检查端点。
  - `http://localhost:18084/actuator/health`
- **Metrics**: 指标已接入 Prometheus。端点地址：
  - `http://localhost:18084/actuator/prometheus`
- **日志**: 应用日志格式为 JSON，统一由 Plumelog 收集并发送到 Elasticsearch。你可以在 Kibana 中查看。
  - **Kibana Dashboard**: [链接到 Kibana 仪表盘](https://kibana.jrx.com)