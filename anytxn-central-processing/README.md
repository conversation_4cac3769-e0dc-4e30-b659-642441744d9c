# AnyTXN信用卡核心系统-中央处理服务 (AnyTXN Central Processing Service)

本项目是 AnyTXN信用卡核心系统 的中央处理服务，负责处理卡号池管理、客户信息管理、卡片开户、会计查询、交易处理、清算服务等核心业务。

---

## 目录 (Table of Contents)

- [AnyTXN信用卡核心系统-中央处理服务 (AnyTXN Central Processing Service)](#anytxn信用卡核心系统-中央处理服务-anytxn-central-processing-service)
  - [目录 (Table of Contents)](#目录-table-of-contents)
  - [1. 概述 (Overview)](#1-概述-overview)
    - [1.1. 核心功能详解(Detailed Explanation of Core Features)](#11-核心功能详解detailed-explanation-of-core-features)
  - [2. 架构 (Architecture)](#2-架构-architecture)
    - [2.1. 技术栈 (Technology Stack)](#21-技术栈-technology-stack)
  - [3. 快速开始 (Getting Started)](#3-快速开始-getting-started)
    - [3.1. 环境要求 (Prerequisites)](#31-环境要求-prerequisites)
    - [3.2. 本地构建与运行 (Build \& Run Locally)](#32-本地构建与运行-build--run-locally)
  - [4. 项目结构 (Project Structure)](#4-项目结构-project-structure)
  - [5. 配置 (Configuration)](#5-配置-configuration)
  - [6. 部署 (Deployment)](#6-部署-deployment)
  - [7. 监控与告警 (Monitoring \& Alerting)](#7-监控与告警-monitoring--alerting)

---

## 1. 概述 (Overview)

AnyTXN信用卡核心系统的中央处理服务是企业级微服务应用，在整个AnyTXN生态系统中负责卡核心公共区服务处理，为卡片服务、账户服务、授权服务、交易服务等其他业务模块提供中央化的核心业务处理能力。该服务解决了卡号池统一管理、客户信息集中维护、卡片开户流程标准化、会计账务集中查询、交易批量处理、国际卡组织清算等关键业务问题。

主要功能包括：卡号池和卡号锁定管理、个人和企业客户信息维护、单笔和批量卡片开户处理、会计报表和总账查询、交易批量录入和多记录处理、物料消耗管理、MasterCard等国际卡组织清算服务（包含30+个清算相关服务），支持多租户数据隔离和分布式事务处理。

### 1.1. 核心功能详解(Detailed Explanation of Core Features)

基于API接口、服务实现和批量作业分析，anytxn-central-processing工程提供以下八大核心功能模块：

#### 1.1.1 卡号池统一管理功能 (Card Pool Management)

**核心API接口：**
- `GET /anytxn/v2/api/cm/cardPool` - 根据条件分页查询卡号池
- `GET /anytxn/v2/api/cm/cardPool/queryOne` - 根据BinTableId查询单个卡池信息

**卡号锁定管理API：**
- `POST /anytxn/v2/api/cm/cardLock/cardNumbers` - 根据机构号、卡产品获取自选卡号
- `POST /anytxn/v2/api/cm/cardLock` - 新增卡号锁定
- `POST /anytxn/v2/api/cm/removeCardLock` - 解除卡号锁定
- `POST /anytxn/v2/api/cm/cardLock/release` - 从卡号池释放卡号
- `DELETE /anytxn/v2/api/cm/cardLock/{idType}/{idNumber}/{cardNumber}` - 删除卡号锁定数据
- `GET /anytxn/v2/api/cm/cardLock/` - 分页查询卡号锁定数据

**卡号生成与校验API：**
- `GET /anytxn/v2/api/cm/cardLastNum/{cardSequence}` - 生成卡号校验位（Luhn算法）
- `GET /anytxn/v2/api/cm/cardRange/cardProduct/{cardProduct}` - 获取可填卡序号范围
- `POST /anytxn/v2/api/cm/cardLock/optional` - 新增自选靓号
- `GET /anytxn/v2/api/cm/cardLock/goldenNumberSelect/{productNumber}/{last4Digits}` - 根据后4位确定可选靓号

**批量处理作业：**
- `CardPoolConfig` - 卡号池批量处理配置，包含CardPoolProcessor、CardPoolReader、CardPoolWriter
- `OptionalCardConfig` - 自选卡批量处理配置

**核心业务能力：**
- 统一管理信用卡号资源池，支持多产品多机构的卡号分配
- 提供卡号锁定机制，防止卡号重复使用和并发冲突
- 支持自选卡号和靓号管理，提供个性化卡号服务
- 实现基于Luhn算法的卡号校验位自动生成
- 支持卡号范围管理和序号分配规则配置

#### 1.1.2 卡片开户处理功能 (Card Opening Processing)

**单笔开卡API接口：**
- `POST /anytxn/v2/api/cm/card/open` - 申卡接口（CMS页面使用，包含额度节点）
- `POST /anytxn/v2/api/cm/card/openByNasOnline` - NAS在线开卡接口（外围系统调用）
- `POST /anytxn/v2/api/cm/card/openCardCheck` - 开卡前置检查

**批量开卡API接口：**
- `POST /anytxn/v2/api/cm/card/batchOpen` - 通过文件名批量开卡
- `POST /anytxn/v2/api/cm/card/batch/open` - 批量开卡处理
- `POST /anytxn/v2/api/cm/card/batch/open/upload` - 上传文件批量开卡
- `POST /anytxn/v2/api/cm/card/batchOpenCard` - 批量开卡（传入DTO列表）

**企业卡开户API：**
- `POST /anytxn/v2/api/cm/cardholder/corporateCustomerInfo` - 添加企业客户基本信息
- `POST /anytxn/v2/api/cm/cardholder/corporateRegistrationInfo` - 添加公司注册信息

**批量处理作业：**
- `CardOpenConfig` - 开卡批量处理配置
- `NasFileOpenCardConfig` - NAS文件开卡处理
- `NasFileProcessConfig` - NAS文件处理，包含NasApplicationProcessor、NasApplicationWriter

**核心业务能力：**
- 支持单笔和批量卡片开户处理，满足不同业务场景需求
- 提供完整的开卡前置检查和业务规则验证
- 支持个人卡和企业卡的差异化开户流程
- 实现多渠道开卡接入（CMS、NAS、外部系统）
- 提供文件上传批量开卡能力，支持大批量卡片处理

#### 1.1.3 会计查询与账务处理功能 (Accounting Query & Processing)

**会计查询API接口：**
- `GET /cm/accountant/balchk` - 根据日期查询总分核对表
- `GET /cm/accountant/acgd` - 根据日期查询日总账（日科目余额）
- `GET /cm/accountant/acgm` - 根据日期查询月总账（月科目余额）
- `GET /cm/accountant/acgy` - 根据日期查询年总账（年科目余额）

**总账调账处理API：**
- `GET /cm/accountant/voucheradj/{no}` - 根据ID查询调账数据
- `GET /cm/accountant/voucheradj` - 分页查询调账数据
- `POST /cm/accountant/voucheradj/add` - 新增调账数据
- `PUT /cm/accountant/voucheradj/update` - 调整调账数据
- `DELETE /cm/accountant/voucheradj/{no}` - 删除调账数据

**批量处理作业：**
- `AccountsCheckConfig` - 总分核对批量处理，包含AccountCheckProcessor、AccountCheckReader、AccountCheckWriter
- `GlVcherAdjConfig` - 总账调账批量处理
- `GlvcherSumJobConfig` - 总账汇总作业，包含GlvcherSumProcessor、GlvcherSumReader、GlvcherSumWriter
- `GeneLedgerConfig` - 总账生成配置，包含LedgerProcessor、LedgerReader、LedgerWriter
- `YearEndConfig` - 年终处理配置，包含YearEndProcessor、YearEndReader、YearEndWriter
- `UsdTaxToVoucherConfig` - 美元税费转凭证处理
- `CmDataCleanConfig` - 会计数据清理作业

**核心业务能力：**
- 提供完整的会计账务查询功能，支持日、月、年维度的科目余额查询
- 实现总分核对功能，确保会计数据的准确性和一致性
- 支持总账调账处理，提供会计数据修正能力
- 提供批量会计数据处理和汇总统计功能
- 支持年终处理和数据清理等定期会计作业

#### 1.1.4 交易批量录入处理功能 (Transaction Batch Entry Processing)

**交易录入API接口：**
- `POST /anytxn/v2/api/cm/transaction/multirecord` - 交易批量录入接口

**核心服务实现：**
- `TransactionCentralRecordedService.batchTransactionEntry()` - 批量交易录入处理服务

**核心业务能力：**
- 支持多记录交易数据的批量录入处理
- 提供交易数据的集中化处理和验证
- 实现交易记录的批量入账和状态管理
- 支持大批量交易数据的高效处理

#### 1.1.5 客户信息管理功能 (Customer Information Management)

**客户查询API接口：**
- `GET /anytxn/v2/api/cm/cardholder/getCustomerIdByEcif` - 根据ECIF号获取客户ID

**企业客户管理API：**
- `POST /anytxn/v2/api/cm/cardholder/corporateCustomerInfo` - 添加企业客户基本信息
- `POST /anytxn/v2/api/cm/cardholder/corporateRegistrationInfo` - 添加公司注册信息

**核心服务实现：**
- `CorporateService` - 企业客户服务，提供企业客户信息管理
- `MappingFeign` - 客户映射服务，支持ECIF号到客户ID的映射

**核心业务能力：**
- 提供个人和企业客户信息的统一管理
- 支持客户身份识别和映射关系维护
- 实现多渠道客户信息同步和更新
- 支持企业客户的注册信息管理

#### 1.1.6 物料消耗管理功能 (Material Consumption Management)

**物料查询API接口：**
- `GET /cm/card/findMaterialConsumeInfo/page` - 分页查询物料消耗反馈信息
- `GET /cm/card/findMaterialConsumeInfoById/id/{id}` - 根据ID查询物料消耗反馈信息

**批量处理作业：**
- `ResolveMaterialConsumerBathConfig` - 物料消耗批量处理配置
- `ResolveMaterialOtherBathConfig` - 其他物料批量处理配置
- 包含ResloveMaterialConsumerReader、ResolveMaterialConsumerProcessor、ResolveMaterialConsumerWriter等处理组件

**核心业务能力：**
- 提供卡片制作过程中的物料消耗跟踪和管理
- 支持物料消耗数据的批量处理和反馈
- 实现物料使用情况的统计和报告
- 支持物料成本核算和消耗分析

#### 1.1.7 国际卡组织清算服务功能 (International Card Organization Settlement)

**清算处理服务（30+个服务类）：**

**MasterCard清算核心服务：**
- `MastercardIncomingRecordService` - MasterCard来文记录处理
- `MastercardOutgoingService` - MasterCard去文处理
- `MastercardRecordProcessService` - MasterCard记录处理
- `MastercardFinancialDetailService` - MasterCard财务明细处理
- `MastercardReconciliationMessageService` - MasterCard对账消息处理

**MasterCard文件生成服务：**
- `MastercardHeaderGenerateService` - MasterCard文件头生成
- `MastercardTrailerGenerateService` - MasterCard文件尾生成
- `MastercardPdsGenerateService` - MasterCard PDS生成
- `MastercardEmvGenerateService` - MasterCard EMV生成
- `MastercardRetrievalGenerateService` - MasterCard检索生成

**MasterCard业务处理服务：**
- `MastercardChargeBackGenerateService` - MasterCard拒付生成
- `MastercardFeeCollectionGenerateService` - MasterCard费用收集生成
- `MastercardExchangeRateHandleService` - MasterCard汇率处理
- `MastercardEmvAnalyzeService` - MasterCard EMV分析

**系统控制和公共服务：**
- `SystemControlService` - 系统控制服务
- `ProcessRerunService` - 流程重跑服务
- `InterchangeBatchSequenceService` - 交换批次序号服务
- `SettleOriginLogService` - 清算原始日志服务

**批量处理作业：**
- `MastercardIncomingRecordConfig` - MasterCard来文记录批量处理
- `MastercardOutgoingJobConfiguration` - MasterCard去文作业配置
- `MastercardRecordProcessJobConfiguration` - MasterCard记录处理作业
- `PublicVisaFileProcessConfig` - Visa文件处理配置
- `PublicJcbFileProcessConfig` - JCB文件处理配置

**核心业务能力：**
- 支持MasterCard、Visa、JCB等主要国际卡组织的清算处理
- 提供完整的清算文件处理流程（来文解析、记录处理、去文生成）
- 实现清算数据的自动对账和差错处理
- 支持多种清算消息格式和业务类型处理
- 提供清算费用计算和汇率处理功能

#### 1.1.8 批量处理与定时任务功能 (Batch Processing & Scheduled Jobs)

**会计批量处理作业：**
- 总分核对处理、总账汇总、科目余额生成、年终处理、数据清理

**卡片批量处理作业：**
- 卡片制作结果处理、卡片退回处理、DP结果文件合并、卡函合并、NAS文件处理

**清算批量处理作业：**
- MasterCard来文处理、去文生成、文件处理、数据对账、错误报告生成
- Visa文件处理、JCB文件处理、清算数据备份和清理

**对账匹配批量处理：**
- CUP对账匹配（S1文件读取、S2发卡行、S3比较、S4报告）
- EPCC对账匹配（文件读取、发卡行读取、数据比较、报告生成）
- UPI对账匹配处理

**其他批量处理作业：**
- 汇率同步和更新、品牌服务费处理、物料消耗处理、自选卡处理

**核心业务能力：**
- 提供完整的Spring Batch批量处理框架支持
- 支持多种批量作业的并行执行和调度管理
- 实现大数据量的高效批量处理能力
- 提供批量作业的监控、重跑和异常处理机制
- 支持定时任务和事件驱动的批量作业执行

**技术特色：**
- **PostgreSQL主数据库**：采用PostgreSQL作为主要数据库，支持复杂查询和大数据量处理
- **多数据库支持**：同时支持PostgreSQL和MySQL，实现数据源的灵活配置
- **Spring Batch框架**：基于Spring Batch实现企业级批量处理能力
- **国际卡组织集成**：深度集成MasterCard、Visa、JCB等国际卡组织清算系统
- **分布式处理**：支持分布式批量处理和多租户数据隔离
- **高可用设计**：提供完整的批量作业监控、重跑和异常恢复机制

## 2. 架构 (Architecture)

### 2.1. 技术栈 (Technology Stack)

**核心框架版本**：Spring Boot 3.4.2, Spring Cloud 2024.0.0, Spring Cloud Alibaba 2023.0.3.2

**数据库**：PostgreSQL（主要）, MySQL 8.3.0（业务数据）, Redis（通过Redisson 3.41.0集成）

**中间件**：Nacos（服务发现与配置中心）

**构建工具**：Maven 3.5

**JDK版本要求**：Java 17

**其他核心组件**：
- MyBatis 3.5.16（数据持久化）
- MyBatis Spring Boot 3.0.4
- Undertow（Web服务器，替代Tomcat）
- ShardingSphere 5.5.0（分库分表）
- Druid 1.2.24（数据库连接池）
- Spring Batch（批量处理）
- Jasypt 3.0.5（参数加解密）
- OpenFeign + OkHttp（服务间调用）
- Plumelog 3.1.0-SNAPSHOT（分布式日志）
- Micrometer + Prometheus（监控）

## 3. 快速开始 (Getting Started)

### 3.1. 环境要求 (Prerequisites)

**JDK**：Java 17+

**Maven**：Maven 3.5+

**Docker**：Docker & Docker Compose

**中间件要求**：
- Nacos Server
- PostgreSQL 12+
- MySQL 8.3.0+
- Redis 6.2+

### 3.2. 本地构建与运行 (Build & Run Locally)

**第一步：克隆项目**

```bash
git clone http://10.0.11.44:1000/AnyTXN-Product/anytxn-central-processing.git
cd anytxn-central-processing
```

**第二步：配置 IntelliJ IDEA 开发环境**

在 IntelliJ IDEA 中打开项目并配置开发环境：

1. **导入项目**
   - 打开 IntelliJ IDEA
   - 选择 `File` → `Open` 或 `Import Project`
   - 选择项目根目录，IDEA 会自动识别为 Maven 项目

2. **配置 JDK**
   - 打开 `File` → `Project Structure` → `Project`
   - 设置 `Project SDK` 为 Java 17（根据项目要求选择对应版本）
   - 设置 `Project Language Level` 为对应的 Java 版本

3. **配置 Maven**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Build Tools` → `Maven`
   - 设置 `Maven home path` 为本地 Maven 安装目录
   - 设置 `User settings file` 和 `Local repository` 路径
   - 确保 `Import Maven projects automatically` 选项已勾选

4. **刷新 Maven 依赖**
   - 在 IDEA 右侧 Maven 工具窗口点击刷新按钮
   - 或使用快捷键 `Ctrl+Shift+O`（Windows/Linux）或 `Cmd+Shift+I`（macOS）

**第三步：构建项目**

```bash
mvn clean install -DskipTests
```

**第四步：配置 IntelliJ IDEA 启动参数**

在 IntelliJ IDEA 中配置应用启动的程序参数：

1. **打开运行配置**
   - 在 IDEA 中找到主启动类（如 `CentralProcessingServerApplication.java`）
   - 右键点击该文件，选择 `Run` 或 `Debug`
   - 或者点击 IDEA 顶部的运行配置下拉菜单

2. **编辑运行配置**
   - 点击运行配置旁的编辑按钮（齿轮图标）
   - 或者选择 `Run` → `Edit Configurations...`

3. **配置程序参数**
   - 在 `Program arguments` 字段中输入以下参数：
   ```
   --NACOS_USERNAME=nacos --NACOS_PASSWORD=nacos --CLUSTER_NAME=A --NACOS_SERVER_ADDR=**********:8848,**********:8848,**********:8848 --NACOS_NAMESPACE=2cff4a1a-e0f9-42e4-8472-a7b0a17a1235
   ```
   
   > **注意**：请根据实际环境情况调整以下参数值：
   > - `NACOS_USERNAME`: Nacos 用户名
   > - `NACOS_PASSWORD`: Nacos 密码  
   > - `CLUSTER_NAME`: 集群名称
   > - `NACOS_SERVER_ADDR`: Nacos 服务器地址列表
   > - `NACOS_NAMESPACE`: Nacos 命名空间ID

4. **保存配置**
   - 点击 `Apply` 和 `OK` 保存配置

**第五步：运行应用**

我们推荐在 IntelliJ IDEA 中直接运行 `CentralProcessingServerApplication.java`。

或者通过命令行启动，指定 `dev` 环境配置：

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

服务启动后，你可以访问 [http://localhost:18091](http://localhost:18091) 来检查服务状态。

## 4. 项目结构 (Project Structure)

**多模块结构说明**：
项目采用Maven多模块架构，按照业务功能和技术层次进行模块划分，实现清晰的依赖关系和职责分离。

**各模块职责划分**：
基础定义模块提供API接口和基础组件，客户端模块提供Feign接口，业务实现模块包含核心逻辑，服务启动模块负责应用启动，批量处理模块处理定时任务。

**包结构说明**：
各模块遵循标准包命名规范，基础包名为com.anytech.anytxn.central.base，数据访问包名为com.anytech.anytxn.central，客户端包名为com.anytech.anytxn.central.client。

**关键目录介绍**：

```
.
├── anytxn-central-processing-base        # 基础定义模块 (可被其他服务依赖)
│   └── com.anytech.anytxn.central.base
│       ├── annotation/                   # 注解定义
│       ├── constants/                    # 常量类集合 (30+个常量类)
│       ├── domain/                       # 领域对象
│       ├── enums/                        # 枚举类集合 (40+个枚举)
│       ├── exception/                    # 异常定义
│       └── utils/                        # 工具类集合 (25+个工具类)
├── anytxn-central-processing-client      # 客户端模块 (Feign接口定义)
│   └── com.anytech.anytxn.central.client
│       ├── card/                         # 卡片相关Feign接口
│       ├── companycard/                  # 企业卡相关Feign接口
│       ├── mapping/                      # 映射相关Feign接口
│       └── transaction/                  # 交易相关Feign接口
├── anytxn-central-processing-sdk         # 业务实现模块 (核心业务逻辑)
│   └── com.anytech.anytxn.central
│       ├── controller/                   # REST控制器 (11个)
│       ├── mapper/                       # MyBatis映射器 (33个)
│       ├── service/                      # 业务服务实现 (50个)
│       │   ├── accountant/               # 会计服务 (7个)
│       │   ├── card/                     # 卡片服务 (7个)
│       │   └── settle/                   # 清算服务 (30+个)
│       └── config/                       # 配置类
├── anytxn-central-processing-server      # 服务启动模块
│   └── com.anytech.anytxn.central.server
│       ├── CentralProcessingServerApplication.java  # 主启动类
│       └── application.yaml              # 配置文件
├── anytxn-central-processing-batch       # 批量处理模块 (定时任务)
│   └── com.anytech.anytxn.central.batch
│       └── CentralProcessingBatchApplication.java   # 批处理启动类
├── doc/                                  # 项目文档
└── pom.xml                               # 父 POM
```

- `anytxn-central-processing-base`: 定义了对外暴露的API接口、DTO和业务常量，可以被其他微服务依赖，避免了代码重复。
- `anytxn-central-processing-client`: 定义了Feign客户端接口，供其他服务调用中央处理功能。
- `anytxn-central-processing-sdk`: 包含所有业务逻辑、Controller、Service等，是核心业务实现模块。
- `anytxn-central-processing-server`: 微服务启动入口，包含Spring Boot应用配置。
- `anytxn-central-processing-batch`: 批量处理模块，包含定时任务和批量作业实现。

## 5. 配置 (Configuration)

本项目使用 Nacos 作为配置中心。核心配置文件在 Nacos 中，`group` 为 `DEFAULT_GROUP`，`Data ID` 为 `anytxn-central-processing-server-dev.yaml`。

本地开发时，会优先加载 `src/main/resources/application.yaml` 中的配置。

**关键环境变量/配置项说明:**
| 变量名/属性名 | 示例值 | 描述 |
|---------------|--------|------|
| `NACOS_SERVER_ADDR` | `************:8848` | Nacos 服务器地址 |
| `SPRING_DATASOURCE_URL` | `***********************************************` | PostgreSQL数据库连接地址 |
| `REDIS_HOST` | `localhost` | Redis 主机名 |
| `SERVER_PORT` | `18091` | 服务端口号 |
| `UNDERTOW_IO_THREADS` | `8` | Undertow IO线程数 |
| `UNDERTOW_WORKER_THREADS` | `256` | Undertow工作线程数 |

## 6. 部署 (Deployment)

**构建命令**：
```bash
# 完整构建
mvn clean install

# 跳过测试构建
mvn clean install -DskipTests

# 单模块构建
mvn clean install -pl anytxn-central-processing-server -am
```

**部署步骤**：
1. 代码提交到Git仓库
2. Jenkins Pipeline自动触发构建
3. Maven编译打包
4. Docker镜像构建
5. 推送至Harbor镜像仓库
6. Kubernetes集群部署

**Docker部署方式**：
```bash
# 构建Docker镜像
mvn clean package docker:build

# 运行Docker容器
docker run -p 18091:18091 -e SPRING_PROFILES_ACTIVE=dev k8s.jrx.com/multi-tenant-sgb/anytxn-central-processing-server:1.0.2-SNAPSHOT
```

**环境变量配置**：
- `SPRING_PROFILES_ACTIVE`: 指定运行环境（dev/sit/prod）
- `NACOS_SERVER_ADDR`: Nacos服务地址
- `POSTGRES_HOST`: PostgreSQL数据库主机
- `MYSQL_HOST`: MySQL数据库主机
- `REDIS_HOST`: Redis缓存主机

## 7. 监控与告警 (Monitoring & Alerting)

- **健康检查**: Spring Boot Actuator 提供了健康检查端点。
  - `http://localhost:18091/actuator/health`
- **Metrics**: 指标已接入 Prometheus。端点地址：
  - `http://localhost:18091/actuator/prometheus`
- **日志**: 应用日志格式为 JSON，统一由 Plumelog 收集并发送到 Elasticsearch。你可以在 Kibana 中查看。
  - **Kibana Dashboard**: [链接到 Kibana 仪表盘](https://kibana.jrx.com)